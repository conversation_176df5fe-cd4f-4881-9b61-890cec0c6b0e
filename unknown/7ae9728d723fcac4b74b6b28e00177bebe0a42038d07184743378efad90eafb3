<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="3.7.7.14" />
    <PackageReference Include="Experimental.System.Messaging" Version="1.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="System.Data.OleDb" Version="6.0.0" />
    <PackageReference Include="System.DirectoryServices" Version="5.0.0" />
    <PackageReference Include="System.DirectoryServices.Protocols" Version="5.0.1" />
    <PackageReference Include="System.ServiceModel.Web" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj" />
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
    <ProjectReference Include="..\MongoConfigProject\MongoConfigProject.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="RuleEngine\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="RuleEngine\" />
  </ItemGroup>
</Project>
