﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32929.385
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Allocation", "Allocation\Allocation.csproj", "{F5DF3E1C-7055-46AA-A24B-3B195B027732}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EmailCommunicationBLL", "EmailCommunicationBLL\EmailCommunicationBLL.csproj", "{64CAD865-EED4-44E5-B4EF-1906D6A713EE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataAccessLayer", "DataAccessLayer\DataAccessLayer.csproj", "{54290892-25C2-4A68-81C5-147EA7B1A2E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataHelper", "DataHelper\DataHelper.csproj", "{29A6322D-6F83-46AD-A302-BF28847E030E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ReadXmlProject", "ReadXmlProject\ReadXmlProject.csproj", "{D02CE903-39E4-41C3-A352-A642F25895C5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LoggingHelper", "LoggingHelper\LoggingHelper.csproj", "{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Helper", "Helper\Helper.csproj", "{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PropertyLayers", "PropertyLayers\PropertyLayers.csproj", "{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MongoConfigProject", "MongoConfigProject\MongoConfigProject.csproj", "{46A35195-F38B-4FC7-8F42-D7F696AD965D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kafka", "kafka\Kafka.csproj", "{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Debug|x86.Build.0 = Debug|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Release|x86.ActiveCfg = Release|Any CPU
		{F5DF3E1C-7055-46AA-A24B-3B195B027732}.Release|x86.Build.0 = Release|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Debug|x86.Build.0 = Debug|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Release|x86.ActiveCfg = Release|Any CPU
		{64CAD865-EED4-44E5-B4EF-1906D6A713EE}.Release|x86.Build.0 = Release|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Debug|x86.Build.0 = Debug|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Release|x86.ActiveCfg = Release|Any CPU
		{54290892-25C2-4A68-81C5-147EA7B1A2E1}.Release|x86.Build.0 = Release|Any CPU
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Debug|x86.ActiveCfg = Debug|x86
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Debug|x86.Build.0 = Debug|x86
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Release|Any CPU.Build.0 = Release|Any CPU
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Release|x86.ActiveCfg = Release|x86
		{29A6322D-6F83-46AD-A302-BF28847E030E}.Release|x86.Build.0 = Release|x86
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Debug|x86.ActiveCfg = Debug|x86
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Debug|x86.Build.0 = Debug|x86
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Release|x86.ActiveCfg = Release|x86
		{D02CE903-39E4-41C3-A352-A642F25895C5}.Release|x86.Build.0 = Release|x86
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Debug|x86.Build.0 = Debug|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Release|x86.ActiveCfg = Release|Any CPU
		{A90EC7EB-67CF-473F-BF79-AB2E37EF42C3}.Release|x86.Build.0 = Release|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Debug|x86.Build.0 = Debug|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Release|x86.ActiveCfg = Release|Any CPU
		{F39E9C38-3393-41A9-AA2C-88BC9E7E3C7D}.Release|x86.Build.0 = Release|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Debug|x86.Build.0 = Debug|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Release|Any CPU.Build.0 = Release|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Release|x86.ActiveCfg = Release|Any CPU
		{074E65AD-7A0C-4332-9EB6-EDD7FF4B841C}.Release|x86.Build.0 = Release|Any CPU
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Debug|x86.ActiveCfg = Debug|x86
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Debug|x86.Build.0 = Debug|x86
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Release|Any CPU.Build.0 = Release|Any CPU
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Release|x86.ActiveCfg = Release|x86
		{46A35195-F38B-4FC7-8F42-D7F696AD965D}.Release|x86.Build.0 = Release|x86
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Debug|x86.Build.0 = Debug|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Release|x86.ActiveCfg = Release|Any CPU
		{8A5D389F-60AF-47A4-967E-A2B5C346E1DC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FF22CB12-D24E-44BC-9D95-EF4B86F245FF}
	EndGlobalSection
EndGlobal
