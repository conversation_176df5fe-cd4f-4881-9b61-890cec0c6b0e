﻿using System;
using System.Collections.Generic;
using System.Linq;
using Helper;
using PropertyLayers;

namespace EmailCommunicationBLL
{
    public class StringConditionStrategy : IConditionStrategy<string>
    {
        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;
            bool isCaseSensitive = Operator.Contains("CaseSensitive"); // sample condition, can be updated as per usecase
            string ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue, isCaseSensitive);
            string ActualPropertyValue = ParsePropertyValue(leadDetails[property], isCaseSensitive);
            bool isConditionValid = false;

            List<string> ReferenceValueForInOperator = new();

            if (Operator.ToLower() != "in" && Operator.ToLower() != "not in")
            {
                // for In operator we expect reference value to be string of comma separated values,
                // eg: "pb,pbapp,crosssell"
                ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue);
            }
            else
            {
                if (isCaseSensitive == false)
                {
                    ruleCondition.ReferenceValue = ruleCondition.ReferenceValue.ToLower();
                }
                ReferenceValueForInOperator = ((string[])ruleCondition.ReferenceValue.Split(',')).Select(val => val.Trim()).ToList();

            }

            switch (Operator)
            {
                case "Equal":
                    if (ActualPropertyValue == ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Equal":
                    if (ActualPropertyValue != ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Contains":
                    if (ActualPropertyValue.ToString().ToLower().Contains(ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "In":
                    if (ActualPropertyValue != null && ReferenceValueForInOperator.Contains(ActualPropertyValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not In":
                    if (!(ActualPropertyValue != null && ReferenceValueForInOperator.Contains(ActualPropertyValue)))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Like":
                    if (CoreCommonMethods.Like(ActualPropertyValue, ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Like":
                    if (!CoreCommonMethods.Like(ActualPropertyValue, ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;

                default:
                    break;
            }
            return isConditionValid;
        }


        public string ParsePropertyValue(dynamic property, bool caseSensitive = false)
        {
            var parsedValue = string.Empty;
            try
            {
                try
                {
                    parsedValue = (property == null || property.ToString() == ",,") ? string.Empty : property.ToString();
                }
                catch { }

                if (!caseSensitive)
                {
                    parsedValue = parsedValue.ToLower();
                }
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return property;
            }
        }
    }

    public class BoolConditionStrategy : IConditionStrategy<bool>
    {
        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;
            bool ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue);
            bool ActualPropertyValue = ParsePropertyValue(leadDetails[property]);
            bool isConditionValid = false;

            switch (Operator)
            {
                case "Equal":
                    if (ActualPropertyValue == ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Equal":
                    if (ActualPropertyValue != ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                default:
                    break;
            }
            return isConditionValid;
        }

        public bool ParsePropertyValue(dynamic property, bool caseSensitive = false)
        {
            try
            {
                bool parsedValue = property == null ? false : Convert.ToBoolean(property);
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return property;
            }
        }
    }

    public class DateTimeConditionStrategy : IConditionStrategy<DateTime?>
    {
        public DateTime? ParsePropertyValue(dynamic property, bool isCaseSensitive = false)
        {
            try
            {
                return Convert.ToDateTime(property);
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return null;
            }
        }

        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            bool isConditionValid = false;
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;
            int ReferenceValue = ParseReferenceValue(ruleCondition.ReferenceValue); // Reference Value in minutes
            DateTime? ActualPropertyValue = ParsePropertyValue(leadDetails[property]);

            switch (Operator)
            {
                case "IsNull":
                    if (ActualPropertyValue == null || ActualPropertyValue == DateTime.MinValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "IsNotNull":
                    if (ActualPropertyValue != null && ActualPropertyValue != DateTime.MinValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Before":
                    if (ActualPropertyValue != null && ActualPropertyValue < DateTime.Now.AddMinutes(-1 * ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "WithinLast":
                    if (ActualPropertyValue != null && ActualPropertyValue <= DateTime.Now
                        && ActualPropertyValue >= DateTime.Now.AddMinutes(-1 * ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "WithinNext":
                    if (ActualPropertyValue != null && ActualPropertyValue >= DateTime.Now
                        && ActualPropertyValue <= DateTime.Now.AddMinutes(ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "After":
                    if (ActualPropertyValue != null && ActualPropertyValue >= DateTime.Now.AddMinutes(ReferenceValue))
                    {
                        isConditionValid = true;
                    }
                    break;
                default:
                    break;
            }
            return isConditionValid;
        }

        public static Int32 ParseReferenceValue(dynamic property)
        {
            try
            {
                Int32 parsedValue = Convert.ToInt32(property);
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParseReferenceValue ERROR: {err}, {property}");
                return property;
            }
        }
    }

    #region Numeric conditions
    public class Int64ConditionStrategy : IConditionStrategy<Int64>
    {
        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;
            Int64 ReferenceValue = 0;
            Int64 ActualPropertyValue = ParsePropertyValue(leadDetails[property]);

            List<string> ReferenceValueForInOperator = new();

            if (Operator.ToLower() != "in" && Operator.ToLower() != "not in")
            {
                ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue);
                // for In operator we expect reference value to be string of comma separated values,
                // eg: "13,64,30"
            }
            else
            {
                ReferenceValueForInOperator = ((string[])ruleCondition.ReferenceValue.Split(',')).Select(val => val.Trim()).ToList();
            }

            bool isConditionValid = false;

            switch (Operator)
            {
                case "Equal":
                    if (ActualPropertyValue == ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Equal":
                    if (ActualPropertyValue != ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;

                case "GreaterThan":
                    if (ActualPropertyValue > ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThan":
                    if (ActualPropertyValue < ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "GreaterThanOrEqualTo":
                    if (ActualPropertyValue >= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThanOrEqualTo":
                    if (ActualPropertyValue <= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "DivisibleBy":
                    if (ActualPropertyValue % ReferenceValue == 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "NotDivisibleBy":
                    if (ActualPropertyValue % ReferenceValue != 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "In":
                    if (ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not In":
                    if (!ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                default:
                    break;
            }
            return isConditionValid;
        }

        public Int64 ParsePropertyValue(dynamic property, bool caseSensitive = false)
        {
            try
            {
                Int64 parsedValue = Convert.ToInt64(property);
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return property;
            }
        }
    }
    public class Int32ConditionStrategy : IConditionStrategy<Int32>
    {
        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;
            Int32 ReferenceValue = 0;
            Int32 ActualPropertyValue = ParsePropertyValue(leadDetails[property]);

            List<string> ReferenceValueForInOperator = new();

            if (Operator.ToLower() != "in" && Operator.ToLower() != "not in")
            {
                // for In operator we expect reference value to be string of comma separated values,
                // eg: "13,64,30"
                ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue);
            }
            else
            {
                ReferenceValueForInOperator = ((string[])ruleCondition.ReferenceValue.Split(',')).Select(val => val.Trim()).ToList();
            }

            bool isConditionValid = false;

            switch (Operator)
            {
                case "Equal":
                    if (ActualPropertyValue == ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Equal":
                    if (ActualPropertyValue != ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;

                case "GreaterThan":
                    if (ActualPropertyValue > ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThan":
                    if (ActualPropertyValue < ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "GreaterThanOrEqualTo":
                    if (ActualPropertyValue >= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThanOrEqualTo":
                    if (ActualPropertyValue <= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "DivisibleBy":
                    if (ActualPropertyValue % ReferenceValue == 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "NotDivisibleBy":
                    if (ActualPropertyValue % ReferenceValue != 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "In":
                    if (ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not In":
                    if (!ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                default:
                    break;
            }
            return isConditionValid;
        }

        public Int32 ParsePropertyValue(dynamic property, bool caseSensitive = false)
        {
            try
            {
                Int32 parsedValue = Convert.ToInt32(property);
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return property;
            }
        }
    }
    public class Int16ConditionStrategy : IConditionStrategy<Int16>
    {
        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;

            Int16 ReferenceValue = 0;
            Int16 ActualPropertyValue = ParsePropertyValue(leadDetails[property]);

            List<string> ReferenceValueForInOperator = new();
            if (Operator.ToLower() != "in" && Operator.ToLower() != "not in")
            {
                // for In operator we expect reference value to be string of comma separated values,
                // eg: "13,64,30"
                ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue);
            }
            else
            {
                ReferenceValueForInOperator = ((string[])ruleCondition.ReferenceValue.Split(',')).Select(val => val.Trim()).ToList();
            }

            bool isConditionValid = false;

            switch (Operator)
            {
                case "Equal":
                    if (ActualPropertyValue == ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Equal":
                    if (ActualPropertyValue != ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;

                case "GreaterThan":
                    if (ActualPropertyValue > ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThan":
                    if (ActualPropertyValue < ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "GreaterThanOrEqualTo":
                    if (ActualPropertyValue >= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThanOrEqualTo":
                    if (ActualPropertyValue <= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "DivisibleBy":
                    if (ActualPropertyValue % ReferenceValue == 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "NotDivisibleBy":
                    if (ActualPropertyValue % ReferenceValue != 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "In":
                    if (ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not In":
                    if (!ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                default:
                    break;
            }
            return isConditionValid;
        }

        public Int16 ParsePropertyValue(dynamic property, bool caseSensitive = false)
        {
            try
            {
                Int16 parsedValue = Convert.ToInt16(property);
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return property;
            }
        }
    }
    public class ByteConditionStrategy : IConditionStrategy<Byte>
    {
        public bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            string property = ruleCondition.Property;
            string Operator = ruleCondition.Operator;

            Byte ReferenceValue = 0;
            Byte ActualPropertyValue = ParsePropertyValue(leadDetails[property]);

            List<string> ReferenceValueForInOperator = new();

            if (Operator.ToLower() != "in" && Operator.ToLower() != "not in")
            {
                ReferenceValue = ParsePropertyValue(ruleCondition.ReferenceValue);
                // for In operator we expect reference value to be string of comma separated values,
                // eg: "13,64,30"
            }
            else
            {
                ReferenceValueForInOperator = ((string[])ruleCondition.ReferenceValue.Split(',')).Select(val => val.Trim()).ToList();
            }

            bool isConditionValid = false;

            switch (Operator)
            {
                case "Equal":
                    if (ActualPropertyValue == ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not Equal":
                    if (ActualPropertyValue != ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;

                case "GreaterThan":
                    if (ActualPropertyValue > ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThan":
                    if (ActualPropertyValue < ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "GreaterThanOrEqualTo":
                    if (ActualPropertyValue >= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "LessThanOrEqualTo":
                    if (ActualPropertyValue <= ReferenceValue)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "DivisibleBy":
                    if (ActualPropertyValue % ReferenceValue == 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "NotDivisibleBy":
                    if (ActualPropertyValue % ReferenceValue != 0)
                    {
                        isConditionValid = true;
                    }
                    break;
                case "In":
                    if (ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                case "Not In":
                    if (!ReferenceValueForInOperator.Contains(ActualPropertyValue.ToString()))
                    {
                        isConditionValid = true;
                    }
                    break;
                default:
                    break;
            }
            return isConditionValid;
        }

        public Byte ParsePropertyValue(dynamic property, bool caseSensitive = false)
        {
            try
            {
                Byte parsedValue = Convert.ToByte(property);
                return parsedValue;
            }
            catch (Exception err)
            {
                Console.WriteLine($"ParsePropertyValue ERROR: {err}, {property}");
                return property;
            }
        }
    }
    #endregion
}

