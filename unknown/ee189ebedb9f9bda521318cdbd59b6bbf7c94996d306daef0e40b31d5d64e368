﻿using DataHelper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using PropertyLayers;
using Redis;
namespace DataAccessLibrary
{
    public class PriorityConfig
    {
        public static LeadPriorityConstants getConfig()
        {
            LeadPriorityConstants objt = new LeadPriorityConstants();
            List<int> priorityGroup = new List<int>();
            try
            {
                string path = AppDomain.CurrentDomain.BaseDirectory + "\\LeadPriorityConstants.json";
                ObjectCache CacheConfig = MemoryCache.Default;
                objt = (LeadPriorityConstants)CacheConfig.Get("priorityconfig");
                if (objt == null)
                {
                    /*
                    string Key = RedisCollection.LeadPriorityConstants();
                    string ob = RedisHelper.GetRedisData(Key);
                    */
                    CacheItemPolicy objCachePolicies = new CacheItemPolicy();
                    objCachePolicies.ChangeMonitors.Add(new HostFileChangeMonitor(new List<string> { path }));
                    if (true)
                    {

                        var Json = System.IO.File.ReadAllText(path);
                        var data = JsonConvert.DeserializeObject<LeadPriorityConstants>(Json);

                        string Connectionstring = ConnectionClass.LivesqlConnection();
                        string strQuery = "SELECT UserGroupID from CRM.UserGroupMaster (NOLOCK) WHERE IsOneLead = 1";
                        SqlParameter[] SqlParam = new SqlParameter[0];
                        var obj = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
                        if (obj != null)
                        {
                            var r = obj.Tables[0].AsEnumerable().Select(p => p.Field<Int16>("UserGroupID")).ToList();
                            data.PriorityGroups = r;
                        }

                        CacheConfig.Add("priorityconfig", data, objCachePolicies);
                        //RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(data), new TimeSpan(12,0,0));
                        return data;
                    }
                    /*
                    else
                    {
                        objt = JsonConvert.DeserializeObject<LeadPriorityConstants>(ob);
                        CacheConfig.Add("priorityconfig", objt, objCachePolicies);
                        return objt;
                    }
                    */
                }
                else
                    return objt;
            }
            catch (Exception ex)
            {
                return objt;
            }
        }
    }
}
