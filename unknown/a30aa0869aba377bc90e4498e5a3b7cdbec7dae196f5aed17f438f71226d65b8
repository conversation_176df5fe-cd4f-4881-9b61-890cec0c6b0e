﻿using System;
using System.Collections.Generic;
using System.Data;

using System.Linq;
using DataAccessLayer;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using MongoConfigProject;
using StackExchange.Redis;

namespace EmailCommunicationBLL
{
    public static class CustomerdetailBLL
    {

        public static void UpdateCustomerSubscriptionInMatrix(short duration)
        {
            DateTime requestTime = DateTime.Now;
            List<string> AllProductIds = "UnsubscribeProductIds".AppSettings().Split(',').ToList();
            List<string> CategoriesId = "UnsubscribeCategoryIds".AppSettings().Split(',').ToList();
            List<string> ChannelIds = "UnsubscribeChannelIds".AppSettings().Split(',').ToList();
            bool IsPreviousEntry0 = false;
            bool IsPreviousEntry0Active = false;
            List<string> AlteredProducts = null;            

            try
            {
                var ds = Customerdetaildll.GetUnsubscriptionDataForMatrix(duration);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count != 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        AlteredProducts = new List<string>(AllProductIds);
                        Int32 customerId = 0;
                        try
                        {
                            if (row["CustomerId"] == null || row["CustomerId"] == DBNull.Value)
                            {
                                continue;
                            }

                            customerId = row["CustomerId"] != null && row["CustomerId"] != DBNull.Value ? Convert.ToInt32(row["CustomerId"]) : 0;
                            var categoryId = row["CategoryId"] != null && row["CategoryId"] != DBNull.Value ? Convert.ToInt32(row["CategoryId"]) : 0;
                            var channelId = row["channelId"] != null && row["channelId"] != DBNull.Value ? Convert.ToInt32(row["channelId"]) : 0;
                            var IsActive = Convert.ToBoolean(row["IsActive"]);
                            //var createdBy = row["createdBy"] != null && row["createdBy"] != DBNull.Value ? Convert.ToInt64(row["createdBy"]) : 0;
                            var UpdatedOn = row["UpdatedOn"] != null && row["UpdatedOn"] != DBNull.Value ? Convert.ToDateTime(row["UpdatedOn"]) : DateTime.MinValue;
                            var ProductId = row["ProductId"] != null && row["ProductId"] != DBNull.Value ? Convert.ToInt32(row["ProductId"]) : 0;
                            var MobileNo = row["MobileNo"] != null && row["MobileNo"] != DBNull.Value ? Convert.ToInt64(row["MobileNo"]) : 0;
                            var IsSubscribedCommDB = Convert.ToBoolean(row["IsSubscribed"]);
                            // IsSubscribed  -- true  unblock;
                            // IsSubscribed  --false  block;
                            


                            if (CategoriesId.Contains(Convert.ToString(categoryId)) && ChannelIds.Contains(Convert.ToString(channelId)))
                            {

                                bool IsSubscribedForMatrix = true;
                                if (IsActive == false)
                                {
                                    IsSubscribedForMatrix = true;
                                }
                                else if (IsActive)
                                {
                                    IsSubscribedForMatrix = IsSubscribedCommDB;
                                }

                                /*for matrix db IsActive= 1 means blocked ,0 means unblocked reverse from commdb IsSubscribed*/
                                IsSubscribedForMatrix = !IsSubscribedForMatrix;

                                //checking whether previous entry was 0 or not
                                var ds1 = Customerdetaildll.GetCustomerUnsubscription(customerId, categoryId, channelId);
                                if (ds1 != null && ds1.Tables.Count > 0 && ds1.Tables[0].Rows.Count > 0)
                                {
                                    IsPreviousEntry0 = ds1.Tables[0].Rows[0]["ProductId"] != null && ds1.Tables[0].Rows[0]["ProductId"] != DBNull.Value ? Convert.ToBoolean(Convert.ToInt32(ds1.Tables[0].Rows[0]["ProductId"]) == 0 ? true : false) : false;
                                    IsPreviousEntry0Active = ds1.Tables[0].Rows[0]["IsActive"] != null && ds1.Tables[0].Rows[0]["IsActive"] != DBNull.Value ? Convert.ToBoolean(ds1.Tables[0].Rows[0]["IsActive"]) : false;
                                }
                                //when specific productid is given
                                if (ProductId > 0 && IsPreviousEntry0Active)
                                {
                                    //IsSubscribedForMatrix == false &&

                                    //insert/update the entry
                                    Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsSubscribedForMatrix, 124, UpdatedOn, ProductId, MobileNo);
                                    AlteredProducts.Remove(Convert.ToString(ProductId));

                                    //if entry was 0 against the set, then add all other products in database fro unsubscribing
                                    if(IsPreviousEntry0 == true)
                                    {
                                        foreach(var Product in AlteredProducts)
                                        {
                                            Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsPreviousEntry0Active, 124, UpdatedOn, Convert.ToInt32(Product), MobileNo);
                                        }
                                    }
                                }   
                                else
                                {
                                    //when specific product id is given
                                    if (ProductId > 0)
                                    {
                                        Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsSubscribedForMatrix, 124, UpdatedOn, ProductId, MobileNo);
                                    }
                                    //when 0 is given as productid, then we make entry for every product
                                    else
                                    {
                                        foreach (var Product in AllProductIds)
                                        {
                                            Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsSubscribedForMatrix, 124, UpdatedOn, Convert.ToInt32(Product), MobileNo);
                                        }
                                    }
                                }
                            }                            
                        }
                        catch (Exception err)
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue(null, customerId, err.ToString(), "UpdateCustomerSubscriptionInMatrix", "Allocation", "CustomerDetailBLL", "", "", requestTime, DateTime.Now);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "UpdateCustomerSubscriptionInMatrix", "Allocation", "CustomerDetailBLL", "", "", requestTime, DateTime.Now);
            }


        }

        public static void FetchCustomerForUnsubscriptionDayWise()
        {
            DateTime requestTime = DateTime.Now;           
            try
            {


                var documents = Customerdetaildll.GetSchCollection();
                string StartDate = "";
                string EndDate = "";
                if (documents != null)
                {
                    foreach (var document in documents)
                    {
                        StartDate = document.StartDate.ToString();
                        EndDate = document.EndDate.ToString();
                    }
                }
                if (!string.IsNullOrEmpty(StartDate) && !string.IsNullOrEmpty(EndDate))
                {
                    DateTime Start_dt = Convert.ToDateTime(StartDate);
                    DateTime End_dt = Convert.ToDateTime(EndDate);
                    if (Start_dt <= End_dt)
                    {
                        var ds = Customerdetaildll.FetchCustomerForUnsubscriptionDayWise(Start_dt.ToString("yyyy-MM-dd"), Start_dt.AddDays(1).ToString("yyyy-MM-dd"));
                        if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count != 0)
                        {
                            foreach (DataRow row in ds.Tables[0].Rows)
                            {
                                long custId = row["CustomerId"] != null && row["CustomerId"] != DBNull.Value ? Convert.ToInt64(row["CustomerId"]) : 0;
                                UpdateSubscriptionInMatrixByCustomerId(custId);
                            }
                        }

                        Customerdetaildll.updateSchCollection(Start_dt.AddDays(1));
                    }


                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "FetchCustomerForUnsubscriptionDayWise", "Allocation", "CustomerDetailBLL", "", "", requestTime, DateTime.Now);
            }


        }

        public static void UpdateSubscriptionInMatrixByCustomerId(long custId)
        {
            DateTime requestTime = DateTime.Now;
            List<string> AllProductIds = "UnsubscribeProductIds".AppSettings().Split(',').ToList();
            List<string> CategoriesId = "UnsubscribeCategoryIds".AppSettings().Split(',').ToList();
            List<string> ChannelIds = "UnsubscribeChannelIds".AppSettings().Split(',').ToList();
            bool IsPreviousEntry0 = false;
            bool IsPreviousEntry0Active = false;
            List<string> AlteredProducts = null;
            string strexception = string.Empty;
            try
            {
                var ds = Customerdetaildll.GetUnsubscriptionDataForMatrixByCustId(custId);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count != 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        AlteredProducts = new List<string>(AllProductIds);
                        Int32 customerId = 0;
                        try
                        {
                            if (row["CustomerId"] == null || row["CustomerId"] == DBNull.Value)
                            {
                                continue;
                            }

                            customerId = row["CustomerId"] != null && row["CustomerId"] != DBNull.Value ? Convert.ToInt32(row["CustomerId"]) : 0;
                            var categoryId = row["CategoryId"] != null && row["CategoryId"] != DBNull.Value ? Convert.ToInt32(row["CategoryId"]) : 0;
                            var channelId = row["channelId"] != null && row["channelId"] != DBNull.Value ? Convert.ToInt32(row["channelId"]) : 0;
                            var IsActive = Convert.ToBoolean(row["IsActive"]);
                            //var createdBy = row["createdBy"] != null && row["createdBy"] != DBNull.Value ? Convert.ToInt64(row["createdBy"]) : 0;
                            var UpdatedOn = row["UpdatedOn"] != null && row["UpdatedOn"] != DBNull.Value ? Convert.ToDateTime(row["UpdatedOn"]) : DateTime.MinValue;
                            var ProductId = row["ProductId"] != null && row["ProductId"] != DBNull.Value ? Convert.ToInt32(row["ProductId"]) : 0;
                            var MobileNo = row["MobileNo"] != null && row["MobileNo"] != DBNull.Value ? Convert.ToInt64(row["MobileNo"]) : 0;
                            var IsSubscribedCommDB = Convert.ToBoolean(row["IsSubscribed"]);
                            // IsSubscribed  -- true  unblock;
                            // IsSubscribed  --false  block;



                            if (CategoriesId.Contains(Convert.ToString(categoryId)) && ChannelIds.Contains(Convert.ToString(channelId)))
                            {

                                bool IsSubscribedForMatrix = true;
                                if (IsActive == false)
                                {
                                    IsSubscribedForMatrix = true;
                                }
                                else if (IsActive)
                                {
                                    IsSubscribedForMatrix = IsSubscribedCommDB;
                                }

                                /*for matrix db IsActive= 1 means blocked ,0 means unblocked reverse from commdb IsSubscribed*/
                                IsSubscribedForMatrix = !IsSubscribedForMatrix;

                                //checking whether previous entry was 0 or not
                                var ds1 = Customerdetaildll.GetCustomerUnsubscription(customerId, categoryId, channelId);
                                if (ds1 != null && ds1.Tables.Count > 0 && ds1.Tables[0].Rows.Count > 0)
                                {
                                    IsPreviousEntry0 = ds1.Tables[0].Rows[0]["ProductId"] != null && ds1.Tables[0].Rows[0]["ProductId"] != DBNull.Value ? Convert.ToBoolean(Convert.ToInt32(ds1.Tables[0].Rows[0]["ProductId"]) == 0 ? true : false) : false;
                                    IsPreviousEntry0Active = ds1.Tables[0].Rows[0]["IsActive"] != null && ds1.Tables[0].Rows[0]["IsActive"] != DBNull.Value ? Convert.ToBoolean(ds1.Tables[0].Rows[0]["IsActive"]) : false;
                                }
                                //when specific productid is given
                                if (ProductId > 0 && IsPreviousEntry0Active)
                                {
                                    //IsSubscribedForMatrix == false &&

                                    //insert/update the entry
                                    Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsSubscribedForMatrix, 124, UpdatedOn, ProductId, MobileNo);
                                    AlteredProducts.Remove(Convert.ToString(ProductId));

                                    //if entry was 0 against the set, then add all other products in database fro unsubscribing
                                    if (IsPreviousEntry0 == true)
                                    {
                                        foreach (var Product in AlteredProducts)
                                        {
                                            Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsPreviousEntry0Active, 124, UpdatedOn, Convert.ToInt32(Product), MobileNo);
                                        }
                                    }
                                }
                                else
                                {
                                    //when specific product id is given
                                    if (ProductId > 0)
                                    {
                                        Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsSubscribedForMatrix, 124, UpdatedOn, ProductId, MobileNo);
                                    }
                                    //when 0 is given as productid, then we make entry for every product
                                    else
                                    {
                                        foreach (var Product in AllProductIds)
                                        {
                                            Customerdetaildll.PushUnsubscriptionDataInMatrix(customerId, categoryId, channelId, IsSubscribedForMatrix, 124, UpdatedOn, Convert.ToInt32(Product), MobileNo);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception err)
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue(null, customerId, err.ToString(), "UpdateCustomerSubscriptionInMatrix", "Allocation", "CustomerDetailBLL", "", "", requestTime, DateTime.Now);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", custId, strexception, "UpdateSubscriptionInMatrixByCustomerId", "Allocation", "Scheduler","", string.Empty, DateTime.Now, DateTime.Now);

            }

        }

    }
}


