﻿using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace PropertyLayers
{
    [DataContract]
    public class SaveInfo
    {
        [DataMember]
        public bool IsSaved { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public Int32 StatusCode { get; set; }

        [DataMember]
        public string ReferralId { get; set; }
    }

    [DataContract]
    public class SVModal
    {
        [DataMember]
        public string URL { get; set; }

        [DataMember]
        public string User { get; set; }

        [DataMember]
        public Int32 StatusCode { get; set; }

    }

    [DataContract]
    public class SlotsModel
    {
        [DataMember]
        public Int32 SlotId { get; set; }

        [DataMember]
        public string StartTime { get; set; }
        [DataMember]
        public string EndTime { get; set; }


    }

    [DataContract]
    public class AppointmentSlotsModel
    {
        [DataMember]
        public List<SlotsModel> SlotsModel { get; set; }
        [DataMember]
        public Dictionary<string, List<int>> NotAvailableSlots { get; set; }
        [DataMember]
        public string ServerDate { get; set; }
        [DataMember]
        public Int16 NoOfDays { get; set; }



    }
    [DataContract]
    public class ResponseAPI
    {
        [DataMember]
        public bool status { get; set; }
        [DataMember]
        public string message { get; set; }
        [DataMember]
        public DateTime dockerTime { get; set; }


    }

    [DataContract]
    public class AutoCompleteResponse
    {
        [DataMember]
        public string main_text { get; set; }
        [DataMember]
        public string secondary_text { get; set; }
        [DataMember]
        public string place_id { get; set; }

    }
    [DataContract]
    public class PincodeLatLongModel
    {
        [DataMember]
        public Int32 Pincode { get; set; }
        [DataMember]
        public decimal Lat { get; set; }
        [DataMember]
        public decimal Long { get; set; }

    }
    [DataContract]
    public class PlaceLatLongModel
    {
        [DataMember]
        public string PlaceId { get; set; }
        [DataMember]
        public decimal Lat { get; set; }
        [DataMember]
        public decimal Long { get; set; }

    }


    public class GoogleMapResultModel
    {
        public Prediction[] predictions { get; set; }
        public string status { get; set; }
    }

    public class Prediction
    {
        public string description { get; set; }
        public Matched_Substrings[] matched_substrings { get; set; }
        public string place_id { get; set; }
        public string reference { get; set; }
        public Structured_Formatting structured_formatting { get; set; }
        public Term[] terms { get; set; }
        public string[] types { get; set; }
    }

    public class Structured_Formatting
    {
        public string main_text { get; set; }
        public Main_Text_Matched_Substrings[] main_text_matched_substrings { get; set; }
        public string secondary_text { get; set; }
    }

    public class Main_Text_Matched_Substrings
    {
        public int length { get; set; }
        public int offset { get; set; }
    }

    public class Matched_Substrings
    {
        public int length { get; set; }
        public int offset { get; set; }
    }

    public class Term
    {
        public int offset { get; set; }
        public string value { get; set; }
    }

    public class AppointmentsDataModel
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Status { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string SubStatus { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AppointmentId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 StatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PivcStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string VerificationStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ApplicationNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PivcLink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }



        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long ParentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Address { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Pincode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 CityId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 AppointmentType { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 OfflineCityId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 ZoneId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Address1 { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Landmark { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Comments { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public List<Recommendation> PlanList;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 AssignmentId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Reason { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 ReasonId { get; set; }



        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Source { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime? AppointmentStart { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 IncomeDocsId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 EducationId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 IncomeId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 BookingId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 SlotId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public PlaceLatLongModel location { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NearBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallData Call { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public RevisitData Revisit { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallBackData CallBack { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsVisited { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsCallDone { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsCBDone { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsAppointmentDone { get; set; }
        

    }

    public class Recommendation
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string supplierId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string planId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string supplierName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string planName { get; set; }

    }


    public class AutoCompleteModel
    {
        [DataMember]
        public string sessiontoken { get; set; }
        [DataMember]
        public string Pincode { get; set; }
        [DataMember]
        public string Input { get; set; }

    }

   

    public class LeadsSubStatusModel
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 StatusId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int64 SubStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 ParentID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsLogSubStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Latitude { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Longitude { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 IsAppDone { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CancelReasonId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CreatedBy { get; set; }


    }


    [DataContract]
    public class RescheduleValidationResponse
    {
        [DataMember]
        public string RescheduleLink { get; set; }
        [DataMember]
        public bool IsReschedule { get; set; }
    }
    public class AppointmentConfirmResponse
    {
        [DataMember]
        public DateTime NewAppointmentDatetime { get; set; }
        
        [DataMember]
        public DateTime LastAppointmenttime { get; set; }

        [DataMember]
        public bool CanAppointmentConfirm { get; set; }
        [DataMember]
        public Int64 AppointmentSubstatus { get; set; }


        [DataMember]
        public string Reason { get; set; }
        [DataMember]
        public Int16 ReasonId { get; set; }

        [DataMember]
        public string Address { get; set; }
        [DataMember]
        public string SlotStartTime { get; set; }
        [DataMember]
        public string SlotEndTime { get; set; }
        [DataMember]
        public string RescheduleLink { get; set; }
        [DataMember]
        public Int64 NewAppointmentID { get; set; }

        [DataMember]
        public Int64 NewAppointmentUID { get; set; }

        [DataMember]
        public Int32 ProductId { get; set; }
        [DataMember]
        public string CustomerName { get; set; }
        [DataMember]
        public string ProductName { get; set; }
    }

    public enum ConfirmReasonTypeEnum
    {
        IsAlreadyConfirm = 1,
        Cancel = 2,
        Complete = 3,
        Reschedule = 4,
        NoAppointmentAvailable = 5,
        NewAppointmentSchedule = 6,
        IsLapse = 7,
        Confirm = 8,
        NewAppointmentConfirm=9,

    }


    public enum AppointmentTypeEnum
    {
        Default = 0,
        setAppointment = 1,

    }




    public class AppoitmentstatusMaster
    {
        [DataMember]
        public string SubStatusName { get; set; }
        [DataMember]
        public Int16 SubStatusID { get; set; }

    }
    public class ShortLinkResponse
    {
        public bool status { get; set; }
        public string url { get; set; }
    }

    public class SelfieModel
    {
        public long AppointmentId { get; set; }
        public long UserID { get; set; }
        public string url { get; set; }
    }
    

    public class CustomerAuthenticateData
    {

        [DataMember]
        public string encryptLeadId { get; set; }
        
        [DataMember]
        public long Token { get; set; }
        [DataMember]
        public DateTime ts { get; set; }
        [DataMember]
        public string Url { get; set; }
        [DataMember]
        public string LeadId { get; set; }
        
    }
    public enum EnumAppSusbtatus
    {
        Booked = 2002,
        Completed = 2003,
        Cancelled = 2004,
        ReScheduled = 2005,
        Confirmed = 2088,
        Start=2124
    }
    public class AdvisorLocation
    {
        [DataMember]
        public long UserID { get; set; }
        [DataMember]
        public Int32 ServingPincode { get; set; }
        [DataMember]
        public decimal Lat { get; set; }
        [DataMember]
        public decimal Long { get; set; }


    }
    [DataContract]
    public class AppointmentModel
    {
        [DataMember]
        public long ParentId { get; set; }
        [DataMember]
        public Int16 SubstatusId { get; set; }
        [DataMember]
        public string SubstatusName { get; set; }
    }

    [DataContract]
    public class FosCityModel
    {
       
        [DataMember]
        public bool IsActive { get; set; }
        [DataMember]
        public long CityId { get; set; }
        [DataMember]
        public int ZoneId { get; set; }
        [DataMember]
        public int AppointmentTypeId { get; set; }
        [DataMember]
        public int AssignmentTypeId { get; set; }
        [DataMember]
        public int ProductId { get; set; }
        [DataMember]
        public long Id { get; set; }
        [DataMember]
        public long UserId { get; set; }
        

    }

    [DataContract]
    public class ChurnLeadModel
    {

        [DataMember(EmitDefaultValue = false)]
        public string AgentType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool VisitFlag { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Incentive { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal VisitApp { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal VisitCr { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal ATS { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Issuance { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal IncentiveRate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }
    }
    [DataContract]
    public class UserChurnLeadModel
    {

        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBookingDone { get; set; }

    }


}