﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Text;
using DataAccessLayer;
using DataAccessLibrary;
using DataHelper;
using Helper;
using LoggingHelper;
using Microsoft.Extensions.Configuration;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;

namespace EmailCommunicationBLL
{

    public class TermPayURanksBLL : ITermPayURanksBLL
    {

        public static string Push2LacLeadsToTermCJ()
        {
            if (CoreCommonMethods.GetEnvironmentVar().ToLower() != "live")
            {
                return "Push2LacLeadsToTermCJ: Run on Live env only";
            }
            DataSet ds = TermPayURanksDLL.GetTermLeadsPayU2lackincome();
            int counter = 10;
            if (ds != null && ds.Tables != null && ds.Tables.Count>0 && ds.Tables[0].Rows.Count != 0)
            {
                foreach (DataRow leadRow in ds.Tables[0].Rows)
                {
                    if (counter <= 0) return "restricted";
                    counter--;

                    long LeadId = 0;
                    try
                    {
                        dynamic msgObj = new ExpandoObject();

                        LeadId = Convert.ToInt64(leadRow["LeadID"]);
                        msgObj.leadId = LeadId;

                        msgObj.mobileNo = (leadRow["MobileNo"] != null && leadRow["MobileNo"] != DBNull.Value) ? leadRow["MobileNo"].ToString() : "";
                        msgObj.email = (leadRow["EmailID"] != null && leadRow["EmailID"] != DBNull.Value) ? leadRow["EmailID"].ToString() : "";
                        msgObj.custId = Convert.ToInt64(leadRow["CustomerID"]);


                        // push to CJ
                        
                        TermPayURanksDLL.SendEventToTermCJ(msgObj);
                        TermPayURanksDLL.Insert2LacLeadsPushtoTerm(LeadId);
                    }
                    catch (Exception error)
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, error.ToString(), "GetTermPayURanks LeadError", "Allocation", "", "", "", DateTime.Now, DateTime.Now);
                    }
                }
            }
            return "done";
        }


        public static string GetTermPayURanks()
        {
            DataSet bookingDataSet = TermPayURanksDLL.GetBookingforPayuRank();

            if (bookingDataSet != null && bookingDataSet.Tables[0].Rows.Count != 0)
            {
                foreach (DataRow leadRow in bookingDataSet.Tables[0].Rows)
                {
                    long LeadId = 0;
                    try
                    {

                        LeadId = (Int64)leadRow["LEADID"];
                        string MobileNo = leadRow["MobileNo"] != null && leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : "";
                        string EmailId = leadRow["emailId"] != null && leadRow["emailId"] != DBNull.Value ? leadRow["emailId"].ToString() : "";
                        var rank = AllocationBLL.GetPayULeadRank(MobileNo, EmailId, LeadId);

                        TermPayURanksDLL.InsertBookingRanks(LeadId, rank);
                    }
                    catch (Exception error)
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, error.ToString(), "GetTermPayURanks LeadError", "Allocation", "", "", "", DateTime.Now, DateTime.Now);
                    }
                }
            }
            return string.Empty;
        }



        public static void GetIncomeFromPayU()
        {


        }
        public static string GetLeadsIncomeFromPayU()
        {

            List<string> dates = new() {
                "2024-04-02"
            };
            foreach (string dt in dates)
            {
                DateTime date = DateTime.Parse(dt);
                //for (int hour = 2; hour < 24; hour++)
                //{
                DateTime startDT = date.AddHours(0);
                DateTime endDT = date.AddHours(1);

                DataSet ds = TermPayURanksDLL.GetLeadsforPayuIncome(startDT, endDT);
                int i = 0;
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow leadRow in ds.Tables[0].Rows)
                    {
                        //if (i > 10) {
                        //    break;
                        //}
                        i++;
                        LeadDetails leadData = new()
                        {
                            LeadId = (Int64)leadRow["LeadID"],
                            CustomerId = leadRow["CustomerID"] == null || leadRow["CustomerID"] == DBNull.Value ? (Int64)0 : (Int64)leadRow["CustomerID"],
                            MobileNo = leadRow["MobileNo"] != null && leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : "",
                            EmailId = leadRow["EmailID"] != null && leadRow["EmailID"] != DBNull.Value ? leadRow["EmailID"].ToString() : "",
                        };


                        // -1 -> data not available at payu
                        // -2 -> API didnot respond for this customer previously
                        // -3 -> Cache not present
                        //long PredictedIncome = TermPayURanksDLL.GetIncomePayU(leadData.CustomerId);

                        //if (PredictedIncome < -2) {
                        //        PredictedIncome = GetIncomeFromPayUAPI(leadData.MobileNo, leadData.EmailId, leadData.LeadId);
                        //}

                        try
                        {
                            //bool IsNRI = false;
                            //if (!new[] { "392", "91", "999", "INDIA", "0", "" }.Contains(leadData.Country) || lead.InvestmentTypeID == 15 || (!string.IsNullOrEmpty(lead.Utm_source) && lead.Utm_source.ToLower() == "facebook" && !string.IsNullOrEmpty(lead.Utm_campaign) && CoreCommonMethods.Like(lead.Utm_campaign, "%NRI%")))
                            //{
                            //    IsNRI = true;
                            //}

                            // DataSet payUIncomeDataset = TermAllocationDLL.GetTermLeadsPayUIncome(leadData.CustomerId);

                            // if (payUIncomeDataset != null)
                            // {
                            //     var ds1 = payUIncomeDataset.Tables[0].Rows[0];

                            //     leadData.AnnualIncome = ds1["AnnualIncome"] == null || ds1["AnnualIncome"] == DBNull.Value ? 0 : Convert.ToInt64(ds1["AnnualIncome"]);

                            //     //if (leadData.AnnualIncome == 0)
                            //     //{
                            //     //    TermPayURanksDLL.Insert2LacLeadsPushtoTerm(lead.LeadId, lead.AnnualIncome, lead.CustomerId, "apple");
                            //     //}
                            // }
                            // if(leadData.AnnualIncome  0)
                            // {
                                string DerivedFrom = "NA";
                                long PredictedIncome = TermPayURanksBLL.GetIncomeFromPayUAPI(leadData.MobileNo, "");

                                if (PredictedIncome > 0)
                                {
                                    leadData.AnnualIncome = PredictedIncome * 12;
                                    DerivedFrom = "PayU";
                                }
                                
                                TermPayURanksDLL.Insert2LacLeadsPushtoTerm(leadData.LeadId, leadData.AnnualIncome, leadData.CustomerId, DerivedFrom);

                                TermPayURanksDLL.UpdatePayUIncomeTerm(leadData.LeadId, leadData.AnnualIncome);

                            // }
                        }
                        catch (Exception ex)
                        {
                            string error = ex.ToString();
                            LoggingHelper.LoggingHelper.AddloginQueue(null, leadData.LeadId, error, "PopulatePayUIncome", "Allocation", "TermAllocationBLL", "", "", DateTime.Now, DateTime.Now);
                        }
                        //DataSet payUIncomeDataset = TermAllocationDLL.GetTermLeadsPayUIncome(leadData.CustomerId);


                        //TermPayURanksDLL.InsertPayUIncomeData(leadData.LeadId, PredictedIncome, leadData.CustomerId);
                        //TermPayURanksDLL.Insert2LacLeadsPushtoTerm(leadData.LeadId, PredictedIncome*12, leadData.CustomerId, "PAYU");

                    }

                }
                //}
            }
            return "";
        }


        public static long GetIncomeFromPayUAPI(string mobileNo, string emailId, long LeadId = 0)
        {
            DateTime requestTime = DateTime.Now;
            List<KeyValuePair<string, string>> FormData = null;
            string error = string.Empty;
            string response = string.Empty;

            try
            {
                emailId = emailId.ToLower();
                // --------QA
                //string URL ="Payu_APIUrl_QA".AppSettings() + "daas/";
                //Dictionary<object, object> header = new() {
                //    { "API-KEY", "Payu_income_APIKey_QA".AppSettings() }, { "API-TOKEN", "Payu_income_APIToken_QA".AppSettings() }
                //};
                //FormData = new List<KeyValuePair<string, string>>
                //{
                //    new KeyValuePair<string, string>("mobile", "da7f5240ff54b5cdbaa9e99be6fd84b26cd6887abdaecfe818cc0b6edf2d4444"),
                //    new KeyValuePair<string, string>("email", "59cc86ead166eb8f999645de4fd302ad6ecda445ade3303577eb175a57fe131a"),
                //};
                // --------


                string URL = "Payu_APIUrl".AppSettings() + "daas/";
                Dictionary<object, object> header = new() {
                    { "API-KEY", "Payu_income_APIKey".AppSettings() }, { "API-TOKEN", "Payu_income_APIToken".AppSettings() }
                };

                if(!string.IsNullOrEmpty(emailId))
                {
                    FormData = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("mobile", Crypto.ComputeSHA256Hash(mobileNo)),
                        new KeyValuePair<string, string>("email", Crypto.ComputeSHA256Hash(emailId))
                    };
                } 
                else 
                {
                    FormData = new List<KeyValuePair<string, string>>
                    {
                        new("mobile", Crypto.ComputeSHA256Hash(mobileNo)),
                    };
                }

                response = CommonAPICall.PostAPICall_FormDataAsync(URL, FormData, header, 2000);
                dynamic _data = null;
                if (response != null) _data = JsonConvert.DeserializeObject<dynamic>(response);
                if (_data != null && _data.message == "Data Fetched Successfully!!" && _data.data != null && _data.data.predicted_income != null)
                {
                    return Convert.ToInt64(_data.data.predicted_income);
                }
                else
                {
                    return -2;
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return -1;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, error, "GetIncomeFromPayUAPI", "Allocation", "", JsonConvert.SerializeObject(FormData), response, requestTime, DateTime.Now);
            }
        }
    }
}

