﻿using DataAccessLibrary;
using DataHelper;
using System.Data.SqlClient;
using System.Data;
using System;
using System.Threading.Channels;
using MongoDB.Bson;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using PropertyLayers;
using System.Collections.Generic;

namespace DataAccessLayer
{
    public class Customerdetaildll
    {
        public static bool SetCustomerUnsubscription(long customerId, int categoryId, int channelId, bool unSubscribed,DateTime RequestPushTime, int ProductId, long MobileNo)
        {

            SqlParameter[] sqlParam = new SqlParameter[7];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@CategoryId", categoryId);
            sqlParam[2] = new SqlParameter("@ChannelId", channelId);
            sqlParam[3] = new SqlParameter("@IsActive", Convert.ToByte(unSubscribed));
            sqlParam[4] = new SqlParameter("@UpdatedOn", RequestPushTime);
            sqlParam[5] = new SqlParameter("@ProductId", ProductId);
            sqlParam[6] = new SqlParameter("@MobileNo", MobileNo);

            var result = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateCustomerUnsubscriptionV1]", sqlParam);

            if (result == 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public static DataSet GetUnsubscriptionDataForMatrix(short duration)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@duration", duration);
            return SqlHelper.ExecuteDataset(ConnectionClass.BmsDBsqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUnsubscriptionDataForMatrix_test]", sqlParam);
        }

        public static void PushUnsubscriptionDataInMatrix(long customerId, int categoryId, int channelId, bool IsActive, long createdBy, DateTime UpdatedOn, int ProductId, long MobileNo)
        {

                SqlParameter[] sqlParam = new SqlParameter[8];
                sqlParam[0] = new SqlParameter("@CustomerId", customerId);
                sqlParam[1] = new SqlParameter("@CategoryId", categoryId);
                sqlParam[2] = new SqlParameter("@ChannelId", channelId);
                sqlParam[3] = new SqlParameter("@IsActive", Convert.ToByte(IsActive));
                sqlParam[4] = new SqlParameter("@createdBy", 121);
                sqlParam[5] = new SqlParameter("@UpdatedOn", UpdatedOn);
                sqlParam[6] = new SqlParameter("@ProductId", ProductId);
                sqlParam[7] = new SqlParameter("@MobileNo", MobileNo);

                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateCustomerUnsubscriptionV1]", sqlParam);
        }

        public static DataSet GetCustomerUnsubscription(long customerId, int categoryId, int channelId)
        {
            SqlParameter[] sqlParam = new SqlParameter[8];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@CategoryId", categoryId);
            sqlParam[2] = new SqlParameter("@ChannelId", channelId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerUnsubscription]", sqlParam);
        }

        public static DataSet GetMultiProdLead()
        {
            SqlParameter[] sqlParam = new SqlParameter[0];

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetMultiProdLead]", sqlParam);
        }
        
        public static DataSet GetMissSellCommData(string salesAgentEmployeeId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@EmployeeId", salesAgentEmployeeId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetMissSellCommData]", sqlParam);
        }

        public static void TriggerCommunicationInsertSMSLead(Int64 LeadID, string TriggerName, string UUID = null, string ActionName = null)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[1] = new SqlParameter("@TriggerName", TriggerName);
            SqlParam[2] = new SqlParameter("@UID", UUID);
            SqlParam[3] = new SqlParameter("@ActionName", ActionName);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertMotorSMSSendDump]", SqlParam);
        }

        public static DataSet GetUnsubscriptionDataForMatrixByCustId(long custId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustomerId", custId);
            return SqlHelper.ExecuteDataset(ConnectionClass.BmsDBsqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUnsubscriptionDataForMatrix_test]", sqlParam);
        }

        public static DataSet FetchCustomerForUnsubscriptionDayWise(string fromDate, string toDate)
        {

            SqlParameter[] sqlParam = new SqlParameter[1];

            string strQuery = "SELECT  distinct CustomerId from comm.comm.CustomerUnsubscription with(nolock) where  " +
                                  "(" +

                                      "(CreatedOn >= CAST('" + fromDate + "' AS DATE) AND CreatedOn <= CAST('" + toDate + "' AS DATE))" +
                                      "OR (ModifiedOn >= CAST('" + fromDate + "'  AS DATE) AND ModifiedOn <= CAST('" + toDate + "'  AS DATE))" +
                                   ")";


            return SqlHelper.ExecuteDataset(ConnectionClass.BmsDBsqlConnection(), CommandType.Text, strQuery);
        }


        public static void updateSchCollection(DateTime dt)
        {
            MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());
            var query = Query.Null;
            var update = Update.Set("StartDate", dt.ToString("yyyy-MM-dd"));
            var result = OneLeadDb.UpdateDocument(query, update, "SchCollection");
        }

        public static dynamic GetSchCollection()
        {
            try
            {
                MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());
                return OneLeadDb.GetDocuments<dynamic>(null, "SchCollection");
            }
            catch (Exception ex)
            {               
                return null;
            }
        }
    }
}