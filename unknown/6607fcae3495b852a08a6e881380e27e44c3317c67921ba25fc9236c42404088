﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{
    public class MissSellData
    {
        [DataMember(EmitDefaultValue = false)]
        public string EventName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime MisSellDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int BookingId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string InsurerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MisSellRaisedByEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MisSellRaisedByEmployeeName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SalesAgentEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SalesAgentEmployeeName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int MisSellingReasonId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MisSellingReason { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ServiceMisSellingComment { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SalesMisSellingComment { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int MisSellingStatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MisSellingStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool Closed { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ServiceTLEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ServiceTLEmployeeName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int MissellAmount { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TLEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TLUserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AMEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AMUserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ManagerEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ManagerUserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TLEmail { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AMEmail { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ManagerEmail { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MisSellingTLEmailId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string QualityMisSellingComment { get; set; }
    }

    public class MissSellQualityUsers
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }
    }
}
