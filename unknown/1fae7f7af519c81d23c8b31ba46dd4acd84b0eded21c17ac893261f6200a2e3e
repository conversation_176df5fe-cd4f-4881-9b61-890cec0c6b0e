﻿using System;
using PropertyLayers;

namespace EmailCommunicationBLL
{
	public static class ConditionCheckerFactory
	{
	
        public static bool ValidateCondition(string type, RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            return type.ToLower() switch
            {
                "string" => new StringConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                "bool" => new BoolConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                "long" or "int64" => new Int64ConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                "int" or "int32" => new Int32ConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                "short" or "int16" => new Int16ConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                "byte" => new ByteConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                "datetime" => new DateTimeConditionStrategy().CheckCondition(ruleCondition, leadDetails),
                _ => false,
            };
        }
    }
}

