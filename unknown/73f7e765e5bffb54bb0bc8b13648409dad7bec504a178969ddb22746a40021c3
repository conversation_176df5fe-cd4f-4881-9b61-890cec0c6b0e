﻿using System;
using System.Runtime.Caching;
namespace Helper
{
    public static class CommonCache
    {
        static class TypeLock<T>
        {
            public static readonly object SyncLock = new object();
        }
        public static T GetOrInsertIntoCache<T>(T Data, string key, int CaceheTimeOut = 25)
        {
            Object obj = MemoryCache.Default[key];
            try
            {
                if (obj != null && Convert.ToString(obj) == Convert.ToString(Data))
                {
                    return (T)obj;
                }
                lock (string.Intern(key))
                {
                    Object obj1 = MemoryCache.Default[key];
                    if (obj1 != null && Convert.ToString(obj1) == Convert.ToString(Data))
                    {
                        return (T)obj1;
                    }
                    T obj2 = Data;
                    if (obj2 == null) return default(T);
                    //var obj3 = MemoryCache.Default.AddOrGetExisting(key, obj2, DateTime.Now.AddMinutes(CaceheTimeOut));
                    MemoryCache.Default.Set(key, obj2, DateTime.Now.AddMinutes(CaceheTimeOut));
                    return obj2;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetOrInsertIntoCache." + " key" + key + ex.ToString());
                return default(T);
            }
        }
    }
}
