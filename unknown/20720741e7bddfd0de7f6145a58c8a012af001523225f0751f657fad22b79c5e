﻿
using EmailCommunicationBLL;
using GlobalErrorHandling.Extensions;
using PrioritizationQueueSchedulers;

namespace Allocation
{
    public class Startup
    {
        public IConfiguration configRoot
        {
            get;
        }

        public Startup(IConfiguration configuration)
        {
            configRoot = configuration;
        }


        public static void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen();
            services.AddScoped<IAllocationBLL, AllocationBLL>();
            services.AddScoped<IFOSAllocationBLL, FOSAllocationBLL>();
            services.AddScoped<IRejectLeadBLL, RejectLeadBLL>();
            services.AddScoped<ILeadRejectionBLL, LeadRejectionBLL>();
            services.AddScoped<ITermAllocationBLL, TermAllocationBLL>();
            services.AddScoped<IPushMultiProdLeadBLL, PushMultiProdLeadBLL>();
            services.AddScoped<IRejectAllLeadsBLL, RejectAllLeadsBLL>();
            services.AddScoped<ISMEBLL, SMEBLL>();
            //services.AddScoped<ITermPayURanksBLL, TermPayURanksBLL>();
            services.AddSingleton(new HealthAllocation());
            services.AddSingleton(new TermAllocation());
            services.AddSingleton(new FosAllocation());
            services.AddSingleton(new RejectLeadScheduler());
            services.AddSingleton(new CustomerUnsubscription());
            services.AddSingleton(new LeadRejectionScheduler());
            //services.AddSingleton(new TermPayUScheduler());
            services.AddSingleton(new KafkaConsumer());
            services.AddSingleton(new SMEAllocation());
            services.AddSingleton(new PushMultiProdLead());
            services.AddSingleton(new RejectAllLeadsScheduler());
            services.AddSingleton(new MigrateUnsubscriptionData());
            // services.AddSingleton(new TermLeadPushToCJPayuSchedular());

        }


        public static void Configure(WebApplication app, IWebHostEnvironment env)
        {
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            app.UseHttpsRedirection();
            app.UseAuthorization();
            app.MapControllers();
            app.ConfigureExceptionHandler();//for globally catch error
            app.Run();
        }
    }
}

