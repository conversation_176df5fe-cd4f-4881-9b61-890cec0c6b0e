using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;
using EmailCommunicationBLL;
using System.Text;
using System.Timers;


namespace PrioritizationQueueSchedulers
{
    public class RejectAllLeadsScheduler
    {
        private static Timer _rejectionTimer;

        public RejectAllLeadsScheduler()
        {
            _rejectionTimer = new Timer(60 * 1000);
            _rejectionTimer.Elapsed += _LeadRejection_timer_Elapsed;
            _rejectionTimer.Start();
        }

        static async void _LeadRejection_timer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }

            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _rejectionTimer.Stop();

                sb.Append("start-");

                bool RunRejectAllLeadsScheduler = "RunRejectAllLeadsScheduler".AppSettings() == "true";
                if (RunRejectAllLeadsScheduler == true)
                {
                    RejectAllLeadsBLL.RejectAllLeads();
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "RejectAllLeadsScheduler", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _rejectionTimer.Start();
            }
        }


    }
}
