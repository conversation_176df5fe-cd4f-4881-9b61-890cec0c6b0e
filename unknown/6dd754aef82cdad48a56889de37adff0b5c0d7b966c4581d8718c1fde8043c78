﻿using Helper;
using PropertyLayers;
using System;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class LeadRejectionFactory
    {
        public ILeadRejectionLogics GetRejectionLogic(ParentDetails ParentLead)
        {
            //Int16 ProductId = ParentLead.ProductId;
            //switch (ProductId)
            //{
            //    case 115:
            //        return new InvestmentRejectionLogics();
            //    case 3:
            //        return new TravelRejectionLogics();
            //    case 131:
            //        return new SMERejectionLogics();
            //    case 117:
            //        return new CarRejectionLogics();
            //    case 2:
            //        return new HealthRejectionLogics();
            //    case 7:
            //        return new TermRejectionLogics();
            //    case 114:
            //        return new TwoWheelerRejectionLogics();

            //    default:
            //        return new FalseyRejectionLogics();

            //}

            return new EvaluationLogics();
        }
    }
}
