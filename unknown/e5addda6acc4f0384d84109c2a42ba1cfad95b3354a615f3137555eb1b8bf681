using System;
using System.IO;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;

namespace Helper
{
    public class AmazonSecret
    {
        public static string GetSecret(string secretName)
        {
            MemoryStream memoryStream = new();
            IAmazonSecretsManager client = new AmazonSecretsManagerClient(Amazon.RegionEndpoint.APSouth1);

            GetSecretValueRequest request = new GetSecretValueRequest();
            request.SecretId = secretName;
            request.VersionStage = "AWSCURRENT"; // VersionStage defaults to AWSCURRENT if unspecified.

            GetSecretValueResponse response = null;


            response = client.GetSecretValueAsync(request).Result;
            
            string secret;
            if (response.SecretString != null)
            {
                secret = response.SecretString;
            }
            else
            {
                memoryStream = response.SecretBinary;
                StreamReader reader = new StreamReader(memoryStream);
                secret = System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(reader.ReadToEnd()));
            }


            return secret;
        }
    }
}
