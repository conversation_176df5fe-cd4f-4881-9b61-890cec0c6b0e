﻿using PropertyLayers;
using System;
using System.Collections.Generic;


namespace EmailCommunicationBLL.RejectionHelpers
{
    public class InvestmentRejectionHelper
    {
        public static bool Reject90DaysOlderLead(ParentDetails ParentLead)
        {
           
            List<string> LeadSourceStrings = new List<string>{ "ACAFFAPP", "ACAFF"};
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate" };

            if (RejectionHelper.IsDateWithinRange(100, 150, ParentLead.CreatedOn)
                && (ParentLead.CallHistory.Count == 0 || !(RejectionHelper.IsDateWithinRange(0, 15, ParentLead.LastCallDate)))// call not done in last 15 days
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings)))
             
            {
                return true;
            }

            return false;
        }

        public static bool RejectUnAssignedInvestmentLeads(ParentDetails ParentLead)
        {

            //No check for LD.ParentID is NULL
            List<string> LeadSourceStrings = new List<string> { "RENEWAL" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2 };


            if (RejectionHelper.IsDateWithinRange(5, 10, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                )
            {
                return true;
            }
            return false;
        }
    }
}

