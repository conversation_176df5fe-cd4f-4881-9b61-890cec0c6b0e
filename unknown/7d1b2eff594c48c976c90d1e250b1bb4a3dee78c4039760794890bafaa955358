using DataAccessLibrary;
using DataHelper;
using PropertyLayers;
using System;
using System.Data;
using System.Data.SqlClient;

namespace DataAccessLayer
{
    public class RejectAllLeadsDLL
    {
        public static DataSet GetRejectAllLeads()
        {
            SqlParameter[] SqlParam = new SqlParameter[0];

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeads_RejectAllLeadsFlag]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }  

        public static void RejectLeads(RejectAllLeadsData lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[8];
            SqlParam[0] = new SqlParameter("@LeadID", lead.LeadID);
            SqlParam[1] = new SqlParameter("@StatusId", lead.StatusId);
            SqlParam[2] = new SqlParameter("@IsParent", lead.IsParent);
            SqlParam[3] = new SqlParameter("@ProductID", lead.ProductID);
            SqlParam[4] = new SqlParameter("@SubStatusID", lead.SubStatusID);
            SqlParam[5] = new SqlParameter("@IsReject", lead.IsReject);
            SqlParam[6] = new SqlParameter("@RejectionReason", lead.RejectionReason);
            SqlParam[7] = new SqlParameter("@UserID", lead.UserId);

            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[RejectLead_RejectAllLeadsFlag]", SqlParam);
        }
    }

}




