﻿//using System;
//using System.Net.Http;
//using System.Threading.Tasks;
//using Amazon.Runtime;
//using Newtonsoft.Json.Linq;
//using Newtonsoft.Json;
//using Amazon;
////using Amazon.Athena;
////using Amazon.Athena.Model;

//public class AwsCredentialsFetcher
//{
//    private static readonly HttpClient client = new HttpClient();

//    public static async Task<AWSCredentials> FetchTemporaryCredentialsAsync()
//    {
//        try
//        {

//            string roleUrl = "http://169.254.169.254/latest/meta-data/iam/security-credentials/matrixlive-api-role";
//            var response = await client.GetStringAsync(roleUrl);

//            var jsonResponse = JObject.Parse(response);

//            var accessKeyId = jsonResponse["AccessKeyId"].ToString();
//            var secretAccessKey = jsonResponse["SecretAccessKey"].ToString();
//            var sessionToken = jsonResponse["Token"].ToString();

//            var credentials = new SessionAWSCredentials(accessKeyId, secretAccessKey, sessionToken);
//            return credentials;
//        }
//        catch (Exception ex) { 
//            Console.WriteLine(ex.ToString());
//            return null;
//        }
//    }
//}


//public class AthenaQueryExecutor
//{
//    private static readonly string region = "ap-south-1";
    
//    private static readonly string outputLocation = "s3://aws-athena-query-results-721537467949-ap-south-1";

//    public static async Task<GetQueryResultsResponse> ExecuteQueryAsync(string query)
//    {
//        //var credentials = await AwsCredentialsFetcher.FetchTemporaryCredentialsAsync();
//        try
//        {


//            var client = new AmazonAthenaClient(RegionEndpoint.GetBySystemName(region));

//            var queryRequest = new StartQueryExecutionRequest
//            {
//                QueryString = query,
//                ResultConfiguration = new ResultConfiguration
//                {
//                    OutputLocation = outputLocation
//                }
//            };

//            var queryResponse = await client.StartQueryExecutionAsync(queryRequest);
//            string queryExecutionId = queryResponse.QueryExecutionId;

//            Console.WriteLine($"Query submitted: {queryExecutionId}");

//            // Wait for the query to complete
//            var queryExecutionRequest = new GetQueryExecutionRequest
//            {
//                QueryExecutionId = queryExecutionId
//            };

//            QueryExecutionStatus queryExecutionStatus = null;
//            do
//            {
//                await Task.Delay(1000); // wait for 1 second
//                var queryExecutionResponse = await client.GetQueryExecutionAsync(queryExecutionRequest);
//                queryExecutionStatus = queryExecutionResponse.QueryExecution.Status;
//            }
//            while (queryExecutionStatus.State == QueryExecutionState.RUNNING);

//            if (queryExecutionStatus.State == QueryExecutionState.SUCCEEDED)
//            {
//                Console.WriteLine("Query succeeded!");
//                // Fetch the results
//                var resultRequest = new GetQueryResultsRequest
//                {
//                    QueryExecutionId = queryExecutionId
//                };

//                var resultResponse = await client.GetQueryResultsAsync(resultRequest);
//                return resultResponse;
     
//            }
//            else
//            {
//                Console.WriteLine($"Query failed: {queryExecutionStatus.StateChangeReason}");
//                return null;
//            }
//        }
//        catch (Exception ex) { 
//            Console.WriteLine($"ATHENA Query exception: {ex}");
//             return null;
//        }
//    }
//}


