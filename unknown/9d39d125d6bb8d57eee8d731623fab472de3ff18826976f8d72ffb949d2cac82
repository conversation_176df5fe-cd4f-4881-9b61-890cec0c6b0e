﻿using Helper;
using MongoConfigProject;
using System.Text;
using System.Timers;

namespace EmailCommunicationBLL
{
    public class CustomerUnsubscription
    {
        private readonly System.Timers.Timer _timer;
        private readonly int interval;
        private readonly short intervalInMinutes;

        public CustomerUnsubscription()
        {
            intervalInMinutes = 30;
            interval = intervalInMinutes * 60 * 1000; // Interval in milliseconds (2 Minute)
            _timer = new(interval);
            _timer.Elapsed += Timer_Elapsed;
            _timer.Start();
        }


        private void Timer_Elapsed(object? sender, ElapsedEventArgs e)
        {
            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _timer.Stop();

                sb.Append("start-");

                bool RunCustomerUnsubscriptionSch = "RunCustomerUnsubscriptionScheduler".AppSettings() == "true";
                if (RunCustomerUnsubscriptionSch == true)
                {

                    CustomerdetailBLL.UpdateCustomerSubscriptionInMatrix((short)(intervalInMinutes+20));
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "CustomerUnsubscription", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _timer.Start();
            }
        }
    }
}