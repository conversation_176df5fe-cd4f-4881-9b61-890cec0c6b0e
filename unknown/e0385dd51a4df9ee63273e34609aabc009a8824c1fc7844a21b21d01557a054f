<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile></DocumentationFile>
    <DocumentationFile></DocumentationFile>
    <CheckForOverflowUnderflow></CheckForOverflowUnderflow>
    <LangVersion>Default</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' " />
  <ItemGroup>
    <PackageReference Include="Confluent.Kafka" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Helper\Helper.csproj" />
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
  </ItemGroup>

</Project>
