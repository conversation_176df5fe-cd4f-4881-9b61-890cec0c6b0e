﻿using System.Text;
using DataAccessLayer;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class HealthAllocation
    {
        private static Timer _allocationtimer;
        public HealthAllocation()
        {
            //allocation queue for allocating data start            
            _allocationtimer = new Timer(1.5 * 60 * 1000);
            _allocationtimer.Elapsed += _allocationtimer_Elapsed;
            _allocationtimer.Start();
            //allocation queue for allocating data end                       
        }
        static async void _allocationtimer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode()) {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _allocationtimer.Stop();

                sb.Append("start-");

                bool RunAllocationScheduler = "RunAllocationScheduler".AppSettings() == "true";
                if (RunAllocationScheduler == true)
                {
                    var ds = AllocationDLL.GetAllocationConfig();

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        //var IsAI = Convert.ToBoolean(ds.Tables[0].Rows[0]["IsAI"]);
                        var startTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day) + TimeSpan.Parse(ds.Tables[0].Rows[0]["startTime"].ToString());
                        var endTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day) + TimeSpan.Parse(ds.Tables[0].Rows[0]["endTime"].ToString());
                        AllocationBLL _Leadallocation = new();
                        if (DateTime.Now >= startTime && DateTime.Now <= endTime)
                            await _Leadallocation.LeadsAllocation_HealthAsync();
                    }
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "HealthallocationJobNew", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _allocationtimer.Start();
            }
        }
    }
}
