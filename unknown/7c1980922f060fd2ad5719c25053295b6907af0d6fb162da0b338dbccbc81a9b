﻿using Allocation.Helpers;
using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;

namespace Allocation.Controllers
{
    [CommAuth]
    [ApiController]
    [Route("allocation/api/[controller]/[action]")]
    public class SMEController : ControllerBase
    {
        readonly ISMEBLL objSMEBLL;
        public SMEController(ISMEBLL _objSMEBLL)
        {
            objSMEBLL = _objSMEBLL;
        }

        [HttpPost]
        public AssignedLeadData AssignLeadToAgent(long leadId)
        {
            return objSMEBLL.AssignLeadToAgent(leadId);
        }

    }
}