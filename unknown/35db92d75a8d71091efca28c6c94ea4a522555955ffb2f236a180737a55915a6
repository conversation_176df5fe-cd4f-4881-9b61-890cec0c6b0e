﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{

    public class CarRejectionHelper
    {
        public static bool RejectNewAndValidCarLeads(ParentDetails ParentLead)
        {


            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF", "RENEWAL" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate", "OnlineAffiliate" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2};

            if (RejectionHelper.IsDateWithinRange(40, 43, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings))
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList)))
            {
                return true;
            }
            return false;
        }

        public static bool Reject60DaysOlderNewCarleads(ParentDetails ParentLead)
        {
            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF", "RENEWAL"};
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate", "OnlineAffiliate" };
            List<Int16> StatusIDList = new List<Int16> {1,2,3,4,11};


            if ((ParentLead.LeadAssignData.Count != 0 && RejectionHelper.IsDateWithinRange(60, 150, ParentLead.LeadAssignData[0].AssignedDate))
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings))
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                && (ParentLead.LeadAssignData.Count != 0 && ParentLead.LeadAssignData[0].AssignToGroupId == 845))
            {
                return true;
            }
            return false;
        }


        public static bool RejectHomChurnLeads(ParentDetails ParentLead)
        {
            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF", "RENEWAL" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate", "OnlineAffiliate" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2, 3, 4, 11 };


            if ((ParentLead.LeadAssignData.Count != 0 && RejectionHelper.IsDateWithinRange(45, 150, ParentLead.LeadAssignData[0].AssignedDate))
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings))
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                && (ParentLead.LeadAssignData.Count != 0 && ParentLead.LeadAssignData[0].AssignToGroupId == 2878))
            {
                return true;
            }
            return false;
        }

        public static bool RejectUnAssignedCarLeads(ParentDetails ParentLead)
        {

            //No check for LD.ParentID is NULL
            List<string> LeadSourceStrings = new List<string> { "RENEWAL" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2 };


            if (RejectionHelper.IsDateWithinRange(5, 10, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                )
            {
                return true;
            }
            return false;
        }
    }
    
}
