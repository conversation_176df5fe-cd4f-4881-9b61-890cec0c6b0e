﻿namespace DataAccessLibrary
{
    public class RedisCollection
    {
        internal static string RevisitCustomers()
        {
            return "OnlineCustomer";
        }

        internal static string RevisitEnquiry()
        {
            return "OnlineEnquiry";
        }

        internal static string VisitLeadId()
        {
            return "VisitLeadId";
        }

        public static string Next5Leads()
        {
            return "Next5Leads";
        }

        internal static string LeadAgentIndex()
        {
            return "LeadId.AgentId.index";
        }

        internal static string RedisKey()
        {
            return "LeadId";
        }

        internal static string CheckIVRFeedBack()
        {
            return "CheckIVRFeedBack";
        }

        internal static string IsCallBackAllowed()
        {
            return "IsCBAllowed";
        }

        internal static string TimeZone()
        {
            return "TimeZone";
        }

        internal static string LeadPriorityConstants()
        {
            return "priorityconfig";
        }
        public static string PredictiveAgent()
        {
            return "PredictiveAgent";
        }
        internal static string PredictiveAgentCode()
        {
            return "PredictiveAgentCode";
        }
        internal static string PredictiveAgentManagerIndex()
        {
            return "AgentId.Manager.Index";
        }
        internal static string PredictiveAsteriskToken()
        {
            return "AsteriskToken";
        }
        internal static string PredictiveAgentCallTrack()
        {
            return "PredictiveAgentCallTrack";
        }
        internal static string AgentIP()
        {
            return "AgentIP";
        }
        internal static string PredictiveAgentContext()
        {
            return "AgentId.Context.Index";
        }
        public static string RealTimeDataContext()
        {
            return "ServerCallStatus";
        }
        public static string MobileStatus()
        {
            return "Mobile";
        }

        internal static string AgentPhone()
        {
            return "AgentPhone";
        }
        public static string CallingList()
        {
            return "CallingList";
        }
        public static string appointmentTypeList()
        {
            return "appointmentTypeList";
        }
        public static string appointmentCityList()
        {
            return "appointmentCityList";
        }
        public static string CityList()
        {
            return "CityList";
        }
        public static string substatusList()
        {
            return "substatusList";
        }
        public static string MongoConfig()
        {
            return "MongoConfig";
        }
        public static string AppToken()
        {
            return "AppToken";
        }
        public static string FOSAppSlotList()
        {
            return "FOSAppSlotList";
        }
        public static string PincodeAutoCompleteList()
        {
            return "PincodeAutoCompleteList";
        }
        public static string AppKey()
        {
            return "AppToken";
        }
        public static string AppoitmentMasterList()
        {
            return "AppoitmentMasterList";
        }
        public static string ChurnLeadModel()
        {
            return "ChurnLeadModel";
        }
        public static string UserChurnLeads()
        {
            return "UserChurnLeads";
        }

        public static string FOSZones()
        {
            return "FOSZones";
        }
        public static string LeadRankRE_Engine()
        {
            return "LeadRankRE_Engine";
        }
        public static string LeadRankRE_Rules()
        {
            return "LeadRankRE_Rules";
        }
        public static string LeadRankRE_RuleConditions()
        {
            return "LeadRankRE_Rules";
        }

        public static string LeadRejectionLogicsDocument()
        {
            return "LeadRejectionLogicsDocument";
        }

        public static string GetAssignToGroupIDsFromDB() 
        {
            return "GetAssignToGroupIDsFromDB";
        }

        public static string ACLMongoConfig()
        {
            return "ACLMongoConfig";
        }

    }
}
