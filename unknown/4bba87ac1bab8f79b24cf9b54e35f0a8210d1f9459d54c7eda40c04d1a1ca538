﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
//using System.Messaging;
using PropertyLayers;
using ReadXmlProject;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.IO;
using MongoDB.Bson;
using Experimental.System.Messaging;

namespace EmailCommunicationBLL
{
    public class MSMQ
    {
        public static string AddInMSMQ<T>(T objData, string QueueName)
        {
            MessageQueue msMq = null;
            try
            {
                if (!MessageQueue.Exists(QueueName))
                {
                    msMq = MessageQueue.Create(QueueName);
                }
                else
                {
                    msMq = new MessageQueue(QueueName);
                    msMq.MessageReadPropertyFilter.Priority = true;
                }
                Message msg1 = new Message(objData);
                msg1.Label = Guid.NewGuid().ToString();
                msg1.Priority = MessagePriority.Normal;
                msMq.Send(msg1);
                return "Mail has been Queued";
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (msMq != null)
                    msMq.Close();
            }
        }
        
    }
}
