﻿using System;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;
using Helper;

namespace DataAccessLibrary
{
    public class SingletonClass
    {

        static MongoDatabase _LoggingDB;
        static MongoDatabase _OneLeadDB;

        private SingletonClass()
        {

        }

        public static MongoDatabase LoggingDB(string MongoDBConnection, string MongoDataBaseName)
        {
            try
            {
                if (_LoggingDB != null)
                {
                    return _LoggingDB;
                }
                else
                {
                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    var connectionString = MongoDBConnection;
                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(MongoDataBaseName);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static MongoDatabase OneLeadDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(OneLeadDB);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}