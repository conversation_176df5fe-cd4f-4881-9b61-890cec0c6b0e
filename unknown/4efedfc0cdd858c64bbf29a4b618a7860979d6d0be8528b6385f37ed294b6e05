﻿using System;
using System.Collections.Generic;
using System.Linq;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using MongoDB.Driver.GridFS;
using System.IO;
using System.Threading;
using MongoDB.Driver.Core.Connections;
using ReadXmlProject;
using Microsoft.Extensions.Configuration;


namespace DataHelper
{
    public class MongoHelper
    {
        MongoDatabase MongoDB;             
        int _mongotimeout=0;
        public ConnectionId ConnectionId { get; }
        public MongoHelper(MongoDatabase db,int mongotimeout=0)
        {
            MongoDB = db;
            this._mongotimeout = mongotimeout;
            if (_mongotimeout == 0)
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                _mongotimeout = Convert.ToInt32(con.GetSection("Communication").GetSection("MongoTimeOut").Value);
            }
                
        }
        public int InsertData<T>(T objData, string CollectionTable)
        {
            //try
            //{
            //    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
            //    var objresult = EmailCollection.Insert(objData);
            //}
            //catch (Exception ex)
            //{
            //    LogIntoFile(objData, ex.ToString(), "SaveData", "MongoHelper");
            //}

            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                    var objresult = EmailCollection.Insert(objData);
                }
                catch (Exception ex)
                {
                    LogIntoFile(objData, ex.ToString(), "SaveData", "MongoHelper");
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                
                
                 throw new MongoConnectionException(ConnectionId, "mongo Timeout");
            }
            else
            {
                return 1;
            }
        }
        public int SaveData<T>(T objData, string CollectionTable)
        {
            try
            {
                Thread t = new Thread(new ThreadStart(
                () =>
                {
                    try
                    {
                        var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                        EmailCollection.Save(objData);
                    }
                    catch (Exception ex)
                    {

                    }
                }));
                t.Start();
                t.Join(_mongotimeout);

                if (t.IsAlive)
                {
                    try
                    {
                        t.Abort();
                    }
                    catch (ThreadAbortException e)
                    {
                    }
                    throw new MongoConnectionException(ConnectionId, "mongo Timeout");
                }
                else
                {
                    return 1;
                }
                //if (objresult.Ok == true)
                //    return 1;
                //else
                //    return 0;
            }
            catch (MongoConnectionException MCEX)
            {
                throw MCEX; // Connection Exception
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public int BulkDataInsert<T>(List<T> objData, string CollectionTable)
        {
            try
            {
                var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                EmailCollection.InsertBatch(objData);
                return 1;
                //if (objresult.Ok == true)
                //    return 1;
                //else
                //    return 0;
            }
            catch (MongoConnectionException MCEX)
            {
                throw MCEX; // Connection Exception
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public List<T> FindAllDocument<T>(string CollectionTable) where T : class
        {
            try
            {
                var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                MongoCursor<T> cursor = EmailCollection.FindAllAs<T>();                
                return cursor.ToList();
            }
            catch (Exception ex)
            {
               throw ex;
            }
        }
        public T FindandModify<T>(FindAndModifyArgs UpdateArgs, string CollectionTable)
        {
            FindAndModifyResult objresult = null;
            //var EmailCollection1 = MongoDB.GetCollection<T>(CollectionTable);
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection(CollectionTable);
                    objresult = EmailCollection.FindAndModify(UpdateArgs);
                }
                catch (Exception ex)
                {

                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                // throw new MongoConnectionException("Timeout");
                throw new MongoConnectionException(ConnectionId, "mongo Timeout");
            }
            else
            {
                if (objresult == null)
                    return default(T);
                else
                    return objresult.GetModifiedDocumentAs<T>();
            }
        }
        public int UpdateDocument(IMongoQuery query, IMongoUpdate update, string CollectionTable, UpdateFlags uf = UpdateFlags.None)
        {
            WriteConcernResult objresult = null;
            try
            {
                Thread t = new Thread(new ThreadStart(
                () =>
                {
                    try
                    {
                        var EmailCollection = MongoDB.GetCollection(CollectionTable);
                        objresult = EmailCollection.Update(query, update, uf);
                    }
                    catch (Exception ex)
                    {

                    }
                }));

                t.Start();
                t.Join(_mongotimeout);

                if (t.IsAlive)
                {
                    try
                    {
                        t.Abort();
                    }
                    catch (ThreadAbortException e)
                    {
                    }
                    return 0;
                }
                else
                {
                   // if (objresult != null && objresult.Ok == true && objresult.DocumentsAffected > 0)
                   if (objresult != null && objresult.DocumentsAffected > 0)
                        return 1;
                    else
                        return 0;
                }
            }
            catch (MongoConnectionException MCEX)
            {
                throw MCEX; // Connection Exception
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return 0;
        }
        public int Delete_ColectionByQyery<T>(IMongoQuery query, string CollectionTable)
        {
            try
            {
                var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);                
                var r=EmailCollection.Remove(query);
                return 1;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public void RemoveDocuments(IMongoQuery query, string CollectionTable)
        {
            var EmailCollection = MongoDB.GetCollection(CollectionTable);
            EmailCollection.Remove(query);            
        }
        public void Reindex(string CollectionTable)
        {
            var EmailCollection = MongoDB.GetCollection(CollectionTable);
            EmailCollection.ReIndex();            
        }

        public List<T> GetDocuments<T>(IMongoQuery query, string CollectionTable, IMongoSortBy strOrderField, int limit) where T : class
        {

            List<T> objlist = new List<T>();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable).Find(query);
                    EmailCollection = EmailCollection.SetSortOrder(strOrderField);
                    if (EmailCollection != null && EmailCollection.Count() > 0)
                        objlist = EmailCollection.Take(limit).ToList();
                }
                catch (Exception ex)
                {

                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }


        public List<T> GetDocuments<T>(IMongoQuery query, string CollectionTable, IMongoSortBy strOrderField, IMongoFields Fields, int skip, int limit) where T : class
        {

            List<T> objlist = new List<T>();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable).Find(query);
                    if (Fields != null)
                        EmailCollection = EmailCollection.SetFields(Fields).SetSortOrder(strOrderField);
                    else
                        EmailCollection = EmailCollection.SetSortOrder(strOrderField);
                    if (EmailCollection != null && EmailCollection.Count() > 0)
                    {
                        if (limit == 0)
                            objlist = EmailCollection.ToList();
                        else
                            objlist = EmailCollection.Skip(skip).Take(limit).ToList();
                    }
                }
                catch (Exception ex)
                {

                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }
        public List<T> GetDocuments<T>(IMongoQuery query, string CollectionTable, int skip=0, int limit=0) where T : class
        {
            List<T> objlist=new List<T>();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    if (limit == 0)
                        objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).ToList();
                    else
                        objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).Skip(skip).Take(limit).ToList();
                }
                catch(Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }        

        public List<T> chkMongoConnection<T>(IMongoQuery query, string CollectionTable) where T : class
        {
            List<T> objlist = new List<T>();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).Take(1).ToList();                 
                }
                //catch(MongoConnectionException mex)
                //{
                //    throw mex;
                //}
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                //catch (MongoConnectionException mex)
                //{
                //    throw mex;
                //}
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }

        public List<T> GetDocuments<T>(IMongoQuery query, IMongoFields IncludeField, string CollectionTable) where T : class
        {
            List<T> objlist = new List<T>();
            try
            {
                objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).SetFields(IncludeField).ToList();
            }
            catch (Exception ex)
            {

            }


           
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    objlist= MongoDB.GetCollection<T>(CollectionTable).Find(query).SetFields(IncludeField).ToList();
                }          
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }                                    
        }        
        public T FindOneDocument<T>(IMongoQuery query, string CollectionTable) where T : new()
        {
            T objt = new T();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {                    
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                    objt = EmailCollection.FindOne(query);            
                }           
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objt;
            }
            else
            {
                return objt;
            }               
        }
        public T FindOneDocument<T>(IMongoQuery query, string CollectionTable,IMongoFields Fields) where T : new()
        {

            T objt = new T();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {                    
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                    objt = EmailCollection.FindOne(query);
                }             
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objt;
            }
            else
            {
                return objt;
            }                           
        }        
        public string GetSclarField(IMongoQuery query, string CollectionTable,string SelectField)
        {
            try
            {
                var EmailCollection = MongoDB.GetCollection(CollectionTable);
                BsonDocument bsonDoc = EmailCollection.FindOne(query);
                if (bsonDoc!=null && bsonDoc.Count() > 0)
                    return Convert.ToString(bsonDoc[SelectField]);
                else
                    return string.Empty;
            }
            //catch (MongoConnectionException MCEX)
            //{
            //    throw MCEX;
            //}
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public List<T> GetUnreadMails<T>(IMongoQuery query, string CollectionTable, IMongoFields Fields)
        {

            List<T> objt = new List<T>();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    objt = MongoDB.GetCollection<T>(CollectionTable).Find(query).SetFields(Fields).ToList();                                        
                }             
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objt;
            }
            else
            {                
                return objt;
            }                           
        }
        public string UploadFiles(Stream stream,string fileName)
        {
            string ID = string.Empty;

            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    MongoGridFSFileInfo gridFsInfo;
                    gridFsInfo = MongoDB.GridFS.Upload(stream, fileName);
                    ID= gridFsInfo.Id.ToString();
                }             
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return ID;
            }
            else
                return ID;
            
        }
        public string GetFile(string id)
        {
            string FileStream = string.Empty;
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    ObjectId oid = new ObjectId(id);
                    var file = MongoDB.GridFS.FindOneById(oid);
                    using (var stream = file.OpenRead())
                    {
                        var bytes = new byte[stream.Length];
                        stream.Read(bytes, 0, (int)stream.Length);
                        FileStream = Convert.ToBase64String(bytes);
                    }      
                }             
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return FileStream;
            }
            else
                return FileStream;
            
        }
        public void DownloadFIle(string id)
        {
            ObjectId oid = new ObjectId(id);
            var file = MongoDB.GridFS.FindOneById(oid);            
            MongoDB.GridFS.Download(file.Name, file);
            //file.OpenRead().BeginWrite((f,0,f.Length,null,null);
            //return Convert.ToBase64String(f);            
        }
        public void RemoveFiles(string id)
        {
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    ObjectId oid = new ObjectId(id);
                    MongoDB.GridFS.DeleteById(oid);
                }            
                catch (Exception ex)
                {
                    
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }                
            }                                          
        }
        public long GetDocumentCount(IMongoQuery query, string CollectionTable)
        {
            try
            {
                long DocCount = 0;
                var Collection = MongoDB.GetCollection(CollectionTable);
                DocCount = Collection.Count(query);
                return DocCount;
            }
            //catch (MongoConnectionException MCEX)
            //{
            //    throw MCEX;
            //}
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public void DropCollection(string CollectionTable)
        {
            var collection = MongoDB.GetCollection(CollectionTable);
            collection.Drop();
        }

        public int SaveParserData<T>(T objData, string CollectionTable)
        {
            try
            {
                Thread t = new Thread(new ThreadStart(
                () =>
                {
                    try
                    {
                        var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                        EmailCollection.Save(objData);
                        string ISMongoErrorLog = "ISMongoErrorLog".AppSettings();
                        

                        if (ISMongoErrorLog.Equals("1"))
                        {

                            LogIntoFile(objData, string.Empty, "SaveData", "MongoHelper");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogIntoFile(objData, ex.StackTrace.ToString(), "SaveData", "MongoHelper");
                    }
                }));
                t.Start();
                t.Join(_mongotimeout);

                if (t.IsAlive)
                {
                    try
                    {
                        t.Abort();
                    }
                    catch (ThreadAbortException e)
                    {
                    }
                    //throw new MongoConnectionException("Timeout");
                    throw new MongoConnectionException(ConnectionId, "mongo Timeout");
                }
                else
                {
                    return 1;
                }
                //if (objresult.Ok == true)
                //    return 1;
                //else
                //    return 0;
            }
            catch (MongoConnectionException MCEX)
            {
                throw MCEX; // Connection Exception
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private void LogIntoFile<T>(T objData, String sMsg, String MethodName, string ApplicationName)
        {
            try
            {
                string ISMongoErrorLog = "ISMongoErrorLog".AppSettings();
                
                if (ISMongoErrorLog.Equals("1") || ISMongoErrorLog.Equals("2"))
                {
                    string returnString = convertToJsonString(objData, 0);
                    String FilePath = "ErrorNInfoLogFilePath".AppSettings();
                    
                    if (!String.IsNullOrEmpty(FilePath.Trim()))
                    {
                        FilePath = FilePath + "//" + DateTime.Now.ToString("ddMMyyyy") + "//" + MethodName + "//";
                        if (!Directory.Exists(FilePath))
                            Directory.CreateDirectory(FilePath);
                        System.IO.File.AppendAllText(FilePath + MethodName + "_" + DateTime.Now.Date.ToString("ddMMyyyy") + "_" + DateTime.Now.Hour.ToString() + "_" + DateTime.Now.Minute.ToString() + ".txt", sMsg + Environment.NewLine + "Json" + returnString);
                    }
                }
            }
            catch(Exception ex)
            {

            }
        }
        
        private string convertToJsonString<T>(T objData,int length)
        {
            string returnString = string.Empty;
            if (objData!=null)
            {
                returnString = objData.ToJson();
                if (length > 0)
                {
                    if (returnString.Length >= length)
                    {
                        returnString = returnString.Substring(0, length-1);
                    }
                }               
            }
            return returnString;
        }
    }
}

