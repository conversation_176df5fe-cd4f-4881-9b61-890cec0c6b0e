﻿using DataAccessLayer;
using DataAccessLibrary;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmailCommunicationBLL
{
    public class MisSellBookings
    {
        public static bool Topic_MissSellData(string result)
        {
            DateTime reqTime = DateTime.Now;
            bool response = false;
            long BookingId = 0;
            sendcommunicationResponse objSend = new sendcommunicationResponse();
            int[] MissSellStatus = { 23, 28, 29, 30 };// 23 - pending with sales, 28- accepted by quality, 29 - rejected by quality, 30 - pending with quality

            try
            {
                MissSellData obj = JsonConvert.DeserializeObject<MissSellData>(result);

                if (MissSellStatus.Contains(obj.MisSellingStatusId) && obj.ProductId == 2)
                {
                    if (obj != null && obj.BookingId > 0)
                    {
                        BookingId = obj.BookingId;
                        if (obj.MisSellingStatusId == 23 || obj.MisSellingStatusId == 28 || obj.MisSellingStatusId == 29)
                        {
                            DataSet ds = Customerdetaildll.GetMissSellCommData(obj.SalesAgentEmployeeId);

                            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                            {
                                obj.TLEmployeeId = ds.Tables[0].Rows[0]["TLEmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["TLEmployeeId"]) : "";
                                obj.TLUserName = ds.Tables[0].Rows[0]["TLUserName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["TLUserName"]) : "";
                                obj.TLEmail = ds.Tables[0].Rows[0]["TLEmail"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["TLEmail"]) : "";
                                obj.AMEmployeeId = ds.Tables[0].Rows[0]["AMEmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["AMEmployeeId"]) : "";
                                obj.AMUserName = ds.Tables[0].Rows[0]["AMUserName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["AMUserName"]) : "";
                                obj.AMEmail = ds.Tables[0].Rows[0]["AMEmail"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["AMEmail"]) : "";
                                obj.ManagerEmployeeId = ds.Tables[0].Rows[0]["ManagerEmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["ManagerEmployeeId"]) : "";
                                obj.ManagerUserName = ds.Tables[0].Rows[0]["ManagerUserName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["ManagerUserName"]) : "";
                                obj.ManagerEmail = ds.Tables[0].Rows[0]["ManagerEmail"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["ManagerEmail"]) : "";
                            }
                        }
                        string qualityusersstring = "MissSellQualityUsers".AppSettings();
                        List<MissSellQualityUsers> qualityusers = JsonConvert.DeserializeObject<List<MissSellQualityUsers>>(qualityusersstring);

                        if (    (!string.IsNullOrEmpty(obj.TLEmail) && !string.IsNullOrEmpty(obj.AMEmail) && !string.IsNullOrEmpty(obj.ManagerEmail) 
                            && (obj.MisSellingStatusId == 23 || obj.MisSellingStatusId == 28 || obj.MisSellingStatusId == 29))
                            || 
                            (!string.IsNullOrEmpty(qualityusersstring) && obj.MisSellingStatusId == 30))
                        {

                            var ToemailList = new List<string>();
                            var CCemailList = new List<string>();
                            StringBuilder Content = new StringBuilder("MissSellHTML".AppSettings());
                            string url = "";

                            if (Content.Length > 0)
                            {
                                Content.Replace("[Reason]", obj.MisSellingReason);
                                Content.Replace("[DeductionAmount]", Convert.ToString(obj.MissellAmount));

                                if (obj.MisSellingStatusId == 23)
                                {
                                    url = "MisSellSalesURL".AppSettings();
                                    ToemailList.Add(obj.TLEmail);
                                    ToemailList.Add(obj.AMEmail);
                                    ToemailList.Add(obj.ManagerEmail);
                                    Content.Replace("[HeadLine]", $"Mis-sell raised by <b>{obj.MisSellRaisedByEmployeeName}</b> <b>({obj.MisSellRaisedByEmployeeId})</b> against <b>{obj.BookingId}</b> for <b>{obj.SalesAgentEmployeeName} ({obj.SalesAgentEmployeeId})</b> ");
                                    Content.Replace("[RemarksByService]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       <b>Remarks By Service: </b>&nbsp;-&nbsp;{obj.ServiceMisSellingComment}</p>");
                                    Content.Replace("[RemarksBySales]", "");
                                    Content.Replace("[RemarksByQuality]", "");
                                    Content.Replace("[EndRemarks]", "<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       Kindly check and revert within 3 days before miss-selling is marked against the advisor.</p>");
                                    Content.Replace("[Status]", "");
                                    Content.Replace("[URL]", $"<p>\r\n<a href=\"{url}\" target=\"_blank\" style=\"font:normal normal 600 20px/32px Roboto;text-decoration:none;color:#0065ff\">Click here to view/take action</a>\r\n                 </p>");
                                }
                                else if (obj.MisSellingStatusId == 30)
                                {
                                    url = "MisSellQualityURL".AppSettings();
                                    foreach (var item in qualityusers)
                                    {
                                        ToemailList.Add(item.EmailId);
                                    }
                                    Content.Replace("[HeadLine]", $"Mis-sell case raise by <b>{obj.MisSellRaisedByEmployeeName} ({obj.MisSellRaisedByEmployeeId})</b> against <b>{obj.BookingId}</b> for <b>{obj.SalesAgentEmployeeName} ({obj.SalesAgentEmployeeId})</b> rejected by Team Leader");
                                    Content.Replace("[RemarksByService]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       <b>Remarks By Service: </b>&nbsp;-&nbsp;{obj.ServiceMisSellingComment}</p>");
                                    Content.Replace("[RemarksBySales]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       <b>Remarks By Sales: </b>&nbsp;-&nbsp;{obj.SalesMisSellingComment}</p>");
                                    Content.Replace("[RemarksByQuality]", "");
                                    Content.Replace("[Status]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\"><b>Status - </b>{obj.MisSellingStatus}</p>");
                                    Content.Replace("[EndRemarks]", "");
                                    Content.Replace("[URL]", $"<p>\r\n<a href=\"{url}\" target=\"_blank\" style=\"font:normal normal 600 20px/32px Roboto;text-decoration:none;color:#0065ff\">Click here to view/take action</a>\r\n                 </p>");
                                }
                                else if (obj.MisSellingStatusId == 28 || obj.MisSellingStatusId == 29)
                                {
                                    ToemailList.Add(obj.TLEmail);
                                    ToemailList.Add(obj.AMEmail);
                                    ToemailList.Add(obj.ManagerEmail);
                                    if (!string.IsNullOrEmpty(obj.MisSellingTLEmailId))
                                        ToemailList.Add(obj.MisSellingTLEmailId);
                                    Content.Replace("[HeadLine]", $"Mis-Sell raised against <b>{obj.BookingId}</b> for <b>{obj.SalesAgentEmployeeName} ({obj.SalesAgentEmployeeId})</b> by <b>{obj.MisSellRaisedByEmployeeName} ({obj.MisSellRaisedByEmployeeId})</b> has been <b>{obj.MisSellingStatus}</b>");
                                    Content.Replace("[RemarksByService]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       <b>Remarks By Service: </b>&nbsp;-&nbsp;{obj.ServiceMisSellingComment}</p>");
                                    Content.Replace("[RemarksBySales]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       <b>Remarks By Sales: </b>&nbsp;-&nbsp;{obj.SalesMisSellingComment}</p>");
                                    Content.Replace("[RemarksByQuality]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\">\r\n       <b>Remarks By Quality: </b>&nbsp;-&nbsp;{obj.QualityMisSellingComment}</p>");
                                    Content.Replace("[Status]", $"<p style=\"text-align:left;font:normal normal 300 24px/32px Roboto;letter-spacing:0;color:#354052;margin-top:0\"><b>Status - </b>{obj.MisSellingStatus}</p>");
                                    Content.Replace("[EndRemarks]", "");
                                    Content.Replace("[URL]", "");
                                }

                                objSend = new sendcommunicationResponse
                                {
                                    LeadId = obj.BookingId,
                                    TriggerName = "ALL_MTX_Em_InternalFeedback",
                                    CommunicationType = 1,
                                    ProductId = 0,
                                    To = ToemailList.ToArray(),
                                    CC = CCemailList.ToArray(),
                                    InputData = new Inputdata
                                    {
                                        Subject = $"Misselling Raised against {obj.BookingId} for {obj.SalesAgentEmployeeName} ({obj.SalesAgentEmployeeId})",
                                        Content = Content.ToString(),
                                    }
                                };
                                LoggingHelper.LoggingHelper.Log(obj.BookingId.ToString(), obj.BookingId, "", "Topic_MissSellDataComm", "FOSallocationJobNew", "KafkaConsumer", JsonConvert.SerializeObject(objSend).ToString(), string.Empty, reqTime, DateTime.Now);
                                CommonHelper.SendCommunication(objSend);
                                response = true;
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", BookingId, ex.ToString(), "Topic_MissSellDataError", "FOSallocationJobNew", "KafkaConsumer", result, ex.ToString(), reqTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", BookingId, "", "Topic_MissSellDataFinal", "FOSallocationJobNew", "KafkaConsumer", result, "", reqTime, DateTime.Now);
            }
            return response;
        }

        
    }
}
