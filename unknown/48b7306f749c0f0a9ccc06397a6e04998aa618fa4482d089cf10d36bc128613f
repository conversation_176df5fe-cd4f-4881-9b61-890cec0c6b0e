﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PropertyLayers;

namespace EmailCommunicationBLL
{
    public interface IAllocationBLL
    {
        short ClearCache(string key);
        Task<string> LeadsAllocation_HealthAsync();
        Task<AllocateLeadResponse> HealthLeadAllocate(AllocateLeadsData allocateLeadsData);
        string Test(string filePath, int? cityId);

        bool GetPayuScoresForAllLeads();

        bool GetPayuIncomeForAllLeads();

    }
}

