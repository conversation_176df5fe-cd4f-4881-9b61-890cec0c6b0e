﻿using DataAccessLibrary;
using DataHelper;
using MongoDB.Driver;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace DataAccessLayer
{
    public class FOSAllocationDLL
    {
        #region Common Methods
        public static DataSet GetDataForDistanceCalculation(Int16 @SlotID, string FOSRegionName, bool SameDay=false)
        {
            
                string Connectionstring = ConnectionClass.ReplicasqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@SlotID", @SlotID);
                SqlParam[1] = new SqlParameter("@FOSRegionName", @FOSRegionName);
                SqlParam[2] = new SqlParameter("@SameDay", SameDay);
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[FOS].[GetDataForDistanceCalculation_V1_New]", SqlParam);
            
        }
        public static void InsertDistancesToSql(DistanceInfo odistanceInfo)
        {
                SqlParameter[] sqlParam = null;
                sqlParam = new SqlParameter[6];
                sqlParam[0] = new SqlParameter("@LeadID", odistanceInfo.LeadID);
                sqlParam[1] = new SqlParameter("@UserID", odistanceInfo.UserID);
                sqlParam[2] = new SqlParameter("@Distance", odistanceInfo.Distance);
                sqlParam[3] = new SqlParameter("@CurrentLocationDistance", odistanceInfo.CurrentLocationDistance);
                sqlParam[4] = new SqlParameter("@SlotId", odistanceInfo.SlotId);
                sqlParam[5] = new SqlParameter("@Zone", odistanceInfo.LeadZone);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[UPdateAgentCustomerDistance]", sqlParam);
        }

        public static void LogAgentData(long userID, short slotLeadCount, short freshAssignSlotLeadCount, short slotID)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@UserID", userID);
            SqlParam[1] = new SqlParameter("@SlotLeadCount", slotLeadCount);
            SqlParam[2] = new SqlParameter("@SlotFreshLeadCount", freshAssignSlotLeadCount);
            SqlParam[3] = new SqlParameter("@SlotId", slotID);
            
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[UPdateAgentFOSAllocationTracking]", SqlParam);

        }

        public static DataSet CityGroupMappingAllocation()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[CityGroupMappingAllocation]");
        }


        #endregion


    }
}