﻿using System;
using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace Helper
{
   
    public static class ExecuteString
    {
        public static async Task<T> ExecuteCodeAsync<T>(string code, List<string> imports, object globalData)
        {
            ScriptOptions options = ScriptOptions.Default;
            options = options.AddReferences(typeof(Console).Assembly);
            options = options.AddImports("System");
            imports.ForEach(import => options.AddImports(import));

            try
            {
                var value = (T)await CSharpScript.EvaluateAsync(code, options, globals: globalData);
                return value;
            }
            catch (CompilationErrorException ex)
            {
                Console.WriteLine("Compilation error:");
                foreach (var diagnostic in ex.Diagnostics)
                {
                    Console.WriteLine(diagnostic);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing code: {ex.Message}");
            }
            return default;
        }
    }
}

