﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class CityZone
    {
        [DataMember]
        public string ZoneId { get; set; }
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public List<Coordinate> Coordinates { get; set; }
        [DataMember]
        public string City { get; set; }
        [DataMember]
        public int CityId { get; set; }
        [DataMember]
        public string State { get; set; }
        [DataMember]
        public int StateId { get; set; }
        [DataMember]
        public string FOSRegionName { get; set; }
    }

    [DataContract]
    public class Coordinate
    {
        [DataMember]
        public double Latitude, Longitude;
        public Coordinate(double Lat, double Long)
        {
            this.Latitude = Lat;
            this.Longitude = Long;
        }
    }

    [DataContract]
    public class Line
    {
        public Coordinate p1, p2;
        public Line(Coordinate p1, Coordinate p2)
        {
            this.p1 = p1;
            this.p2 = p2;
        }
    }

    public class NriCityTimeZone
    {
        public string NriCity { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string TimeZone { get; set; }
        public string TimeDiffFromIST { get; set; }
    }
}

