﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class HealthRejectionHelper
    {
        public static bool RejectCorpLeadsOlderThan120days(ParentDetails ParentLead)
        {


            List<string> LeadSourceStrings = new List<string> { "CrossSell" };
            List<Int16> StatusIDList = new List<Int16> {1,2,3,4,11};
            List<string> UTM_SourceStrings = new List<string> { "HealthOnCorp", "visithealth-teleconsult" };

            if (RejectionHelper.IsDateWithinRange(120, 200, ParentLead.CreatedOn)
                && (RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList)))
            {
                return true;
            }
            return false;
        }

        public static bool RejectUnAssignedHealthLeads(ParentDetails ParentLead)
        {

            //No check for LD.ParentID is NULL
            List<string> LeadSourceStrings = new List<string> { "RENEWAL" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2 };


            if (RejectionHelper.IsDateWithinRange(5, 10, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                )
            {
                return true;
            }
            return false;
        }
    }
}
