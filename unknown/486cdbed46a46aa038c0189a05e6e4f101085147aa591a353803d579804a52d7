﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class RejectionHelper
    {
        #region Common Methods
        public static bool IfFutureCustomerCBExist(Int16 CallBackType, DateTime EventDate)
        {
            if (EventDate > DateTime.Now) return true;
            //Customer
            
            return false;
        }

        public static bool IfFutureAppointmentExist(DateTime AppointmentDateTime)
        {
            if (AppointmentDateTime > DateTime.Now.AddDays(-1).Date) 
                return true;

            return false;
        }


        public static int TalkTimeInLastNDays(List<CallDataCDH> CallHistory, int n)
        {
            if (CallHistory == null) return 0;

            int TalkTimeInLastNDays = 0;

            foreach (CallDataCDH call in CallHistory)
            {
                if (call.CallDate > DateTime.Now.AddDays(-n).Date)
                {
                    TalkTimeInLastNDays += call.TalkTime;
                }
            }

            return TalkTimeInLastNDays;
        }

        public static int CallCountInLastNDays(List<CallDataCDH> CallHistory, int n)
        {
            if (CallHistory == null) return 0;
            int CallsInLastNDays = 0;

            foreach (CallDataCDH call in CallHistory)
            {
                if (call.CallDate > DateTime.Now.AddDays(-n).Date)
                {
                    CallsInLastNDays++;
                }
            }

            return CallsInLastNDays;
        }

        public static int CountValidNotAnsweredCalls(List<CallDataCDH> CallHistory)
        {
            if (CallHistory == null) return 0;
            int NumberOfValidNotAnsweredCalls = 0;
            List<string> TwoWayCall = new List<string>();
            TwoWayCall.Add("twowaycall");

            foreach (CallDataCDH call in CallHistory)
            {
                if(!(CompareStrings(call.Context, TwoWayCall)) && call.Duration > 2 && call.TalkTime == 0)
                    NumberOfValidNotAnsweredCalls++;

            }

            return NumberOfValidNotAnsweredCalls;
        }

        public static int CountValidNotAnsweredCalls_RENEWAL(List<CallDataCDH> CallHistory, ParentDetails ParentLead)
        {
            if (CallHistory == null) return 0;
            int NumberOfValidNotAnsweredCalls = 0;
            List<string> TwoWayCall = new List<string>();
            TwoWayCall.Add("twowaycall");

            foreach (CallDataCDH call in CallHistory)
            {
                if (!(CompareStrings(call.Context, TwoWayCall)) && call.Duration > 2 && call.TalkTime == 0 && call.CallDate > ParentLead.PolicyExpiryDate)
                    NumberOfValidNotAnsweredCalls++;

            }

            return NumberOfValidNotAnsweredCalls;
        }

        public static bool CheckIfLastNValidCallsNotAnswered(List<CallDataCDH> CallHistory, int n)
        {
            if (CallHistory == null) return false;
            int NACount = 0;

            int validCallIndex = 0;
            int j = 0;
            List<string> TwoWayCall = new List<string>();
            TwoWayCall.Add("twowaycall");

            while (validCallIndex < n && j < CallHistory.Count)
            {
                if (CallHistory[j].Duration > 2 && !(CompareStrings(CallHistory[j].Context, TwoWayCall)))
                {
                    if (CallHistory[j].TalkTime == 0) NACount++;
                    validCallIndex++;
                }

                j++;
            }

            return NACount == n;
        }

        #endregion

        #region Generic Methods
        public static double GetDaysSince(DateTime date)
        {
            return (DateTime.Now - date).TotalDays;
        }

        public static bool IsDateWithinRange(int MinThreshold, int MaxThreshold, DateTime CreatedOnDate)
        {
            DateTime ToDate = DateTime.Now.AddDays(-MinThreshold).Date;
            DateTime FromDate = DateTime.Now.AddDays(-MaxThreshold).Date;
            
            if (CreatedOnDate >= FromDate && CreatedOnDate < ToDate) return true;

            return false;
        }

        public static bool DaysSinceLastCallBetween(int MinThreshold, int MaxThreshold, DateTime LastCallDate)
        {
            double LastCallDays = GetDaysSince(LastCallDate);
            if (LastCallDays > MinThreshold && LastCallDays < MaxThreshold) return true;

            return false;
        }

        public static bool TalkTimeBetween(int MinThreshold, int MaxThreshold, int Talktime)
        {
            if (Talktime > MinThreshold && Talktime < MaxThreshold) return true;

            return false;
        }

        public static bool CompareStrings(string Str,List<string> StrList) 
        {
            for (int i = 0; i < StrList.Count; i++)
            {
                StrList[i] = StrList[i].ToUpper();
            }

            Str = Str.ToUpper();
            if (StrList != null && StrList.Contains(Str)) return true;
            
            return false;
        }

        public static bool CompareInts(Int16 Integer, List<Int16> IntegerList)
        {
            if (IntegerList != null && IntegerList.Contains(Integer)) return true;
            return false;
        }

        #endregion

    }
}
