﻿using System;
using System.Reflection;
using Newtonsoft.Json;
using PropertyLayers;
using System.Collections.Generic;
using System.Dynamic;
using DataAccessLayer;
using MongoConfigProject;
using System.Linq;

namespace EmailCommunicationBLL
{
    public static class HealthLeadRankRuleEngine
    {
        private static readonly int productId = 2;

        public static string Execute(string engineName, LeadDetails leadDetails)
        {
            string error = string.Empty;
            List<dynamic> executionLog = new();
            string output = "-1";
            //UpdateLeadProperties();
            var requestTime = DateTime.Now;
            try
            {
                InsertKeyValueLog(executionLog, "Engine", engineName);
                InsertKeyValueLog(executionLog, "Lead", leadDetails);

                output = EvaluateRules(engineName, executionLog, leadDetails);
                // Output is a leadProperty

                List<string> HealthREOutputAsProperty = "HealthREOutputAsProperty".AppSettings().Split(",").ToList();
                if (HealthREOutputAsProperty.Contains(output)) {
                    output = leadDetails[output].ToString();
                }
            }
            catch (Exception e)
            {
                error = e.ToString();
                output = "-2";
            }
            finally
            {
                // Console.WriteLine($"{leadDetails.LeadId} - {output}:  {JsonConvert.SerializeObject(executionLog)}");
                if ("DebugRuleEngine".AppSettings() == "true")
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", leadDetails.LeadId, error, "RuleEngine-Execute", "Allocation", $"{engineName}-Engine", JsonConvert.SerializeObject(executionLog), JsonConvert.SerializeObject(output), requestTime, DateTime.Now);
                }
                else if (error != "")
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", leadDetails.LeadId, error, "RuleEngine-Execute", "Allocation", $"{engineName}-Engine", "", JsonConvert.SerializeObject(output), requestTime, DateTime.Now);
                }
            }

            return output;
        }
        private static string EvaluateRules(string engineName, List<dynamic> executionLog, LeadDetails leadDetails)
        {
            var rules = GetRulesForEngine(engineName, leadDetails);

            if (rules == null || rules.Count == 0)
            {
                InsertKeyValueLog(executionLog, "No Rule/engine", $"{engineName} is not defined or has no Rules");
            }
            else 
            {
                bool isBaseConditionValid = CheckBaseCondition(engineName, executionLog, leadDetails);

                foreach (var rule in rules)
                {
                    InsertKeyValueLog(executionLog, "Rule start", rule.RuleName);

                    bool ruleMatched = true;
                    // Rule with No Condition 
                    // Last rule will be default rule, if no condition is present, always valid
                    // If base condition is present, consider base condition only
                    if (rule != rules[rules.Count - 1] && rule.Conditions == null && rule.CheckBaseCondition == false)
                    {
                        InsertKeyValueLog(executionLog, "No Conditions for Rule", $"{rule.RuleName} has no Conditions");
                        continue;
                    }
                    else if (rule == rules[rules.Count - 1] && rule.Conditions == null && (!rule.CheckBaseCondition || (rule.CheckBaseCondition && isBaseConditionValid)))
                    {
                        // default rule.. without any condition
                        return rule.RuleOutput;
                    }
                    if (rule.CheckBaseCondition && !isBaseConditionValid)
                    {
                        ruleMatched = false;
                        InsertKeyValueLog(executionLog, "Rule Failed: BaseCondition-Failed", $"{rule.RuleName} failed at base-condition");
                    }
                    else if (rule.CheckBaseCondition)
                    {
                        InsertKeyValueLog(executionLog, "BaseCondition-passed", rule.RuleName);
                    }
                    List<RuleCondition> conditions = rule.Conditions ?? new();

                    foreach (var ruleCondition in conditions)
                    {
                        bool isConditionValid = false;


                        // if rule gets failed at basecondition, break without checking further conditions
                        if (!ruleMatched) break;

                        isConditionValid = CheckCondition(ruleCondition, leadDetails);
                        ruleMatched = ruleMatched && isConditionValid;
                        if (ruleMatched == false)
                        {
                            InsertKeyValueLog(executionLog, "Rule Failed", $"{rule.RuleName} failed at condition {ruleCondition.Property} {ruleCondition.Operator} {ruleCondition.ReferenceValue}");
                            break;
                        }
                    }

                    if (ruleMatched)
                    {
                        InsertKeyValueLog(executionLog, "Rule Matched", rule.RuleName);
                        return rule.RuleOutput;
                    }
                }
            }

            return "-1";
        }



        private static bool CheckCondition(RuleCondition ruleCondition, LeadDetails leadDetails)
        {
            bool isValid = ConditionCheckerFactory.ValidateCondition(ruleCondition.PropertyType, ruleCondition, leadDetails);
            return isValid;
        }

        private static bool CheckBaseCondition(string engineName, List<dynamic> executionLog, LeadDetails leadDetails)
        {
            // PedIds
            var pedIdsList = (leadDetails.PEDTypes != "" && leadDetails.PEDTypes != null) ? leadDetails.PEDTypes.Split(',').Select(int.Parse) : null;
            int[] excludedIds = { 1000, 1002, 1003, 1004, 1005, 1006 };
            bool containsExcludedPEDIds = pedIdsList != null && pedIdsList.Any(id => excludedIds.Contains(id));

            switch (engineName)
            {
                case Engines.CheckCoreLeads:
                    if ("PB,PBMOBILE,WHATSAPP".Contains(leadDetails.LeadSource?.ToUpper()) || (leadDetails.LeadSource.ToUpper() == "PBMOBILEAPP" && leadDetails.Utm_term.ToUpper() != "AUTO_LEAD"))
                    {
                        return true;
                    }
                    break;
                case Engines.CoreRanking1:
                case Engines.CoreRanking2:
                    if (leadDetails.Utm_source?.ToLower() != "whatsapp_crm_sales")
                    {
                        return true;
                    }
                    break;
                case $"{Engines.RanksCoreRankingN}1":
                case $"{Engines.RanksCoreRankingN}2":
                case $"{Engines.RanksCoreRankingN}3":
                case $"{Engines.RanksCoreRankingN}4":
                    if (leadDetails.IsPED == true && !containsExcludedPEDIds)
                    {
                        return true;
                    }
                    break;

                default:
                    break;
            }
            return false;
        }        

        public static List<AllocationRule> GetRulesForEngine(string engineName, LeadDetails leadDetails)
        {
            var rules = AllocationDLL.GetRulesForEngine(engineName, leadDetails.ProductID);
            return rules;
        }

        public static List<dynamic> GetLeadProperties()
        {
            Type LeadDetailsType = typeof(LeadDetails);
            List<dynamic> LeadDetailsProperties = new List<dynamic>();
            foreach (PropertyInfo property in LeadDetailsType.GetProperties())
            {
                dynamic propertyObj = new ExpandoObject();
                propertyObj.name = property.Name;
                propertyObj.type = property.PropertyType.Name;

                LeadDetailsProperties.Add(propertyObj);
            }
            return LeadDetailsProperties;
        }
        private static void UpdateLeadProperties()
        {
            Type LeadDetailsType = typeof(LeadDetails);
            List<dynamic> LeadDetailsProperties = new List<dynamic>();
            foreach (PropertyInfo property in LeadDetailsType.GetProperties())
            {
                dynamic propertyObj = new ExpandoObject();
                propertyObj.name = property.Name;
                propertyObj.type = property.PropertyType.Name;

                LeadDetailsProperties.Add(propertyObj);
            }
        }

        #region  Utility
        private static void InsertKeyValueLog(List<dynamic> executionLog, string key, dynamic value)
        {
            try
            {
                dynamic keyValueWithDateTime = new ExpandoObject();

                if (value is not string)
                {
                    // serialize non-string value
                    try
                    {
                        value = JsonConvert.SerializeObject(value);
                    }
                    catch (Exception error)
                    {
                        key += "_Err";
                        value = error.ToString();
                    }
                }

                try
                {
                    // Add datetime with each log
                    keyValueWithDateTime.key = key;
                    keyValueWithDateTime.value = value;
                    keyValueWithDateTime.ts = DateTime.Now;
                    //keyValueWithDateTime = keyValueWithDateTime;
                }
                catch
                {
                    keyValueWithDateTime = value;
                }
                executionLog.Add(keyValueWithDateTime);
            }
            catch (Exception e)
            {
                Console.WriteLine("InsertKeyValueLog ERR: " + e.ToString());
            }
        }
        //private static dynamic ParsePropertyValue(dynamic property, string propertyType)
        //{
        //    try
        //    {
        //        return propertyType switch
        //        {
        //            //"String" => property.toString(),
        //            "Int16" => Convert.ToInt16(property),
        //            "Int32" => Convert.ToInt32(property),
        //            "Int64" => Convert.ToInt64(property),
        //            "Byte" => Convert.ToSByte(property),
        //            "DateTime" => Convert.ToDateTime(property),
        //            "Decimal" => Convert.ToDecimal(property),
        //            "Boolean" => Convert.ToBoolean(property),
        //            _ => property,
        //        };
        //    }
        //    catch (Exception err)
        //    {
        //        Console.WriteLine($"ParsePropertyValue ERROR: {err}");
        //        return property;
        //    }
        //}

        #endregion
    }
}

