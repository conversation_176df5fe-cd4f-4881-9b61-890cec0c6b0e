﻿using System.Text;
using DataAccessLayer;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class TermPayUScheduler
    {
        private static Timer _termPayUtimer;
        public TermPayUScheduler()
        {
            //allocation queue for allocating data start            
            _termPayUtimer = new Timer(1 * 60 * 60 * 1000);
            _termPayUtimer.Elapsed += _termPayu_timer_Elapsed;
            _termPayUtimer.Start();
            //allocation queue for allocating data end                       
        }
        static async void _termPayu_timer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _termPayUtimer.Stop();

                sb.Append("start-");

                bool RunTermPayUScheduler = "RunTermPayUScheduler".AppSettings() == "true";
                string startSpan = "RunTermPayUSchedulerTime".AppSettings().Split(" - ")[0];
                string endSpan = "RunTermPayUSchedulerTime".AppSettings().Split(" - ")[1];
                if (RunTermPayUScheduler == true)
                {
                    var ds = AllocationDLL.GetAllocationConfig();

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        var startTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day) + TimeSpan.Parse(startSpan);
                        var endTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day) + TimeSpan.Parse(endSpan);

                        if (DateTime.Now >= startTime && DateTime.Now <= endTime)
                            TermPayURanksBLL.GetTermPayURanks();
                    }
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "TermPayUScheduler", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _termPayUtimer.Start();
            }
        }
    }
}
