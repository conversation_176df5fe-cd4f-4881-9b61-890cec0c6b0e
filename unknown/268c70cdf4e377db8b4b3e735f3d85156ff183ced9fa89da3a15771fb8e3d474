using DataAccessLayer;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;



namespace EmailCommunicationBLL
{
    public class RejectAllLeadsBLL: IRejectAllLeadsBLL
    {
        public static void RejectAllLeads()
        {
            DateTime dt = DateTime.Now;
            string exception = string.Empty;
            int count = 0;
            try
            {
                DataSet ds = RejectAllLeadsDLL.GetRejectAllLeads();
                if (ds != null)
                {
                    foreach (DataRow leadRow in ds.Tables[0].Rows)
                    {

                        RejectAllLeadsData lead = new();
                        var leadId = leadRow["TblLeadId"] == null || leadRow["TblLeadId"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(leadRow["TblLeadId"]);
                      
                        try
                        {
                            lead.LeadID = leadId;
                            lead.StatusId = leadRow["StatusId"] == null || leadRow["StatusId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["StatusId"]);
                            lead.IsParent = leadRow["IsParent"] == null || leadRow["IsParent"] == DBNull.Value ? Convert.ToBoolean(0) : Convert.ToBoolean(leadRow["IsParent"]);
                            lead.ProductID = leadRow["ProductID"] == null || leadRow["ProductID"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["ProductID"]);
                            lead.SubStatusID = leadRow["SubStatusID"] == null || leadRow["SubStatusID"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(leadRow["SubStatusID"]);
                            lead.IsReject = leadRow["IsReject"] == null || leadRow["IsReject"] == DBNull.Value ? Convert.ToBoolean(0) : Convert.ToBoolean(leadRow["IsReject"]);
                            lead.RejectionReason = leadRow["RejectionReason"] == null || leadRow["RejectionReason"] == DBNull.Value ? "" : leadRow["RejectionReason"].ToString();
                            lead.UserId = leadRow["UserID"] == null || leadRow["UserID"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(leadRow["UserID"]);

                            RejectAllLeadsDLL.RejectLeads(lead);
                            count++;
                        }
                        catch (Exception ex)
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.ToString(), "GetRejectAllLeads", "Allocation", "RejectAllLeadsBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("",0, ex.ToString(), "RejectAllLeads-Error", "Allocation", "RejectAllLeadsBLL", "", count.ToString(), dt, DateTime.Now);
            }

            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, exception, "RejectAllLeads", "Allocation", "RejectAllLeadsBLL", string.Empty, count.ToString(), dt, DateTime.Now);
            }
        }
    }
}
