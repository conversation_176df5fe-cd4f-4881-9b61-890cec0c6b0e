using System.Text;
using DataAccessLayer;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class PushMultiProdLead
    {
        private static Timer _allocationtimer;
        public PushMultiProdLead()
        {        
            _allocationtimer = new Timer(5 * 60 * 1000);
            _allocationtimer.Elapsed += _pushMultiProdLead_Elapsed;
            _allocationtimer.Start();
        }
        static async void _pushMultiProdLead_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode()) {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _allocationtimer.Stop();

                sb.Append("start-");

                bool RunPushMultiProdLeadScheduler = "RunPushMultiProdLeadScheduler".AppSettings() == "true";
                if (RunPushMultiProdLeadScheduler == true)
                {
                    PushMultiProdLeadBLL _PushMultiProdLeadBLL = new();
                    _PushMultiProdLeadBLL.PushMultiProdLeadInRedis();
                }
                else
                {
                    sb.Append("Push Multi Product Lead Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "PushMultiProdLeadJobNew", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _allocationtimer.Start();
            }
        }
    }
}
