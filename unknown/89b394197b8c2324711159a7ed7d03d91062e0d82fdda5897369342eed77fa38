﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using Google.Apis.Services;
using Google.Apis.Util.Store;
using System.IO;
using System.Threading;
using System.Reflection;

namespace DataHelper
{
    public class GoogleDocsHelper
    {
        static string[] Scopes = { SheetsService.Scope.SpreadsheetsReadonly };
        static string ApplicationName = "Google Sheets API .NET Quickstart";

        public static List<Dictionary<string, string>> ReadSheet(string spreadsheetId, short Columns, string range)
        {
            List<Dictionary<string, string>> Records = new List<Dictionary<string, string>>();
            UserCredential credential;
            string filePath = AppDomain.CurrentDomain.BaseDirectory + "credentials.json";
            Console.WriteLine(filePath);


            using (var stream =
                new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                // The file token.json stores the user's access and refresh tokens, and is created
                // automatically when the authorization flow completes for the first time.
                string credPath = AppDomain.CurrentDomain.BaseDirectory + "token.json";
                credential = GoogleWebAuthorizationBroker.AuthorizeAsync(
                    GoogleClientSecrets.Load(stream).Secrets,
                    Scopes,
                    "user",
                    CancellationToken.None,
                    new FileDataStore(credPath, true)).Result;
                Console.WriteLine("Credential file saved to: " + credPath);
            }

            // Create Google Sheets API service.
            var service = new SheetsService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = ApplicationName,
            });

            // Define request parameters.
            //String spreadsheetId = "1zcein7UZ4gfTgRxl-a_1XgqwVVQLx67RqPAMHawpakI";
            //String range = "D:D";
            SpreadsheetsResource.ValuesResource.GetRequest request =
                    service.Spreadsheets.Values.Get(spreadsheetId, range);

            ValueRange response = request.Execute();
            IList<IList<Object>> values = response.Values;

            IList<Object> firstRow = null;
                
            if (values != null && values.Count > 0)
            {
                foreach (var row in values)
                {
                    Dictionary<string, string> data = new Dictionary<string, string>();
                    if (firstRow == null)
                    {
                        firstRow = row;
                    }
                    else 
                    {
                        for (var i = 0; i < Columns; i++)
                        {
                            string str = Convert.ToString(firstRow[i]);
                            data.Add(str.Replace(" ","").Substring(0, Math.Min(12, str.Length)), Convert.ToString(row[i]));
                        }

                        Records.Add(data);
                    }
                }
            }
            else
            {
                Console.WriteLine("No data found.");
            }
            
            return Records;
        }
    }
}
