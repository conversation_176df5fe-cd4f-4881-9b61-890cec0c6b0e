<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Athena" Version="3.7.304.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\DataHelper\DataHelper.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\EmailCommunicationBLL\EmailCommunicationBLL.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\Helper\Helper.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\LoggingHelper\LoggingHelper.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\ReadXmlProject\ReadXmlProject.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
   
    <ProjectReference Include="..\MongoConfigProject\MongoConfigProject.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Remove="Schedulers\" />
    <None Remove="Helpers\" />
  </ItemGroup>
<ItemGroup>

    <None Update="QA_ConfigValues.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>

    <None Update="ConfigValues.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
	
  </ItemGroup>
<ItemGroup>
  <Folder Include="Helpers\" />
</ItemGroup>
</Project>
