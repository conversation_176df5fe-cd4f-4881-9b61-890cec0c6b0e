﻿using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;



namespace LeadRejection.Controllers
{

    [ApiController]
    [Route("allocation/api/[controller]/[action]")]
    public class LeadRejectionController : ControllerBase
    {
    
        readonly ILeadRejectionBLL LeadRejectionBLL;

        public LeadRejectionController(ILeadRejectionBLL _LeadRejectionBLL)
        {
            
            LeadRejectionBLL = _LeadRejectionBLL;
        }
        [HttpGet]
        public bool LeadRejectionBySystem()
        {
            return LeadRejectionBLL.LeadRejectionBySystem();
            return false;
            
            
        }
    }
}
