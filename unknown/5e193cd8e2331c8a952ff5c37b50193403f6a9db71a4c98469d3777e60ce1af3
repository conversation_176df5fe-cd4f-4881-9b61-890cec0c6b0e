﻿using System.Text;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class FosAllocation
    {
        private static Timer _fosAllocationTimer = new();
        public FosAllocation()
        {
            //allocation queue for allocating data start            
            _fosAllocationTimer = new Timer(10 * 60 * 1000);
            _fosAllocationTimer.Elapsed += _fosallocationtimer_Elapsed_method;
            _fosAllocationTimer.Start();
            //allocation queue for allocating data end                       
        }
        static void _fosallocationtimer_Elapsed_method(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _fosAllocationTimer.Stop();

                sb.Append("start-");

                bool RunAllocationScheduler = "RunFosAllocationScheduler".AppSettings() == "true";
                string startSpan = "RunFosAllocationSchedulerTime".AppSettings().Split(" - ")[0];
                string endSpan = "RunFosAllocationSchedulerTime".AppSettings().Split(" - ")[1];
                if (RunAllocationScheduler == true)
                {
                    var startTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day) + TimeSpan.Parse(startSpan);
                    var endTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day) + TimeSpan.Parse(endSpan);
                    FOSAllocationBLL _Fosallocation = new();
                    if (DateTime.Now >= startTime && DateTime.Now <= endTime)
                        _Fosallocation.FOSLeadAllocation();
                    
                    //if (DateTime.Now.Minute >= 45 && DateTime.Now.Minute < 55)
                    _Fosallocation.FOSLeadAllocation(sameDayAllocation:true);
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "FOSallocationJobNew", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _fosAllocationTimer.Start();
            }
        }
    }
}
