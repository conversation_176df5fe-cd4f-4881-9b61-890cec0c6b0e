﻿using System;
using MongoDB.Driver;
using MongoDB.Driver.Core.Connections;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading;

namespace DataHelper
{
    public class MongoHelperV2
    {
        IMongoDatabase MongoDB;
        int _mongotimeout = 0;
        public ConnectionId ConnectionId { get; }
        public MongoHelperV2(IMongoDatabase db, int mongotimeout = 0)
        {
            MongoDB = db;
            this._mongotimeout = mongotimeout;
            if (_mongotimeout == 0)
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                _mongotimeout = Convert.ToInt32(con.GetSection("Communication").GetSection("MongoTimeOut").Value);
            }

        }

        public List<T> GetDocuments<T>(FilterDefinition<T> filter, string CollectionTable, int skip = 0, int limit = 0) where T : class
        {
            List<T> objlist = new List<T>();
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    if (limit == 0)
                        objlist = MongoDB.GetCollection<T>(CollectionTable).Find(filter).ToList();
                    else
                        objlist = MongoDB.GetCollection<T>(CollectionTable).Find(filter).Skip(skip).Limit(limit).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }
    }
}

