﻿using Amazon.SQS;
using Amazon.SQS.Model;
using System;

namespace DataHelper
{
    public class AmazonSqs
    {
        public void SQSSendMessage<T>(string Url, T MsgObj, int DelaySeconds = 0)
        {
            //Console.WriteLine("Enter SQSSendMessage.");
            string SqsMsg = Newtonsoft.Json.JsonConvert.SerializeObject(MsgObj);
            using (var client = new AmazonSQSClient(Amazon.RegionEndpoint.APSouth1))
            {
                var request = new SendMessageRequest()
                {
                    QueueUrl = Url,
                    MessageBody = SqsMsg,
                    DelaySeconds = DelaySeconds,
                };


                var response =  client.SendMessageAsync(request);
            }
        }
    }
}
