﻿using System;
using System.Linq;
using System.Data;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using System.Text.RegularExpressions;

namespace Helper
{
    public static class CoreCommonMethods
    {


        public static long ToSafeLong(this String Input)
        {
            if (Input.Contains("."))
            {
                float i;
                float.TryParse(Input, out i);
                return (long)i;
            }
            else
            {
                long i = 0;
                long.TryParse(Input, out i);
                return i;
            }
        }
        public static string[] Split2(this String Input, string split)
        {
            return Input.Split(split).Where(t => !string.IsNullOrEmpty(t)).ToArray();
        }
        public static int ToSafeInt(this String Input)
        {
            if (Input.Contains("."))
            {
                float i;
                float.TryParse(Input, out i);
                return (int)i;
            }
            else
            {
                int i = 0;
                int.TryParse(Input, out i);
                return i;
            }
        }
        public static bool ToSafeBool(this String Input)
        {
            if (string.IsNullOrEmpty(Input))
            {
                return false;
            }
            else
            {
                bool result;
                if (bool.TryParse(Input, out result))
                {
                    return result;
                }
                else
                {
                    return false;
                }
            }
        }
        public static string NullSafeReplace(this string input, char replace, char replaceWith)
        {
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }
            else
            {
                return input.Replace(replace, replaceWith);
            }
        }
        public static string ReadValue(this DataRow row, string fieldName)
        {
            try
            {
                return row[fieldName].ToString();
            }
            catch
            {
                return string.Empty;
            }
        }

        public static string DataTableToJSONWithJSONNet(DataTable table)
        {
            string JSONString = string.Empty;
            JSONString = JsonConvert.SerializeObject(table);
            return JSONString;
        }
        public static bool IsValidString(string input)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(input) && input != "undefined" && input != "null")
                result = true;
            return result;
        }
        public static string Base64Decode(string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }
        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }
        public static string GetEnvironmentVar()
        {
            string Enviornment = string.Empty;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            Enviornment = Environment.GetEnvironmentVariable("MATRIX_ASPNETCORE_ENVIRONMENT");

            if (String.IsNullOrEmpty(Enviornment) || Enviornment.ToLower() == "production")
                Enviornment = con.GetSection("Communication").GetSection("Environment").Value.ToString();
            return Enviornment;
        }
        public static bool IsDevMode()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            //string Enviornment = Environment.GetEnvironmentVariable("MATRIX_ASPNETCORE_ENVIRONMENT");
            //if (String.IsNullOrEmpty(Enviornment) || Enviornment.ToLower() != "LIVE")
            //    Enviornment = con.GetSection("Communication").GetSection("Environment").Value.ToString();
            bool IsDevMode = con.GetSection("Communication").GetSection("IsDevMode").Value.ToString() == "true";
            return IsDevMode;
        }
        public static string TestEnv()
        {
            string Enviornment = Environment.GetEnvironmentVariable("MATRIX_ASPNETCORE_ENVIRONMENT");
            return Enviornment;
        }
        public static bool Like(string baseString, string toFind)
        {
            if (baseString == null || toFind == null)
                return false;

            return new Regex(@"\A" + new Regex(@"\.|\$|\^|\{|\[|\(|\||\)|\*|\+|\?|\\").Replace(toFind, ch => @"\" + ch).Replace('_', '.').Replace("%", ".*") + @"\z", RegexOptions.Singleline).IsMatch(baseString);
        }
    }

}
