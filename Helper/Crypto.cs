﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Security.Cryptography;
using System.IO;
using System.Text.RegularExpressions;
using System.Configuration;
using System.Web;
using Helper;
using Microsoft.Extensions.Configuration;

namespace Helper
{
    public static class Crypto
    {
        

        public static string MaskMobileNo(this string mobileNo)
        {
            int show = 2;
            if (string.IsNullOrEmpty(mobileNo) || mobileNo.Length <= show)
            {
                return mobileNo;
            }
            StringBuilder new_string = new StringBuilder();
            for (var i = 0; i < mobileNo.Length - show; i++)
            {
                new_string.Append("X");
            }
            new_string.Append(mobileNo.Substring(mobileNo.Length - show));
            return new_string.ToString();
            //int mobileLength = mobileNo.Length - 1;
            //string mob = "XXXXXXXX";
            //string postFix = (mobileLength - 8) > 0 ? mobileNo.Substring(8) : string.Empty;
            //return mob + postFix;
        }
        #region Encrypt Message

        public static string EncryptString(string plainMessage)
        {
            string strmsg = string.Empty;
            try
            {
                byte[] encode = new byte[plainMessage.Length];
                encode = Encoding.UTF8.GetBytes(plainMessage);
                strmsg = Convert.ToBase64String(encode);
                encode = null;
            }
            catch (Exception ex)
            {
            }
            return strmsg;
        }

        public static String encrypt_AES(String Input)
        {
            var aes = new RijndaelManaged();
            aes.KeySize = 256;
            aes.BlockSize = 256;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String("PSVJQRk9QTEpNVU1DWUZCRVFGV1VVT0ZOV1RRU1NaWQ=");
            aes.IV = Convert.FromBase64String("YWlFLVEZZUFNaWlhPQ01ZT0lLWU5HTFJQVFNCRUJZVA=");
            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Convert.ToBase64String(xBuff);
            var bytes = Encoding.UTF8.GetBytes(Output);
            Output = Convert.ToBase64String(bytes);

            return Output;
        }

        public static string EncryptStringAES(string plainMessage)
        {
            string strmsg = string.Empty;
            try
            {
                byte[] encode = new byte[plainMessage.Length];
                encode = Encoding.UTF8.GetBytes(plainMessage);
                strmsg = Convert.ToBase64String(encode);
                encode = null;
            }
            catch (Exception ex)
            {
            }
            return strmsg;
        }

        public static String Encrypt_AES256(String Input)
        {
            String enryptStringCrypto = "";
            string Inputkey = "AKSA18CDY3464CG0A2E8O71F9B6B9EA9";
            var result = EncryptRijndael(Input, ref enryptStringCrypto, Inputkey);
            return enryptStringCrypto;
        }
        public static String Decrypt_AES256(String Input)
        {
            string decryptCrypto = "";
            string Inputkey = "AKSA18CDY3464CG0A2E8O71F9B6B9EA9";
            var result = DecryptRijndael(Input, ref decryptCrypto, Inputkey);
            return decryptCrypto;
        }


        #endregion

        #region Decrypt Message

        public static string DecryptString(string EncryptMessage)
        {
            string decryptpwd = string.Empty;
            try
            {
                UTF8Encoding encodepwd = new UTF8Encoding();
                Decoder Decode = encodepwd.GetDecoder();
                byte[] todecode_byte = Convert.FromBase64String(EncryptMessage);
                int charCount = Decode.GetCharCount(todecode_byte, 0, todecode_byte.Length);
                char[] decoded_char = new char[charCount];
                Decode.GetChars(todecode_byte, 0, todecode_byte.Length, decoded_char, 0);
                decryptpwd = new String(decoded_char);
                todecode_byte = null;
            }
            catch
            {

            }
            return decryptpwd;
        }

        public static String decrypt_AES(String Input)
        {
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = 256;
            aes.BlockSize = 256;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String("PSVJQRk9QTEpNVU1DWUZCRVFGV1VVT0ZOV1RRU1NaWQ=");
            aes.IV = Convert.FromBase64String("YWlFLVEZZUFNaWlhPQ01ZT0lLWU5HTFJQVFNCRUJZVA=");
            var decrypt = aes.CreateDecryptor();
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Encoding.UTF8.GetString(xBuff);
            return Output;
        }

        public static bool EncryptRijndael(string plainString, ref string encString, String Key)
        {
            try
            {
                if (string.IsNullOrEmpty(plainString))
                    throw new ArgumentNullException("Not a valid text");

                var aesAlg = NewRijndaelManaged(Key);

                var encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);
                var msEncrypt = new MemoryStream();
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                using (var swEncrypt = new StreamWriter(csEncrypt))
                {
                    swEncrypt.Write(plainString);
                }
                var s = msEncrypt.ToArray();
                encString = Convert.ToBase64String(msEncrypt.ToArray());
                return true;
            }
            catch (Exception ex)
            {
                encString = ex.Message;
                return false;
            }
        }

        public static bool DecryptRijndael(string cipherText, ref string decString, string key)
        {
            try
            {
                if (string.IsNullOrEmpty(cipherText))
                    throw new ArgumentNullException("cipherText");

                if (!IsBase64String(cipherText))
                    throw new Exception("The cipherText input parameter is not base64 encoded");

                var aesAlg = NewRijndaelManaged(key);
                var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
                var cipher = Convert.FromBase64String(cipherText);

                using (var msDecrypt = new MemoryStream(cipher))
                {
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (var srDecrypt = new StreamReader(csDecrypt))
                        {
                            decString = srDecrypt.ReadToEnd();
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                decString = ex.Message;
                return false;
            }
        }

        private static bool IsBase64String(string base64String)
        {
            base64String = base64String.Trim();
            return (base64String.Length % 4 == 0) &&
                   Regex.IsMatch(base64String, @"^[a-zA-Z0-9\+/]*={0,3}$", RegexOptions.None);

        }

        private static RijndaelManaged NewRijndaelManaged(string Inputkey)
        {
            var aesAlg = new RijndaelManaged();
            aesAlg.Key = Encoding.ASCII.GetBytes(Inputkey);
            aesAlg.IV = Encoding.ASCII.GetBytes(Inputkey.Substring(0, 16));
            return aesAlg;
        }

        #endregion

        public static String Encrypt(this String Input)
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string _saltKey = con.GetSection("Communication").GetSection("SaltKey").Value.ToString();
            string _initializationVector = con.GetSection("Communication").GetSection("InitializationVectorKey").Value.ToString();

            var aes = new RijndaelManaged();
            aes.KeySize = 128;
            aes.BlockSize = 128;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String(_saltKey);
            aes.IV = Convert.FromBase64String(_initializationVector);

            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }

                xBuff = ms.ToArray();
            }

            String Output = Convert.ToBase64String(xBuff);
            return Output;
        }
        public static String Decrypt(this String Input)
        {
            try
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                string _saltKey = con.GetSection("Communication").GetSection("SaltKey").Value.ToString();
                string _initializationVector = con.GetSection("Communication").GetSection("InitializationVectorKey").Value.ToString();


                Input = Input.Replace(" ", "+").Replace("\\", "");
                RijndaelManaged aes = new RijndaelManaged();
                aes.KeySize = 128;
                aes.BlockSize = 128;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = Convert.FromBase64String(_saltKey);
                aes.IV = Convert.FromBase64String(_initializationVector);

                var decrypt = aes.CreateDecryptor();
                byte[] xBuff = null;
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                    {
                        byte[] xXml = Convert.FromBase64String(Input);
                        cs.Write(xXml, 0, xXml.Length);
                    }

                    xBuff = ms.ToArray();
                }

                String Output = Encoding.UTF8.GetString(xBuff);
                return Output;
            }
            catch (CryptographicException ex)
            {
                return Input;
            }
        }

        public static String encrypt_AES(String Input, string Key, string IV)
        {
            var aes = new RijndaelManaged();
            aes.KeySize = 256;
            aes.BlockSize = 256;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String(Key);
            aes.IV = Convert.FromBase64String(IV);
            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Convert.ToBase64String(xBuff);
            //var bytes = Encoding.UTF8.GetBytes(Output);
            //Output = Convert.ToBase64String(bytes);

            return Output;
        }

        public static string GetValidationKey(long leadId, string Key, string IV)
        {
            string encryptedLeadId = encrypt_AES(leadId.ToString(), Key, IV);
            string encryptedEncodedLeadId = EncryptString(encryptedLeadId);
            return UrlEncode(encryptedEncodedLeadId);

        }

        public static string UrlEncode(string input)
        {
            return HttpUtility.UrlEncode(input);
        }
        public static String CoommonEncrytion(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            var aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String(encKey);
            aes.IV = Convert.FromBase64String(IVKey);
            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }

                xBuff = ms.ToArray();
            }
            String Output = Convert.ToBase64String(xBuff);
            return Output;
        }

        public static string decrypt_AES(string Input, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String(encKey);
            aes.IV = Convert.FromBase64String(IVKey);
            var decrypt = aes.CreateDecryptor();
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Encoding.UTF8.GetString(xBuff);
            return Output;
        }


        public static String Encrytion_Payment_AES(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey, bool IsUrlEncoded = true)
        {
            String Output = string.Empty;
            try
            {
                var aes = new RijndaelManaged();
                aes.KeySize = KeySize;
                aes.BlockSize = BlockSize;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = Encoding.UTF8.GetBytes(encKey);
                aes.IV = Encoding.UTF8.GetBytes(IVKey);
                var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
                byte[] xBuff = null;
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                    {
                        byte[] xXml = Encoding.UTF8.GetBytes(Input);
                        cs.Write(xXml, 0, xXml.Length);
                    }

                    xBuff = ms.ToArray();
                }
                Output = Convert.ToBase64String(xBuff);
                if (IsUrlEncoded)
                    Output = UrlEncode(Output);
            }
            catch (Exception ex)
            {

            }

            return Output;
        }





        public static String Decrytion_Payment_AES(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            Input = Input.Replace(" ", "+").Replace("\\", "");
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Encoding.UTF8.GetBytes(encKey);
            aes.IV = Encoding.UTF8.GetBytes(IVKey);

            var decrypt = aes.CreateDecryptor();
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }

                xBuff = ms.ToArray();
            }

            String Output = Encoding.UTF8.GetString(xBuff);
            return Output;
        }

        public static string ComputeSHA256Hash(string input)
        {
            using SHA256 sha256 = SHA256.Create();
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashBytes = sha256.ComputeHash(inputBytes);
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
        }
    }


}
