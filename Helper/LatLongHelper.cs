﻿using NetTopologySuite.Geometries;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Xml;
using Coordinate = PropertyLayers.Coordinate;


namespace Helper
{
    public static class LatLongHelper
    {

        public static bool ContainsPoint(this List<Coordinate> polygon, Coordinate pointCoordinate)
        {
            var polygonPoints = new CoordinateList();
            foreach (Coordinate coordinates in polygon)
            {
                polygonPoints.Add(new NetTopologySuite.Geometries.Coordinate(coordinates.Longitude, coordinates.Latitude));
            }

            // Create a polygon object using the points
            var netPolygon = new Polygon(new LinearRing(polygonPoints.ToArray()));

            // Create a point object using the provided latitude and longitude
            var point = new Point(new NetTopologySuite.Geometries.Coordinate(pointCoordinate.Longitude, pointCoordinate.Latitude));

            // Check if the point is within the polygon
            if (netPolygon.Contains(point) || netPolygon.Boundary.Contains(point))
            {
                return true;
            }
            return false;
        }

        public static List<CityZone> ParseKML(string kmlFilePath, int? CityId)
        {

            List<CityZone> zones = new();
            int cityId = 0;
            // Readfile
            var doc = new XmlDocument();
            doc.Load(kmlFilePath);

            // Namespace manager for KML namespace
            var nsMgr = new XmlNamespaceManager(doc.NameTable);
            nsMgr.AddNamespace("kml", "http://www.opengis.net/kml/2.2");

            // Select all zones from the KML file
            var Placemarks = doc.SelectNodes("//Placemark", nsMgr);

            foreach (XmlNode placemarkNode in Placemarks)
            {
                var isPlacemarkPolygon = placemarkNode.SelectNodes(".//Polygon").Count > 0;
                if (!isPlacemarkPolygon) continue;

                //var Coordinates = placemarkNode.SelectNodes("//coordinates", nsMgr);
                var coordinateNode = placemarkNode.SelectSingleNode(".//coordinates", nsMgr);
                var cityIdNode = placemarkNode.SelectSingleNode(".//description", nsMgr);

                if (CityId != null)
                {
                    cityId = (int)CityId;
                }
                else if(cityIdNode != null)
                {
                    cityId = Convert.ToInt32(cityIdNode.InnerText);
                }
                 
                if (coordinateNode == null) continue;

                CityZone cityZone = new()
                {
                    Name = GetZoneName(placemarkNode, nsMgr),
                    CityId = cityId,
                    ZoneId = GetZoneName(placemarkNode, nsMgr).Replace(' ', '-')
                };
                cityZone.Coordinates = new();

                //foreach (XmlNode coordinateNode in Coordinates)
                //{
                // Extract the coordinates from the node
                string coordinates = coordinateNode.InnerText.Trim();
                string[] pointsArray = coordinates.Split(new char[] { ' ', '\n', '\t', ',' }, StringSplitOptions.RemoveEmptyEntries);

                // Iterate through the points and create the polygon
                var polygonPoints = new CoordinateList();
                int coordinateDimension = 2;
                if (double.Parse(pointsArray[2]) == 0) coordinateDimension = 3;
                for (int i = 0; i < pointsArray.Length; i += coordinateDimension)
                {
                    double lon = double.Parse(pointsArray[i]);
                    double lat = double.Parse(pointsArray[i + 1]);
                    Coordinate coordinate = new(lat, lon);

                    cityZone.Coordinates.Add(coordinate);
                }

                if (IsZoneValid(cityZone))
                {
                    zones.Add(cityZone);
                }
                // Create a polygon object using the points
                //var polygon = new Polygon(new LinearRing(polygonPoints.ToArray()));

                // Create a point object using the provided latitude and longitude
                //var point = new Point(new Coordinate(longitude, latitude));

                // Check if the point is within the polygon
                //if (polygon.Contains(point))
                //{
                //    return true;
                //}
                //}
            }
            return zones;
        }

        private static bool IsZoneValid(CityZone cityZone)
        {
            bool IsZoneValid = true;
            var len = cityZone.Coordinates.Count;
            // Check for closed polygon
            if (cityZone.Coordinates.Count == 0)
            {
                IsZoneValid = false;
            }
            else if (cityZone.Coordinates[0].Latitude != cityZone.Coordinates[len - 1].Latitude
                || cityZone.Coordinates[0].Longitude != cityZone.Coordinates[len - 1].Longitude)
            {
                IsZoneValid = false;
            }

            return IsZoneValid;
        }

        private static string GetZoneName(XmlNode placemarkNode, XmlNamespaceManager nsMgr)
        {
            try
            {

                var zoneNode = placemarkNode.SelectSingleNode(".//Name", nsMgr);
                if (zoneNode == null)
                {
                    zoneNode = placemarkNode.SelectSingleNode(".//name", nsMgr);
                }
                var zoneName = zoneNode.InnerText.Trim();
                return zoneName;

            }
            catch
            {
                return null;
            }

        }
    }
}
