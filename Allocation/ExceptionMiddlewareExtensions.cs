﻿using Microsoft.AspNetCore.Diagnostics;
using System.Text.Json;

namespace GlobalErrorHandling.Extensions
{
    public static class ExceptionMiddlewareExtensions
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app)
        {

            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = 400;
                    context.Response.ContentType = "application/json";
                    
                    var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                    if (contextFeature != null)
                    {
                        var ts = new DateTime().ToString();

                        string reqData = "";

                        //string error = ((ExceptionHandlerFeature)contextFeature) !=null && ((ExceptionHandlerFeature)contextFeature).Path!=null ? ((ExceptionHandlerFeature)contextFeature).Path:string.Empty;
                        LoggingHelper.LoggingHelper.AddloginQueue("", 0, contextFeature.Error.ToString(), "ConfigureExceptionHandler", "Allocation", "ExceptionMiddlewareExtensions", "", "", DateTime.Now, DateTime.Now);

                        await context.Response.WriteAsync(new ErrorDetails()
                        {
                            StatusCode = 500,
                            Message = "Something went wrong"
                        }.ToString());


                    }
                });
            });
        }
    }
    public class ErrorDetails
    {
        public int StatusCode { get; set; }
        public string Message { get; set; }
        public override string ToString()
        {
            return JsonSerializer.Serialize(this);
        }
    }
}