﻿using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;


namespace Allocation.Controllers
{
    [ApiController]
    [Route("FOSallocation/api/[controller]/[action]")]
    
    public class FOSAllocationController : ControllerBase
    {
  
        readonly IFOSAllocationBLL objFOSAllocationBLL;
        public FOSAllocationController(IFOSAllocationBLL _objFOSAllocationBLL)
        {
            objFOSAllocationBLL = _objFOSAllocationBLL;
        }
      
        [HttpGet]
        public void FOSLeadAllocation()
        {
            objFOSAllocationBLL.FOSLeadAllocation();
        }
    }
}

