﻿using System.Diagnostics;
using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;

namespace CommAPI.Controllers
{
    [ApiController]
    [Route("allocation/api/[controller]/[action]")]

    public class IndexController : ControllerBase
    {
 

        [HttpGet]
        public ResponseAPI HealthCheck()
        {
            ResponseAPI Response = new()
            {
                status = true,
                message = "Success",
                dockerTime = AllocationBLL.ControllerLoadTime
            };

            return Response;
        }

        [HttpGet]
        public IActionResult NonAuthorised()
        {
            Debug.WriteLine("Invalid Token dubug.");
            return new ContentResult { StatusCode = 401, Content = "Unauthenticated", ContentType = "text/plain" };
        }

        [HttpGet]
        public IActionResult NonAccess()
        {
            return new ContentResult { StatusCode = 403, Content = "Unauthorized", ContentType = "text/plain" };
        }

        [HttpGet]
        public bool ClearCache(string key)
        {
            string response = "";
            try
            {
                response = IndexBLL.ClearCache(key);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, 0, ex.ToString(), "ClearCache", "Allocation", "", key, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response !="null";
        }   
    }
}

