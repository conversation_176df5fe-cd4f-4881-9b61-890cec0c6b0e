﻿using PropertyLayers;
using Allocation.Helpers;
using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;

namespace Allocation.Controllers
{
    [CommAuth]
    [ApiController]
    [Route("allocation/api/[controller]/[action]")]
    
    public class TermAllocationController : ControllerBase
    {
        readonly ITermAllocationBLL objAllocationBLL;        
        public TermAllocationController(ITermAllocationBLL _objAllocationBLL)
        {
            objAllocationBLL = _objAllocationBLL;
        }
       
        [HttpGet]
        public string LeadsAllocation_term()
        {
            try {
                objAllocationBLL.LeadsAllocation_TermAsync();
            } catch (Exception ex){
                return ex.ToString();
            }
            
            return "true";
        }

        [HttpPost]
        public async Task<AllocateTermLeadResponse> AllocateTermLeadAPI(AllocateLeadsData allocateLeadsData)
        {

            return await objAllocationBLL.AllocateTermLeadAPI(allocateLeadsData);
        }
    }
}

