using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;

namespace Allocation.Controllers
{
    [ApiController]
    [Route("allocation/api/[controller]/[action]")]
    
    public class PushMultiProdController : ControllerBase
    {
        readonly IPushMultiProdLeadBLL objPushMultiProdBLL;     
        public PushMultiProdController(IPushMultiProdLeadBLL _objPushMultiProdBLL)
        {
            objPushMultiProdBLL = _objPushMultiProdBLL;
        }
       

        [HttpGet]
        public string PushMultiProdLeadInRedis()
        {
            try {
                objPushMultiProdBLL.PushMultiProdLeadInRedis();
            } catch (Exception ex){
                return ex.ToString();
            }
            
            return "true";
        }
    }
}

