using System.Text;
using DataAccessLayer;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class TermAllocation
    {
        private static Timer _allocationtimer;
        public TermAllocation()
        {
            //allocation queue for allocating data start            
            _allocationtimer = new Timer(60 * 1000);
            _allocationtimer.Elapsed += _termallocationtimer_Elapsed;
            _allocationtimer.Start();
            //allocation queue for allocating data end                       
        }
        static async void _termallocationtimer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode()) {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _allocationtimer.Stop();

                sb.Append("start-");

                bool RunTermAllocationScheduler = "RunTermAllocationScheduler".AppSettings() == "true";
                if (RunTermAllocationScheduler == true)
                {
                    TermAllocationBLL _TermLeadallocation = new();
                    await _TermLeadallocation.LeadsAllocation_TermAsync();
                }
                else
                {
                    sb.Append("Term Allocation Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "TermAllocationJobNew", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _allocationtimer.Start();
            }
        }
    }
}
