﻿using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;
using EmailCommunicationBLL;
using System.Text;
using System.Timers;


namespace PrioritizationQueueSchedulers
{
    public class LeadRejectionScheduler
    {
        private static Timer _LeadRejectionTimer;

        public LeadRejectionScheduler()
        {
            _LeadRejectionTimer = new Timer(1 * 60 * 60 * 1000);
            _LeadRejectionTimer.Elapsed += _LeadRejection_timer_Elapsed;
            _LeadRejectionTimer.Start();
        }

        static async void _LeadRejection_timer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }

            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _LeadRejectionTimer.Stop();

                sb.Append("start-");

                
                if (DateTime.Now.Hour >= 0 && DateTime.Now.Hour < 1)
                {
                    LeadRejectionBLL _LeadRejectionBLL = new();
                    _LeadRejectionBLL.LeadRejectionBySystem();
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "LeadRejectionScheduler", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _LeadRejectionTimer.Start();
            }
        }


    }
}
