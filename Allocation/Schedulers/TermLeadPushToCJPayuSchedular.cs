﻿using System.Text;
using DataAccessLayer;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class TermLeadPushToCJPayuSchedular
    {
        private static Timer _termPayUtimer;
        public TermLeadPushToCJPayuSchedular()
        {
            //allocation queue for allocating data start            
            _termPayUtimer = new Timer(2 * 60 * 1000);
            _termPayUtimer.Elapsed += _termPayu_timer_Elapsed;
            _termPayUtimer.Start();
            //allocation queue for allocating data end                       
        }
        static async void _termPayu_timer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _termPayUtimer.Stop();
                TermPayURanksBLL.Push2LacLeadsToTermCJ();
              
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "TermLeadPushToCJPayuSchedular", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _termPayUtimer.Start();
            }
        }
    }
}
