﻿using System.Text;
using System.Timers;
using EmailCommunicationBLL;
using MongoConfigProject;
using Helper;
using Timer = System.Timers.Timer;

namespace PrioritizationQueueSchedulers
{
    public class RejectLeadScheduler
    {
        private static Timer _RejectLeadtimer;
        public RejectLeadScheduler()
        {
            //allocation queue for allocating data start            
            _RejectLeadtimer = new Timer(1* 60 * 60 * 1000);
            _RejectLeadtimer.Elapsed += _RejectLead_timer_Elapsed;
            _RejectLeadtimer.Start();
            //allocation queue for allocating data end                       
        }
        static async void _RejectLead_timer_Elapsed(object sender, ElapsedEventArgs e)
        {

            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _RejectLeadtimer.Stop();

                sb.Append("start-");

              //  bool RunTermPayUScheduler = "RunTermPayUScheduler".AppSettings() == "true";
               // string startSpan = "RunTermPayUSchedulerTime".AppSettings().Split(" - ")[0];
                //string endSpan = "RunTermPayUSchedulerTime".AppSettings().Split(" - ")[1];
                if (DateTime.Now.Hour>=1 && DateTime.Now.Hour<2)
                {
                    List<string> AllRjectedLeadSP= "AllRjectedLeadSP".AppSettings().Split(',').ToList();    
                     RejectLeadBLL.RejectPredictiveLeads(AllRjectedLeadSP);
                }
                else
                {
                    sb.Append("Scheduler Stopped by config");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "RejectLeadScheduler", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _RejectLeadtimer.Start();
            }
        }
    }
}
