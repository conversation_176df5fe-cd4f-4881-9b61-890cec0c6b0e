// using System.Text;
// using DataAccessLayer;
// using System.Timers;
// using EmailCommunicationBLL;
// using MongoConfigProject;
// using Helper;
// using Timer = System.Timers.Timer;

// namespace PrioritizationQueueSchedulers
// {
//     public class TermAllocation
//     {
//         private static Timer _allocationtimer;
//         private const int interval = 15 * 6 * 1000; // 1.5 minutes, 1.5 * 60 * 1000

//         public TermAllocation()
//         {
//             //allocation queue for allocating data start            
//             _allocationtimer = new Timer(interval);
//             _allocationtimer.Elapsed += _termallocationtimer_Elapsed;
//             _allocationtimer.AutoReset = false;
//             _allocationtimer.Start();
//             //allocation queue for allocating data end                       
//         }
//         static async void _termallocationtimer_Elapsed(object sender, ElapsedEventArgs e)
//         {

//             if (CoreCommonMethods.IsDevMode()) {
//                 //donot run scheduler on local
//                 return;
//             }
//             //Stop and start timer to prevent the database call overlap
//             string strexception = string.Empty;
//             DateTime RequestDatetime = DateTime.Now;
//             StringBuilder sb = new();

//             try
//             {
//                 sb.Append("start-");

//                 bool RunTermAllocationScheduler = "RunTermAllocationScheduler".AppSettings() == "true";
//                 if (RunTermAllocationScheduler == true)
//                 {
//                     TermAllocationBLL _TermLeadallocation = new();
//                     await _TermLeadallocation.LeadsAllocation_TermAsync();
//                 }
//                 else
//                 {
//                     sb.Append("Term Allocation Scheduler Stopped by config");
//                 }
//             }
//             catch (Exception ex)
//             {
//                 strexception = ex.ToString();
//             }
//             finally
//             {
//                 DateTime endTime = DateTime.Now;

//                 LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "TermAllocationJobNew", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, endTime);
                
//                 var duration = (endTime - RequestDatetime).TotalMilliseconds;
//                 if((int)duration < interval)
//                 {
//                     _allocationtimer.Interval = interval-(int)duration;
//                 } else {
//                     _allocationtimer.Interval = 1;
//                 }

//                 _allocationtimer.Start();
//             }
//         }
//     }
// }
