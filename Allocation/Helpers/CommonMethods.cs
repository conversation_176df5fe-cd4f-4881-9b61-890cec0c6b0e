﻿using Newtonsoft.Json;
using MongoConfigProject;
using System.Text;

namespace Helper
{
    public static class CommonMethods
    {

        public static long GetDecryptIdBySource(string Input)
        {
            long output = 0;
            string error = string.Empty;
            try
            {
                if (CoreCommonMethods.IsValidString(Input))
                    Input = Crypto.Decrytion_Payment_AES(Input, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings());

                long.TryParse(Input, out output);

            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return output;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Input, 0, error, "GetDecryptIdBySource", "Allocation", "CommonMethods", "", "", DateTime.Now, DateTime.Now);
            }
            return output;
        }

        public static long GetUserIdByHeader()
        {
            long output = 0;
            try
            {
                string UserId = Environment.GetEnvironmentVariable("UserId");
                if (CoreCommonMethods.IsValidString(UserId))
                    long.TryParse(UserId, out output);

            }
            catch (Exception ex)
            {
                return output;
            }
            return output;
        }

        public static string GetSourceByHeader()
        {
            return Environment.GetEnvironmentVariable("Source");

        }
        public static string ReadCookies(string reqCookies)
        {
            string request = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(reqCookies))
                {

                    byte[] data = Convert.FromBase64String(reqCookies);
                    string decodedString = Encoding.UTF8.GetString(data);

                    if (decodedString != null)
                        request = decodedString;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "ReadCookies", "FOS", "CommonMethods", JsonConvert.SerializeObject(reqCookies), JsonConvert.SerializeObject(request), DateTime.Now, DateTime.Now);
            }
            return request;
        }


    }
}
