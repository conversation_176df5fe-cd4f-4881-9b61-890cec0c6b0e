﻿using DataAccessLibrary;
using Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using MongoDB.Driver;
using MongoDB.Driver.Builders;


namespace Allocation.Helpers
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class CommAuth : ActionFilterAttribute
    {



        public override void OnActionExecuting(ActionExecutingContext actionContext)
        {
            var context = actionContext.HttpContext;
            string? Source = context.Request != null ? context.Request.Headers["source"] : string.Empty;

            if (IsValidRequest(context.Request, context))
            {
                if (CoreCommonMethods.IsValidString(Source) && !IsAuthorize(context, context.Request, Source))
                {
                    
                    actionContext.HttpContext.Response.StatusCode = StatusCodes.Status403Forbidden;
                    //actionContext.HttpContext.Response.WriteAsync("Forbidden");
                    //return;
                    actionContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "Index", action = "NonAccess"}));
                }
                //base.OnActionExecuting(actionContext);
            }
            else
            {
                actionContext.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                actionContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "Index", action = "NonAuthorised" }));
            }
        }

        public bool IsValidRequest(HttpRequest request, HttpContext context)
        {
            bool Result = false;
            try
            {

                var endpoint = context.GetEndpoint() as RouteEndpoint;
                var CurrentActionMethod = endpoint?.RoutePattern?.RequiredValues?.Values?.FirstOrDefault();
                string Source = request != null ? request.Headers["source"] : string.Empty;
                string? Domain = request != null ? Convert.ToString(request.Host) : string.Empty;


                Environment.SetEnvironmentVariable("Source", request.Headers["source"]);
                Environment.SetEnvironmentVariable("UserId", request.Headers["AgentId"]);

               
                ////////-----------------------  for public access -------------------------////////////////////
                if (!string.IsNullOrEmpty(Domain) && (Domain.ToUpper().Contains("MATRIXCOREAPI"))
                            && !(!string.IsNullOrEmpty(Source) && Source.ToLower() == "pg"
                            && CoreCommonMethods.IsValidString(Convert.ToString(CurrentActionMethod))
                            && (Convert.ToString(CurrentActionMethod).ToLower() == "savepgdata"
                            || Convert.ToString(CurrentActionMethod).ToLower() == "getleaddetailsforpg"
                            || Convert.ToString(CurrentActionMethod).ToLower() == "pushhealthsilead")
                            )
                        )
                {
                    string agentId = request != null ? request.Headers["AgentId"] : string.Empty;
                    string token = request != null ? request.Headers["Token"] : string.Empty;

                    var reqCookies = request != null ? request.Cookies["MatrixToken"] : null;
                    string _cookies = CommonMethods.ReadCookies(reqCookies);

                    if (CoreCommonMethods.IsValidString(_cookies))
                    {
                        User? user = !string.IsNullOrEmpty(_cookies) ? JsonConvert.DeserializeObject<User>(_cookies) : null;

                        agentId = user != null ? user.UserId : string.Empty;
                        token = user != null ? user.AsteriskToken : string.Empty;


                        if (request != null)
                        {
                            request.Headers.Remove("AgentId");
                            request.Headers.Add("AgentId", agentId);
                        }
                    }


                    if (CoreCommonMethods.IsValidString(agentId) && CoreCommonMethods.IsValidString(token))
                    {
                        string Key = $"{RedisCollection.PredictiveAgent()}:{agentId.Trim()}";
                        string FOSKey = $"{RedisCollection.AppKey()}:{agentId.Trim()}";

                        string CacheToken = getCacheToken(Key);

                        if (CoreCommonMethods.IsValidString(CacheToken) && CacheToken.Trim() == token.Trim())
                            Result = true;
                        else
                        {
                            string MatrixToken = PredictiveAgentStatusRedis.GetMatrixToken(Convert.ToString(agentId));

                            /*PredictiveAgentStatus objPredictiveAgentDetails = PredictiveAgentStatusRedis.GetAgentDetails(Convert.ToString(agentId));
                            if (objPredictiveAgentDetails != null && objPredictiveAgentDetails.AsteriskToken == token && objPredictiveAgentDetails.AsteriskToken != "123456" && objPredictiveAgentDetails.AsteriskToken != "123456789")
                            {
                                Result = true;
                                CommonCache.GetOrInsertIntoCache(objPredictiveAgentDetails.AsteriskToken, Key, 8 * 60);
                            }*/
                            if (CoreCommonMethods.IsValidString(MatrixToken) && MatrixToken == token)
                            {
                                Result = true;
                                CommonCache.GetOrInsertIntoCache(MatrixToken, Key, 8 * 60);
                            }
                            else if (!Result)
                            {
                                string AppToken = PredictiveAgentStatusRedis.GetAppToken(Convert.ToString(agentId));
                                if (CoreCommonMethods.IsValidString(AppToken) && AppToken == token)
                                {
                                    Result = true;
                                    CommonCache.GetOrInsertIntoCache(AppToken, Key, 8 * 60);
                            }
                            }
                        }
                    }
                }
                ////////-----------------------  for Internal access -------------------------////////////////////
                else
                {
                    string clientKey = request != null ? request.Headers["clientKey"] : string.Empty;
                    string AuthKey = request != null ? request.Headers["authKey"] : string.Empty;


                    if (CoreCommonMethods.IsValidString(AuthKey) && CoreCommonMethods.IsValidString(Source) && CoreCommonMethods.IsValidString(clientKey))
                    {
                        string Key = $"{RedisCollection.MongoConfig()}";
                        Dictionary<string, SysConfigData> _dictCollection = getMongoCacheData(Key);


                        if (_dictCollection == null || _dictCollection.Count == 0)
                        {
                            _dictCollection = getDictCollection();
                            CommonCache.GetOrInsertIntoCache(_dictCollection, Key, 12 * 60);
                        }
                        if (_dictCollection != null && _dictCollection.Count > 0)
                        {
                            SysConfigData SourceCollection = getValidSource(_dictCollection, Source, Key);
                            if (SourceCollection != null)
                            {
                                string EncKey = !string.IsNullOrEmpty(SourceCollection.EncKey) ? SourceCollection.EncKey.ToString() : "";
                                string EncIV = !string.IsNullOrEmpty(SourceCollection.EncIV) ? SourceCollection.EncIV.ToString() : "";
                                request.Headers["EncKey"] = EncKey;
                                request.Headers["EncIV"] = EncIV;
                                Result = IsMatchData(SourceCollection, Source, AuthKey, clientKey);
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                Result = false;

                Console.WriteLine("Exception in IsValidRequest." + ex.ToString());

            }
            return Result;
        }

        public string? getCacheToken(string Key)
        {
            string token = string.Empty;
            try
            {
                if (MemoryCache.Default[Key] is var memKey && memKey != null)
                    token = Convert.ToString(MemoryCache.Default[Key]);

            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in getCacheDatain Commauth." + ex.ToString());
            }
            return token;
        }
        public Dictionary<string, SysConfigData> getMongoCacheData(string Key)
        {
            Dictionary<string, SysConfigData> obj = new();
            try
            {
                if (MemoryCache.Default[Key] != null)
                {
                    ObjectCache CacheConfig = MemoryCache.Default;
                    obj = (Dictionary<string, SysConfigData>)CacheConfig.Get(Key);

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in getMongoCacheData Commauth." + ex.ToString());
            }
            return obj;
        }

        public List<Dictionary<string, SysConfigData>> MakeDictCollection(List<SysConfigData> objMongo)
        {

            List<Dictionary<string, SysConfigData>> obj = (from r in objMongo.AsEnumerable().Where(x => x.source != null)
                                                           select new Dictionary<string, SysConfigData>() { { r.source, r } }).ToList();

            return obj;
        }
        public Dictionary<string, SysConfigData> getDictCollection()
        {
            Dictionary<string, SysConfigData> oMongoData = new Dictionary<string, SysConfigData>();
            oMongoData = MongoDLL.GetConfiValueFromMongo();
            return oMongoData;
        }
        public SysConfigData getValidSource(Dictionary<string, SysConfigData> _dictCollection, string Source, string Key)
        {
            SysConfigData SourceCollection = null;
            string SourceToLower = Source.ToLower();
            try
            {
                if (_dictCollection.ContainsKey(SourceToLower))
                {
                    SourceCollection = _dictCollection[SourceToLower];
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in getValidSource Commauth." + ex.ToString());
            }
            return SourceCollection;
        }

        public static bool IsMatchData(SysConfigData SourceCollection, string Source, string AuthKey, string clientKey)
        {
            bool res = false;
            try
            {
                if (SourceCollection != null && SourceCollection.source.ToLower() == Source.ToLower() && SourceCollection.authKey == AuthKey && SourceCollection.clientKey == clientKey)
                    res = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in IsMatchData." + ex.ToString());

            }
            return res;
        }


        public string getAgentIdfromheader(HttpRequest Request)
        {
            return Request.Headers["AgentId"];
        }


        public class User
        {
            public string UserId { get; set; }
            public string AsteriskToken { get; set; }
            public string EmployeeId { get; set; }
            public int GroupId { get; set; }
        }
        public bool IsAuthorize(HttpContext context, HttpRequest request, string Source)
        {
            bool IsValidRequest = false;
            StringBuilder sb = new();
            try
            {
                var endpoint = context.GetEndpoint() as RouteEndpoint;
                var CurrentActionMethod = endpoint?.RoutePattern?.RequiredValues?.Values?.FirstOrDefault();

                Dictionary<string, string>? SourceCollection = null;
                string? MethodURL = request != null ? request.Path.Value : string.Empty;
                string Key = $"{RedisCollection.ACLMongoConfig()}";
                //string Key = Source.ToLower() + "_" + MethodURL.ToLower();
                string? Origin = request != null ? request.Headers["Origin"].FirstOrDefault() : string.Empty;

                sb.Append("MethodURL " + JsonConvert.SerializeObject(MethodURL) + "\r\n");
                sb.Append(" Source " + Source + "\r\n");
                sb.Append(" Origin " + Origin + "\r\n");

                if (MemoryCache.Default[Key] != null)
                {
                    sb.Append(" ,Enter in cache " + "\r\n");
                    ObjectCache CacheConfig = MemoryCache.Default;
                    SourceCollection = (Dictionary<string, string>)CacheConfig.Get(Key);
                }
                else
                    SourceCollection = getACLDictCollection(Key);

                //----- Valid method found or not
                IsValidRequest = IsValidACLRequest(SourceCollection, Source, CurrentActionMethod.ToString());


                //if (!IsValidRequest)// if valid method not found thn call mongo config
                //{
                //    sb.Append(" ,Recall mongo" + "\r\n");
                //    IsValidRequest = AddAndMatchAclConfig(MethodURL, Key,Source, Convert.ToString(CurrentActionMethod));
                //    sb.Append(" ,End request final IsValidRequest: " + IsValidRequest + "\r\n");

                //}
                if (!IsValidRequest)//logging
                    LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "IsAuthorize", "Allocation", "CommAuth", JsonConvert.SerializeObject(sb), "", DateTime.Now, DateTime.Now);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 1, ex.ToString(), "IsAuthorize", "Allocation", "CommAuth", JsonConvert.SerializeObject(sb), "", DateTime.Now, DateTime.Now);
            }
            return IsValidRequest;

        }
        public bool IsValidACLRequest(Dictionary<string, string> SourceCollection, string Source, string MethodURL)
        {
            bool res = false;

            string KeyToSearch = MethodURL.ToLower() + "_" + Source.ToLower();

            if (SourceCollection.ContainsKey(KeyToSearch))
            {
                res = true;
            }
            return res;
        }
        public Dictionary<string, string> getACLDictCollection(string Key)
        {
            List<ACLConfigData> oMongoData = null;
            IMongoQuery varquery = Query.And(Query.EQ("isActive", true),
                                    Query.EQ("application", "allocation")
                                    );
            IMongoFields Field = Fields.Include("source", "method", "isActive");
            oMongoData = MongoDLL.GetACLMongoConfig(Field, varquery);
            // Convert List<ACLConfigData> to Dictionary<string, string>
            //Dictionary<string, string> dictionary = oMongoData
            //    .ToDictionary(data => $"{(data.method).ToLower()}_{(data.source).ToLower()}", data => data.method);

            Dictionary<string, string> dictionary = oMongoData
            .GroupBy(data => $"{data.method.ToLower()}_{data.source.ToLower()}")
            .ToDictionary(group => group.Key, group => group.First().method);

            if (dictionary.Any())
                CommonCache.GetOrInsertIntoCache(dictionary, Key, 12 * 60);
            return dictionary;
        }

    }
}
