﻿<?xml version="1.0" encoding="utf-8" ?>
<ConfigSettings>
	<!-- log keys-->
	<add key="ISErrorLog" value="1"/>
	<add key="ISLog" value="1"/>
	<add key="LogMethod" value="mongo"/>
	<add key="IsKFKLogingQueueEnabled" value="true"></add>
	<add key="ErrorNInfoLogFilePath" value="D:\Logs\CommunicationLogs\CommunicationService\" />
	<add key="queueloggingon" value="1"></add>
	<add key="loggingqueue" value=".\private$\loggingqueue" />
	<!-- log keys-->
</ConfigSettings>

