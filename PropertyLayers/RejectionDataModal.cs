﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{

    /*public class ParentDetails
    //{
    //    public Int64 ParentID { get; set; }
    //    public int TotalTalkTime { get; set; }

    //    public List<CallDataCDH> CallHistory;

    //    public List<CallBackDataED> CallBackData;

    //    public List<LeadDetailsRejection> LeadDetailsList;

    //    public List<LeadAssignData> LeadAssignData;

    //    public bool isRejectAll = false;

    //    public DateTime LastCallDate { get; set; }


    //    //public List<CallDataCDH> GetCallHistory()
    //    //{
    //    //    if (CallHistory == null)
    //    //    {
    //    //        // get data 

    //    //        //set calldata
    //    //    }
            
    //    //    return CallHistory;
    //    //}

    //    //public List<CallDataCDH> GetLastCallDate()
    //    //{
    //    //    if (CallHistory == null)
    //    //    {
    //    //        // get data 
    //    //    }
    //    //    return CallHistory;
    //    //}


    //}
    */

    public class ParentDetails
    {
        public Int64 ParentID { get; set; }
        public int TotalTalkTime { get; set; }
        public int TotalCallCount { get; set; }

        public List<CallDataCDH> CallHistory;

        public List<CallBackDataED> CallBackData;


        public List<LeadAssignData> LeadAssignData;

        public bool isRejectAll = false;

        public DateTime LastCallDate { get; set; }

        
        public DateTime CreatedOn { get; set; }
        public Int16 LeadRank { get; set; }

        public short ProductId { get; set; }
        public string LeadSource { get; set; }
        public string UTM_Source { get; set; }
        public Int16 InvestmentTypeID { get; set; }

        public Int16 StatusID { get; set; }
        public string Reason { get; set; }
        public string Country { get; set; }
        public DateTime OfferCreatedOn { get; set; }
        public Int64 PaymentStatus { get; set; }
        public DateTime TravelStartDate { get; set; }
        public DateTime AppointmentDateTime { get; set; }
        public string UTM_Term { get; set; }
        public string Source { get; set; }
        public DateTime PolicyExpiryDate { get; set; }

        public List<string> DontRejectChildWithLeadSource { get; set; }
        public List<string> DontRejectChildWithUtmSource { get; set; }
        public int SubStatusID { get; set; }

        public int CategoryId { get; set; }
        public int ChannelId { get; set; }
        public DateTime CustUnsubscribeCreatedOn { get; set; }
        public bool IsPreviousPolicyDateAssumed { get; set; }
        public string PolicyType { get; set; }
    }
    public class LeadDetailsRejection
    {
        public Int64 LeadID { get; set; }
        public string Name { get; set; }
        public DateTime DOB { get; set; }
        public DateTime CreatedOn { get; set; }
        public Int16 LeadRank { get; set; }
        
        public short ProductId { get; set; }
        public string LeadSource { get; set; }
        public string UTM_Source { get; set; }
        public Int16 InvestmentTypeID { get; set; }

        public Int16 StatusID { get; set; }
        public Int64 ParentID { get; set; }
        public string Reason { get; set; }

    }

    public class CallDataCDH
    {
        public Int32 TalkTime { get; set; }
        public DateTime CallDate { get; set; }
        public Int64 CallDataID { get; set; }
        public Int32 Duration { get; set; }
        public string Context { get; set; }
        public string CallType { get; set; }

    }


    public class CallBackDataED
    {
        public Int16 CallBackType { get; set; }
        public DateTime EventDate { get; set; }


    }


    public class LeadAssignData
    {
        public Int16 AssignToGroupId { get; set; }
        public DateTime AssignedDate { get; set; }

    }



    [Serializable]
    [BsonIgnoreExtraElements]
    public class RejectionConditions
    {
        public int MinDays { get; set; }
        public int MaxDays { get; set; }
        public string Property { get; set; }
        public List<string> StrNotIn { get; set; }
        public List<string> StrIn { get; set; }
        public List<Int16> IntIn { get; set; }
        public List<Int16> IntNotIn { get; set; }
        public string StrLike { get; set; }
        public string StrNotLike { get; set; }
        public short NotInQueryInt { get; set; }


    }

    public class SmeConditionsCase
    {
        public List<Int16> InvestmentTypeID { get; set; }
        public List<Int16> StatusID { get; set; }
        public int Sum { get; set; }
    }

    [Serializable]
    [BsonIgnoreExtraElements]
    public class LeadRejectionLogicsDocument
    {
        public int ProductId { get; set; }
        public int SubStatusID { get; set; }
        
        public bool IsActive { get; set; }
        public List<RejectionConditions> Conditions { get; set; }
        public List<SmeConditionsCase> Cases { get; set; }

        public string RejectionReason { get; set; }
        public List<string> DontRejectChildWithLeadSource { get; set; }
        public List<string> DontRejectChildWithUtmSource { get; set; }
        public bool SkipCallbackChk { get; set; }
    }

    [DataContract]
    [Serializable]
    public class RejectAllLeadsData
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public short StatusId { get; set; }

        [DataMember]
        public bool IsParent { get; set; }

        [DataMember]
        public short ProductID { get; set; }

        [DataMember]
        public int SubStatusID { get; set; }

        [DataMember]
        public bool IsReject { get; set; }

        [DataMember]
        public string RejectionReason { get; set; }

        [DataMember]
        public long UserId { get; set; }
    }

}
