﻿using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace PropertyLayers
{
   
    public class MatrixUserInfo
    {
        public string AsteriskToken
        {
            get;
            set;
        }
        public string UserId
        {
            get;
            set;
        }
        public bool IsLDapEnabled
        {
            get;
            set;
        }

        public string IpAddress
        {
            get;
            set;
        }

       
        public string UserName
        {
            get;
            set;
        }
        public string Email
        {
            get;
            set;
        }
        public string DomainUserName
        {
            get;
            set;
        }

        public string Address
        {
            get;
            set;
        }
        public string LoginId
        {
            get;
            set;
        }
        public string Password
        {
            get;
            set;
        }
        public string RoleId
        {
            get;
            set;
        }
        public int ReportingToUserId
        {
            get;
            set;
        }
        public int CreatedByUserId
        {
            get;
            set;
        }
        public DateTime CreatedOn
        {
            get;
            set;
        }
        public bool IsActive
        {
            get;
            set;
        }
        public string EmployeeId
        {
            get;
            set;
        }
        public bool IsAllowExternal
        {
            get;
            set;
        }
        public int CompanyId
        {
            get;
            set;
        }
        public string GroupName
        {
            get;
            set;
        }
        public string RoleName
        {
            get;
            set;
        }
        public int GroupId
        {
            get;
            set;
        }
        
        public Boolean QuickView
        {
            get;
            set;
        }

        public string UserStatus
        {
            get;
            set;
        }
        public string processId
        {
            get;
            set;
        }
        public string productId
        {
            get;
            set;
        }

        public int PendingLimit
        {
            get;
            set;
        }
        public int UntouchedLimit
        {
            get;
            set;
        }

        public int PlanTypeID
        {
            get;
            set;
        }
        public string Planname
        {
            get;
            set;
        }

        public string Contact
        {
            get;
            set;
        }

        #region Propensity Factor Properties

        public int PropensityId
        { get; set; }

        public decimal SourceFactor
        { get; set; }

        public decimal CityTypeFactor
        { get; set; }

        public decimal AgeFactor
        { get; set; }

        public decimal PolicyExpiryFactor
        { get; set; }

        public decimal PolicyTypeFactor
        { get; set; }

        #endregion

        #region Alignment Factor Properties

        public int AlignmentFactorId
        { get; set; }

        public decimal PriceFactor
        { get; set; }

        public decimal ProfileMatchFactor
        { get; set; }

        public decimal BrandFactor
        { get; set; }

        #endregion

        #region Agent Factor Properties

        public int AgentFactorId
        { get; set; }

        public decimal UtilizationFactor
        { get; set; }

        public decimal TargetFactor
        { get; set; }

        public decimal ConversionFactor
        { get; set; }

        #endregion

        #region UTM Source Propoerties

        public int UTMId
        {
            get;
            set;
        }

        public string UTMSourceName
        {
            get;
            set;
        }

        #endregion

        #region CityTypeMapping

        public int CityTypeId
        {
            get;
            set;
        }

        public string StrCityId
        {
            get;
            set;
        }

        #endregion

        #region Brand

        public int Rank
        {
            get;
            set;
        }

        #endregion

        #region Utilization

        public string TimeSpentOnCall
        { get; set; }

        public string WorkingHours
        { get; set; }

        public string ConversionTarget
        { get; set; }

        #endregion

        #region Product

        public bool CalculatdServiceTax
        {
            get;
            set;
        }
        public int ParentPlanId
        {
            get;
            set;
        }
        public int MobPriority
        {
            get;
            set;
        }
        public int MobQuotePref_ID
        {
            get;
            set;
        }
        public int SubProductTypeId
        {
            get;
            set;
        }
        public int OrderOn
        {
            get;
            set;
        }
        public int MinPremium
        {
            get;
            set;
        }

        public int MaxSumAssured
        {
            get;
            set;
        }
        public int MinSumAssured
        {
            get;
            set;
        }
        public string ThidParty_UIN
        {
            get;
            set;
        }
        public string ThirdParty_PrdtCode
        {
            get;
            set;
        }
        public bool IsSubscribe
        {
            get;
            set;
        }

        public Decimal PolicyFee
        {
            get;
            set;
        }

        public bool IsIntegratedOnline
        {
            get;
            set;
        }
        public bool IsServiceTaxApplicable
        {
            get;
            set;
        }
        public bool EnableBuyButton
        {
            get;
            set;
        }
        public int priority
        {
            get;
            set;
        }
        public int EntryAgeForAdult
        {
            get;
            set;
        }
        public int EntryAgeForChild
        {
            get;
            set;
        }
        public int MaxAgeForChild
        {
            get;
            set;
        }
        public int QuotePref_ID
        {
            get;
            set;
        }

        public string Dependency
        {
            get;
            set;
        }
        public string PrefText
        {
            get;
            set;
        }
        public string PremiumFrequency
        {
            get;
            set;
        }
        public string PlanFeature
        {
            get;
            set;
        }
        public string URLToBrochure
        {
            get;
            set;
        }
        public string ProductName
        { get; set; }

        public string ProductActualName
        { get; set; }

        public int Category
        { get; set; }

        #endregion

        #region Supplier

        public int SupplierId
        { get; set; }

        public string SupplierName
        { get; set; }

        public string SupplierFullName
        { get; set; }

        public string SupplierActualName
        { get; set; }

        public int VehicleTypeId
        { get; set; }

        public string IncreaseDecrease
        { get; set; }

        public decimal LoadingPercentage
        { get; set; }


        public string SupplierCode
        { get; set; }

        public int SupplierTypeId
        { get; set; }


        #endregion

        #region VIP Booking  Properties

        public string Head1
        { get; set; }

        public string Head2
        { get; set; }

        public string Head3
        { get; set; }

        public string Condition1
        { get; set; }

        public string Condition2
        { get; set; }

        public string Condition3
        { get; set; }

        public string Text1
        { get; set; }

        public string Text2
        { get; set; }

        public string Text3
        { get; set; }

        public string Operator1
        { get; set; }

        public string Operator2
        { get; set; }

        public string Operator3
        { get; set; }

        public string FilterQuery
        { get; set; }


        #endregion

        public int Grade
        {
            get;
            set;
        }

        #region SubProductType

        public string StrSubProductTypeId
        {
            get;
            set;
        }
        public int IntproductId
        {
            get;
            set;
        }
        public bool IsCreateLead
        {
            get;
            set;
        }

        public int GroupType
        {
            get;

            set;
        }

        public int AutoAllocation
        {
            get;

            set;
        }

        #endregion

        public bool IsAsterickDialer
        {
            get;
            set;
        }

        public string CampaignName
        {
            get;
            set;
        }

        public string Url
        {
            get;
            set;
        }

        public int LogOutType
        {
            get;
            set;
        }

        public bool IsLoginActive { get; set; }

        public string ExistSessionId { get; set; }

        //Deepankar Singh 03-05-2016
        public int CallingType { get; set; }

        /*AV Details (Akhilesh Jatav (2015-02-02)) Start*/
        public String AVLicenseNumber { get; set; }
        public DateTime AVLicenseStartDate { get; set; }
        public DateTime AVLicenseExpiryDate { get; set; }
        public Boolean? AVIsVerifier { get; set; }
        public Boolean AVIsActive { get; set; }
        //public List<AuthorisedVerifierDetails> AuthorisedVerifierData { get; set; }
        /*AV Details (Akhilesh Jatav (2015-01-02)) End*/

        //For 1 lead queueAgent//
        public bool IsQueueAgent { get; set; }
        public bool IsOneLead { get; set; }

        public bool IsProgressive { get; set; }
        public bool IsWFH { get; set; }
        public string Asterisk_IP { get; set; }
        public string Asterisk_Url { get; set; }
        public string DialerPWD { get; set; }
        public string CallingCompany { get; set; }
        public string DIDNo { get; set; }
        public string ContactNo { get; set; }

        public string Context { get; set; }
        public string Queue { get; set; }
        public Decimal AvgRating { get; set; }
        public Decimal ResRating { get; set; }
        public Decimal NorRating { get; set; }
        public Decimal QualityScore { get; set; }
        public Decimal IssuanceScore { get; set; }
        public long ManagerId { get; set; }
        public bool IsPODUser { get; set; }
        public string PODManagerId { get; set; }
        public string ManagerName { get; set; }
        public string ManagerEmployeeId { get; set; }

        public bool IsSOSAgent { get; set; }
        public bool IsNewSV { get; set; }
        public bool IsEnableVC { get; set; }
        public bool IsEnableChat { get; set; }



        public bool IsWebphone { get; set; }
        public bool IsSOSGroup { get; set; }
        public string Token { get; set; }
        public string UserBand { get; set; }
        public string Landingurl { get; set; }
        public DateTime MinVoucherVal { get; set; }
        public DateTime MaxVoucherVal { get; set; }
        public bool IsVoucherEnable { get; set; }

        public string mToken { get; set; }
        public DataTable UserBUMapping
        {
            get;
            set;
        }
        public bool IsOTPSend { get; set; }
        public bool IsOTPVerified { get; set; }

        public DataTable MenuList
        {
            get;
            set;
        }
        public DataTable GroupList
        {
            get;
            set;
        }
        public DataTable QueueList
        {
            get;
            set;
        }
    }

}