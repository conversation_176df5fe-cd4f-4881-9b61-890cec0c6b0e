﻿
using System;
using System.Collections.Generic;
using MongoDB.Bson.Serialization.Attributes;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class LeadProperty
    {
        public string Property { get; set; }
        public string DataType { get; set; }
    }
    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class RuleCondition
    {
        public MongoDB.Bson.ObjectId _id { get; set; }
        public MongoDB.Bson.ObjectId Rule { get; set; }
        public string Property { get; set; }
        public string PropertyType { get; set; }
        public string Operator { get; set; }
        public dynamic ReferenceValue { get; set; }
        public string Description { get; set; }
        public byte IsActive { get; set; }
    }

    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class AllocationRule
    {
        public MongoDB.Bson.ObjectId _id { get; set; }
        public string RuleName { get; set; }
        public string RuleDescription { get; set; }
        public List<RuleCondition> Conditions { get; set; }
        public string RuleOutput { get; set; }
        public bool CheckBaseCondition { get; set; }
        public byte IsActive { get; set; }
    }
    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class Engine
    {
        public MongoDB.Bson.ObjectId _id { get; set; }
        public string EngineName { get; set; }
        public int ProductId { get; set; }
        public List<MongoDB.Bson.ObjectId> Rules { get; set; }
        public byte IsActive { get; set; }
    }

    public class Engines
    {

        // Get LeadProcess - NRI, TELGU etc
        public const string GetLeadProcess = "GetLeadProcess";
        public const string UseLeadScoreForRanking = "UseLeadScoreForRanking";

        public const string AdhocRanks = "AdhocRanks";

        public const string CheckCoreLeads = "CheckCoreLeads";
        // Core Lead rank categories
        public const string CoreRanking1 = "CoreRanking1";
        public const string CoreRanking2 = "CoreRanking2";
        public const string CoreRanking3 = "CoreRanking3";
        //public const string CoreRanking4 = "CoreRanking4";

        // NonCore Lead rank categories
        public const string NonCoreRanking1 = "NonCoreRanking1";
        public const string NonCoreRanking2 = "NonCoreRanking2";
        public const string NonCoreRanking3 = "NonCoreRanking3";
        public const string NonCoreRanking4 = "NonCoreRanking4";

        // Get Ranks for core lead
        public const string RanksCoreRankingN = "RanksCoreRanking";

        // Get Ranks for Non core lead
        public const string RanksNonCoreRankingPre = "RanksNonCoreRankingPre";
        public const string RanksNonCoreRankingN = "RanksNonCoreRanking";
        public const string RanksNonCoreRankingPost = "RanksNonCoreRankingPost";

    }

    public class Processes
    {
        public const string CORE = "CORE";
        public const string NON_CORE = "NON_CORE";
        public const string NRI = "NRI";

    }

    public class SmeEngines
    {
        public const string WC = "WC";
        public const string WC_Group = "WC_Group";
        public const string PIForDoc = "PIForDoc";
        public const string PIForDoc_Group = "PI_For_Doc_Group";
        public const string Marine = "Marine";
        public const string Marine_Group = "Marine_Group";
        public const string ADHOC = "AD-HOC-Group";
        public const string CAR = "CAR";
        public const string CAR_Group = "CAR_Group";
        public const string EAR = "EAR";
        public const string EAR_Group = "EAR_Group";
        public const string CPM = "CPM";
        public const string CPM_Group = "CPM_Group";
        public const string Burglary = "Burglary";
        public const string Burglary_Group = "Burglary_Group";
        public const string Fire = "Fire";
        public const string Fire_Group = "Fire_Group";
        public const string OfficePackage = "OfficePackage";
        public const string OfficePackage_Group = "OfficePackage_Group";
        public const string ShopOwner = "ShopOwner";
        public const string ShopOwner_Group = "ShopOwner_Group";
        public const string CommercialCrime = "CommercialCrime";
        public const string CommercialCrime_Group = "CommercialCrime_Group";
        public const string CyberRisk = "CyberRisk";
        public const string CyberRisk_Group = "CyberRisk_Group";
        public const string DirectorsOfficers = "DirectorsOfficers";
        public const string DirectorsOfficers_Group = "DirectorsOfficers_Group";
        public const string DroneInsurance = "DroneInsurance";
        public const string DroneInsurance_Group = "DroneInsurance_Group";
        public const string GeneralLiability = "GeneralLiability";
        public const string GeneralLiability_Group = "GL_Group";
        public const string PublicLiability = "PublicLiability";
        public const string PublicLiability_Group = "PublicLiability_Group";
        public const string PI_For_Company = "PI_For_Company";
        public const string PI_For_Company_Group = "PI_For_Company_Group";
        public const string GHI = "GHI";
        public const string GHI_Group = "GHI_Group";
        public const string GPA = "GPA";
        public const string GPA_Group = "GPA_Group";
        public const string GTL = "GTL";
        public const string GTL_Group = "GTL_Group";
        public const string GroupCareCovid19 = "GroupCareCovid19";
        public const string GroupCareCovid19_Group = "GroupCareCovid19_Group";
        public const string GroupGratuity = "GroupGratuity";
        public const string GroupGratuity_Group = "GroupGratuity_Group";
        public const string GroupTotalProtect = "GroupTotalProtect";
        public const string GroupTotalProtect_Group = "GroupTotalProtect_Group";
        public const string GroupTravel = "GroupTravel";
        public const string GroupTravel_Group = "GroupTravel_Group";
        public const string KeyMan = "KeyMan";
        public const string KeyMan_Group = "KeyMan_Group";
        public const string OPD = "OPD";
        public const string OPD_Group = "OPD_Group";
        public const string PI_Medical_Establishment = "PI_Medical_Establishment";
        public const string PI_Medical_Group = "PI_Medical_Group";
        public const string CyberRiskIndividual = "CyberRiskIndividual";
        public const string CyberRiskIndividual_Group = "CyberRiskIndividual_Group";
        public const string SuretyBond = "SuretyBond";
        public const string SuretyBond_Group = "SuretyBond_Group";
        public const string TradeCredit = "TradeCredit";
        public const string TradeCredit_Group = "TradeCredit_Group";
    }

    public class SmeLeadRankEngines
    {
        public const string ADHOC_LeadRank = "ADHOC_LeadRank";
        public const string ADHOC_CustomUTM = "ADHOC_CustomUTM";
        public const string WC_LeadRank = "WC_LeadRank";
        public const string PIForDocLeadRank = "PIForDocLeadRank";
        public const string Marine_LeadRank = "Marine_LeadRank";
        public const string CAR_LeadRank = "CAR_LeadRank";
        public const string EAR_LeadRank = "EAR_LeadRank";
        public const string CPM_LeadRank = "CPM_LeadRank";
        public const string Burglary_LeadRank = "Burglary_LeadRank";
        public const string Fire_LeadRank = "Fire_LeadRank";
        public const string OfficePackageLeadRank = "OfficePackageLeadRank";
        public const string ShopOwner_LeadRank = "ShopOwnerLeadRank";
        public const string CommercialCrime_LeadRank = "CommercialCrime_LeadRank";
        public const string CyberRisk_LeadRank = "CyberRisk_LeadRank";
        public const string DirectorsOfficersLeadRank = "DirectorsOfficersLeadRank";
        public const string DroneInsuranceLeadRank = "DroneInsuranceLeadRank";
        public const string GL_LeadRank = "GL_LeadRank";
        public const string PublicLiabilityLeadRank = "PublicLiabilityLeadRank";
        public const string PIForCompanyLeadRank = "PIForCompanyLeadRank";
        public const string GHI_LeadRank = "GHI_LeadRank";
        public const string GPA_LeadRank = "GPA_LeadRank";
        public const string GTL_LeadRank = "GTL_LeadRank";
        public const string GroupCareCovid19LeadRank = "GroupCareCovid19LeadRank";
        public const string GroupGratuityLeadRank = "GroupGratuityLeadRank";
        public const string GroupTotalProtectLeadRank = "GroupTotalProtectLeadRank";
        public const string GroupTravelLeadRank = "GroupTravelLeadRank";
        public const string KeyMan_LeadRank = "KeyMan_LeadRank";
        public const string OPD_LeadRank = "OPD_LeadRank";
        public const string PIMedicalLeadRank = "PIMedicalLeadRank";
    }
}