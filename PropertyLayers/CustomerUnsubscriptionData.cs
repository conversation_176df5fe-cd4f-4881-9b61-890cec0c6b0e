﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{
    public class CustomerUnsubscriptionData
    {
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        //[DataMember(EmitDefaultValue = false)]
        //public string EmailId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsUnsubscribeAll { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<CommPreference> CommPreferences { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime RequestPushTime { get; set; }
    }
    public class CommPreference
    {
        [DataMember(EmitDefaultValue = false)]
        public string CategoryCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CategoryName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CategoryDescription { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ChannelCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool UnSubscribed { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsLocked { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ChannelId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int CategoryId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<int> ProductIds { get; set; }
    }


}
