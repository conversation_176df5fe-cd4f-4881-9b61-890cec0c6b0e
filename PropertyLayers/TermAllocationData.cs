using System;
using System.Collections.Generic;
using System.Reflection;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class TermLeadDetails
    {
        [DataMember]
        public LEADTYPE _LEADTYPE;

        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public int Age { get; set; }

        [DataMember]
        public int InsurerID { get; set; }

        [DataMember]
        public byte PreviousBooking { get; set; }

        [DataMember]
        public byte RepeatCustomer { get; set; }

        [DataMember]
        public long AnnualIncome { get; set; }

        [DataMember]
        public short CityID { get; set; }

        [DataMember]
        public string LeadSource { get; set; }

        [DataMember]
        public string Utm_source { get; set; }

        [DataMember]
        public string UTM_Medium { get; set; }

        [DataMember]
        public string Utm_campaign { get; set; }

        [DataMember]
        public byte StateID { get; set; }

        [DataMember]
        public string source { get; set; }

        [DataMember]
        public string Utm_term { get; set; }

        [DataMember]
        public string Country { get; set; }

        [DataMember]
        public DateTime CreatedOn { get; set; }

        [DataMember]
        public short LeadRank { get; set; }

        [DataMember]
        public short TempLeadRank { get; set; }

        [DataMember]
        public short SpecialLeadRank { get; set; }

        [DataMember]
        public string GroupCode { get; set; }

        [DataMember]
        public decimal ExistingLeadScore { get; set; }

        [DataMember]
        public decimal LeadScore { get; set; }

        [DataMember]
        public long AssigntoUserID { get; set; }

        [DataMember]
        public long AssignbyUserID { get; set; }

        [DataMember]
        public short JobID { get; set; }

        [DataMember]
        public short GroupID { get; set; }

        [DataMember]
        public short NewGroupID { get; set; }

        [DataMember]
        public short ProductID { get; set; }

        [DataMember]
        public short LeadGrade { get; set; }

        [DataMember]
        public byte StatusId { get; set; }

        [DataMember]
        public short SubStatusId { get; set; }

        [DataMember]
        public long CustomerId { get; set; }

        [DataMember]
        public short SelectionCount { get; set; }

        [DataMember]
        public DataStatus DataState { get; set; }

        [DataMember]
        public byte AssignedToGroup { get; set; }

        public string MobileNo { get; set; }

        [DataMember]
        public short InvestmentTypeID { get; set; }

        [DataMember]
        public short TwowheelerBookingCount { get; set; }

        [DataMember]
        public string SourcePage { get; set; }

        [DataMember]
        public int TotalPayout { get; set; }

        [DataMember]
        public short? educationQualificationId { get; set; }

        [DataMember]
        public string ProfessionType { get; set; }

        [DataMember]
        public string CreditScore { get; set; }

        [DataMember]
        public byte IsTobaccoUser { get; set; }

        [DataMember]
        public int LanguageRegionID { get; set; }

        [DataMember]
        public string brandName { get; set; }

        [DataMember]
        public byte IsSAChanged { get; set; }

        [DataMember]
        public byte IsDerivedIncome { get; set; }

        [DataMember]
        public short SubProductTypeId { get; set; }

        [DataMember]
        public byte TermCompare { get; set; }

        [DataMember]
        public byte LimitedPay { get; set; }

        [DataMember]
        public byte TropSelected { get; set; }

        [DataMember]
        public DateTime DOB { get; set; }

        [DataMember]
        public string UtmContent { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public byte ChatStatus { get; set; }

        [DataMember]
        public long AddOnParentID { get; set; }

        [DataMember]
        public long UserId { get; set; }
        
        [DataMember]
        public byte IsCTC { get; set; }

        [DataMember]
        public short SameCustFlag { get; set; }

        [DataMember]
        public long CityUpdatedBy { get; set; }

        [DataMember]
        public long PostCode { get; set; }

        [DataMember]
        public short VisitCount { get; set; }

        [DataMember]
        public string AssignmentProcess { get; set; }

        [DataMember]
        public bool IsAllocable { get; set; } = true;

        public object this[string propertyName]
        {
            get
            {
                PropertyInfo property = GetType().GetProperty(propertyName);
                if (property == null) return null;
                return property.GetValue(this, null);
            }
            set
            {
                PropertyInfo property = GetType().GetProperty(propertyName);
                property.SetValue(this, value, null);
            }
        }
    }

    [DataContract]
    public enum DataStatus
    {
        DataPending = 0,
        DataLoaded = 1,
        LeadAssigned = 2
    }

    public class Assign
    {
        public int reassignCount { get; set; }
        
        public byte NoAgent { get; set; }

        public long CrmLeads { get; set; } 
    }

    [DataContract]
    [Serializable]
    public class AllocateTermLeadResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public List<TermLeadDetails> LeadDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Error { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsSuccess { get; set; }
        public Dictionary<long, string> LeadErrors { get; set; }
    }
}


