﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace PropertyLayers
{
    [DataContract]
    public class CommPrefrencesType
    {
        [DataMember]
        public byte CommTypeID { get; set; }

        [DataMember]
        public string CommTypeName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CreatedBy { get; set; }

        [DataMember]
        public bool IsAllowed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        public string CreatedOn { get; set; }

        [DataMember]
        public byte CategoryId { get; set; }


        [DataMember]
        public bool IsEditable { get; set; }
        

        [DataMember]
        public string CategoryName { get; set; }
    }

    [DataContract]
    public class CommPrefrencesModel
    {
        [DataMember]
        public long LeadId { get; set; }
        [DataMember]
        public int CustomerId { get; set; }
        [DataMember]
        public Int64 UserId { get; set; }
        [DataMember]
        public string Source { get; set; }
        [DataMember]
        public string emailId { get; set; }
        [DataMember]
        public List<CommPrefrencesType> commObj { get; set; }
        [DataMember]
        public string clientIP { get; set; }
        [DataMember]
        public string Browser { get; set; }
        [DataMember]
        public string IPAddress { get; set; }
        [DataMember]
        public int ProductId { get; set; }

        [DataMember]
        public bool OTPVerified { get; set; }

    }

    public class PreferenceStatus
    {
        public long LeadId { get; set; }
        public long MobileNo { get; set; }
        public long CustomerId { get; set; }
        public byte CommType { get; set; }
        public bool IsBooking { get; set; }
        public int StatusId { get; set; }
        public string TriggerName { get; set; }
        public string Message { get; set; }
        public byte CategoryId { get; set; }
    }

    public class Result
    {
        [DataMember]
        public bool Status { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public int StatusCode { get; set; }

        [DataMember]
        public bool IsSuccess { get; set; }


    }
}
