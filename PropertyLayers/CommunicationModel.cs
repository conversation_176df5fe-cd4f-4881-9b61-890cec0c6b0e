﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
//using MongoDB.Driver.Builders;
using Newtonsoft.Json;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class CommunicationModel
    {
        private CommunicationType _CommType;
        [DataMember(EmitDefaultValue = false)]
        public ObjectId id
        {
            get;
            set;
        }
        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public string CommId
        {
            get;
            set;
        }

        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public string ConversationTopic
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long LeadID
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string CommunicationVersion
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public long SelectionID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long W_LeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadDate
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int CoreLeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int NeedID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int EnquiryID
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long CustID
        {
            get;
            set;
        }
        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public string CustName
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID
        {
            get;
            set;
        }
        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public string ProductName
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool IsSpam
        { get; set; }
        //[DataMember]
        //public List<Conversations> Conversations
        //{ get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int CommunicationType
        {
            get { return Convert.ToInt32(_CommType); }
            set { _CommType = (CommunicationType)value; }
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsBooking { get; set; }
        //[BsonIgnoreIfNull]
        //[DataMember(EmitDefaultValue = false)]
        //public Bookingdata BookingDetail { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CustT
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 CustP
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn
        {
            get;
            set;
        }
        //[BsonIgnore]
        //[DataMember]
        //public List<Document> DocumentDetails
        //{
        //    get;
        //    set;
        //}
        [BsonIgnore]
        [DataMember]
        public string CallTrackingID
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember]
        public bool IsCallVerification { get; set; }

        [BsonIgnore]
        [DataMember]
        public string TicketID
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string ReferenceID
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string ReferenceType
        {
            get;
            set;
        }

        [BsonIgnore]
        [DataMember]
        public string CommunicationID
        {
            get;
            set;
        }

        [DataMember]
        [BsonIgnoreIfDefault]
        public Dictionary<string, string> KeyPair
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember]
        public string AsteriskIP { get; set; }

        [DataMember]
        [BsonIgnoreIfDefault]
        public bool FromTxtSearch { get; set; }



        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public byte CategoryId
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool Redial
        {
            get;
            set;
        }
    }
    public enum InboundType
    {
        CustInfo = 0,
        LeadCreation = 1,
        AssignCTC = 2
    }
    public enum CommunicationType
    {
        ALL = 0,
        Email = 1,
        SMS = 2,
        Call = 3,
        Chat = 4,
        OTP = 5,
        IBCall = 6,
        InboundCall = 7,
        WhatsApp = 8
    }
    public class SendCommRequest
    {
        [Required]
        public int SubstatusId { get; set; }
        [Required]
        public string TriggerName { get; set; }
        [Required]
        public long LeadId { get; set; }
        [Required]
        public int CommunicationType { get; set; }
        [Required]
        public int ProductId { get; set; }
    }
    public class InputLinkdataEmail
    {
        [DataMember]
        public string NAME { get; set; }
        [DataMember]
        public string INSURER { get; set; }
        [DataMember]
        public string LINK { get; set; }
        [DataMember]
        public string LINK5 { get; set; }
    }
    public class InputLinkdataWhatsapp
    {
        [DataMember]
        public string CustomerName { get; set; }
        [DataMember]
        public string ScheduleCallbackLink { get; set; }
    }
    public class SendCommResponse
    {
        [DataMember]
        public string UUID
        {
            get;
            set;
        }
        [DataMember]
        public bool IsSuccess
        {
            get;
            set;
        }
        [DataMember]
        public string Message
        {
            get;
            set;
        }
        public string Description
        {
            get;
            set;
        }
    }
    public class MongoData
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string _id { get; set; }
        public string TriggerName { get; set; }
        public int substatusId { get; set; }
        public string substatus { get; set; }
        public string LeadSource { get; set; }
        public string CommunicationType { get; set; }
        public int[] ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Key { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Value { get; set; }
        public string IsActive { get; set; }
    }
    public class LeadDetailsforCommunication
    {
        public string LeadId { get; set; }
        public string Name { get; set; }
        public string InsurerName { get; set; }
        public string EmailId { get; set; }
        public string MobileNo { get; set; }
        public string ExitPointURL { get; set; }
        public string LeadSource { get; set; }
    }
    public class comv2requestEmail
    {
        public string LeadID { get; set; }
        public int ProductId { get; set; }
        public string TriggerName { get; set; }
        public InputLinkdataEmail InputData { get; set; }
        public string CommunicationType { get; set; }
        public string[] To { get; set; }
    }
    public class comv2requestWhatsapp
    {
        public string LeadID { get; set; }
        public int ProductId { get; set; }
        public string TriggerName { get; set; }
        public InputLinkdataWhatsapp InputData { get; set; }
        public string CommunicationType { get; set; }
        public string MobileNo { get; set; }
        public string[] To { get; set; }
        public string Subject { get; set; }
    }
    public class URLShortener
    {
        public string LongURL { get; set; }
        public string Source { get; set; }
    }


}
