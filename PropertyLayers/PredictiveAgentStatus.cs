﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace PropertyLayers
{

    [DataContract]
    [Serializable]
    public class PredictiveAgentStatus : PredictiveAgentCalls
    {
        [DataMember(EmitDefaultValue = false)]
        [IgnoreDataMember]
        public ObjectId id { get; set; }

        [BsonElement("agentid")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string AgentId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string AgentCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int opensv { get; set; }

        //[DataMember(EmitDefaultValue = false)]
        //[BsonIgnoreIfDefault]
        //public RetainerPredictiveDialing Document { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [IgnoreDataMember]
        [BsonIgnoreIfDefault]
        public DateTime _updatedAt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public List<string> UserGroup { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public List<string> TL { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string TLName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Grade { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Context { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int remainingpausetime { get; set; }

        private DateTime _pausetime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime pausetime
        {
            get
            {
                if (_pausetime != null && _pausetime.Kind == DateTimeKind.Utc)
                {
                    _pausetime = _pausetime.ToLocalTime();
                }
                return _pausetime;
            }
            set
            {
                this._pausetime = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool ispause { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime lastActiveTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime LastUpdatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime? lastpausetime { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsProgressive { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string AsteriskToken { get; set; }

        private DateTime _logouttime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime logouttime
        {
            get
            {
                if (_logouttime != null && _logouttime.Kind == DateTimeKind.Utc)
                {
                    _logouttime = _logouttime.ToLocalTime();
                }
                return _logouttime;
            }
            set
            {
                this._logouttime = value;
            }
        }

        private DateTime _holdtime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime holdtime
        {
            get
            {
                if (_holdtime != null && _holdtime.Kind == DateTimeKind.Utc)
                {
                    _holdtime = _holdtime.ToLocalTime();
                }
                return _holdtime;
            }
            set
            {
                this._holdtime = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Asterisk_Url { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string AgentIP { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string RoleId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string DIDNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool? IsWFH { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string CallingCompany { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool? IsCustAnswered { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Process { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PredictiveAgentCalls
    {
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string LeadId { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public int TotalCalls { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string CallType { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public int TotalUniqueCalls { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public int TotalTalkTime { get; set; }
        
        [DataMember(EmitDefaultValue = true)]
        public int TotalConnectedCalls { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string CallId { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int16 VCCount { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int16 VCConnectCount { get; set; }
    }

    [DataContract]
    [Serializable]
    public class AgentStatusReport
    {
       

        [BsonElement("AgentCode")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Eid { get; set; }

        [BsonElement("Grade")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Grade { get; set; }

        [BsonElement("UserName")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string UName { get; set; }

        [BsonElement("agentid")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Uid { get; set; }

        [BsonElement("Context")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string ctx { get; set; }

        [BsonElement("_updatedAt")]
        [DataMember(EmitDefaultValue = false)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime ts { get; set; }

        [BsonElement("LeadId")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string LeadId { get; set; }

        [BsonElement("TLName")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string TLName { get; set; }

        [BsonElement("status")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string status { get; set; }
        
        [BsonElement("TotalCalls")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int TC { get; set; }

        [BsonElement("TotalTalkTime")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public long TTT { get; set; }

        [BsonElement("TotalConnectedCalls")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int TCC { get; set; }

        [BsonElement("TotalUniqueCalls")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int TUC { get; set; }

        [BsonElement("ispause")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool ispause { get; set; }

        [BsonElement("remainingpausetime")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int rtp { get; set; }

        [BsonElement("logouttime")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]        
        public DateTime lt { get; set; }


        [BsonElement("_id")]
        [IgnoreDataMember]
        public ObjectId id { get; set; }
        [BsonIgnoreIfDefault]
       // public RetainerPredictiveDialing Document { get; set; }
        public int opensv { get; set; }
        public List<string> TL { get; set; }
        public List<string> UserGroup { get; set; }
        public DateTime LastUpdatedOn { get; set; }
        public DateTime? lastpausetime { get; set; }
        public bool IsProgressive { get; set; }
        public string AsteriskToken { get; set; }
        public string UserId { get; set; }
        private DateTime _pausetime;        
        public DateTime pausetime
        {
            get
            {
                if (_pausetime != null && _pausetime.Kind == DateTimeKind.Utc)
                {
                    _pausetime = _pausetime.ToLocalTime();
                }
                return _pausetime;
            }
            set
            {
                this._pausetime = value;
            }
        }
    }

}
