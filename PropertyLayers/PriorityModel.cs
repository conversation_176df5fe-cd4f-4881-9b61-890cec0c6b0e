﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class PriorityModel
    {
        

        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 LeadPoints { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<long> ActiveLeadSet { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        private DateTime leadDate;

        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedOn
        {
            get
            {
                if (leadDate != null && leadDate.Kind == DateTimeKind.Utc)
                {
                    leadDate = leadDate.ToLocalTime();
                }
                return leadDate;
            }
            set
            {
                this.leadDate = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 LeadRank { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public SupplierData Supplier { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public LeadStatusData LeadStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public userData User { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public RevisitData Revisit { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallBackData CallBack { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallData Call { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public EventTypeEnum EventType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public LeadCategoryEnum LeadCategory { get; set; }


        [DataMember(EmitDefaultValue = false)]// Reason for Lead Coming in Priority
        public string Reason { get; set; }

        private DateTime Skiptime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime SkippingTime
        {
            get
            {
                if (Skiptime != null && Skiptime.Kind == DateTimeKind.Utc)
                {
                    Skiptime = Skiptime.ToLocalTime();
                }
                return Skiptime;
            }
            set
            {
                this.Skiptime = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]// EmailRevert
        public bool IsEmailRevert { get; set; }

        [DataMember(EmitDefaultValue = false)]// Inbound flag
        public bool IsInbound { get; set; }

        [DataMember(EmitDefaultValue = false)]// selection flag
        public bool IsSelection { get; set; }

        [DataMember(EmitDefaultValue = false)]// Referral flag
        public bool IsReferral { get; set; }

        [DataMember(EmitDefaultValue = false)]// NRI flag
        public bool IsNRI { get; set; }

        private DateTime pointDeductionTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime PointDeductTime
        {
            get
            {
                if (pointDeductionTime != null && pointDeductionTime.Kind == DateTimeKind.Utc)
                {
                    pointDeductionTime = pointDeductionTime.ToLocalTime();
                }
                return pointDeductionTime;
            }
            set { pointDeductionTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]// Flag if The lst bucket was Rest
        public bool IsLastBucketRest { get; set; }

        private DateTime PolicyExpDate;
        [DataMember(EmitDefaultValue = false)]// Mainly for Motor
        public DateTime PrevPolicyExpDate
        {
            get
            {
                if (PolicyExpDate != null && PolicyExpDate.Kind == DateTimeKind.Utc)
                {
                    PolicyExpDate = PolicyExpDate.ToLocalTime();
                }
                return PolicyExpDate;
            }
            set { PolicyExpDate = value; }
        }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public bool MongoOperation { get; set; }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public bool LeadPointOperation { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte CallReleaseCount { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 SkipDurationHrs { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 ReleaseStatus { get; set; }

        private DateTime expectedAppearTime;
        [DataMember(EmitDefaultValue = false)]

        public DateTime ExpectedAppearingTime
        {
            get
            {
                if (expectedAppearTime != null && expectedAppearTime.Kind == DateTimeKind.Utc)
                {
                    expectedAppearTime = expectedAppearTime.ToLocalTime();
                }
                return expectedAppearTime;
            }
            set { expectedAppearTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte star { get; set; }


        private DateTime callReleasets;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CallReleaseTime
        {
            get
            {
                if (callReleasets != null && callReleasets.Kind == DateTimeKind.Utc)
                {
                    callReleasets = callReleasets.ToLocalTime();
                }
                return callReleasets;
            }
            set { callReleasets = value; }
        }


        private DateTime PaymentAttemptTs;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentAttemptTime
        {
            get
            {
                if (PaymentAttemptTs != null && PaymentAttemptTs.Kind == DateTimeKind.Utc)
                {
                    PaymentAttemptTs = PaymentAttemptTs.ToLocalTime();
                }
                return PaymentAttemptTs;
            }
            set { PaymentAttemptTs = value; }
        }


        private DateTime _PaymentFailureTime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentFailureTime
        {
            get
            {
                if (_PaymentFailureTime != null && _PaymentFailureTime.Kind == DateTimeKind.Utc)
                {
                    _PaymentFailureTime = _PaymentFailureTime.ToLocalTime();
                }
                return _PaymentFailureTime;
            }
            set { _PaymentFailureTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string PageName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 Country { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime DOB { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool EmailOnly { get; set; }

        private DateTime _TripStart;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime TripStart
        {
            get
            {
                if (_TripStart != null && _TripStart.Kind == DateTimeKind.Utc)
                {
                    _TripStart = _TripStart.ToLocalTime();
                }
                return _TripStart;
            }

            set { _TripStart = value; }
        }

        private DateTime _TripEnd;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime TripEnd
        {
            get
            {
                if (_TripEnd != null && _TripEnd.Kind == DateTimeKind.Utc)
                {
                    _TripEnd = _TripEnd.ToLocalTime();
                }
                return _TripEnd;
            }

            set { _TripEnd = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 SumInsured { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 BookedId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsBooked { get; set; }

        private DateTime _BookedTime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime BookedTime
        {
            get
            {
                if (_BookedTime != null && _BookedTime.Kind == DateTimeKind.Utc)
                {
                    _BookedTime = _BookedTime.ToLocalTime();
                }
                return _BookedTime;
            }

            set { _BookedTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public TicketObj Ticket { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public Int16 Counter { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public Int16 CustomerNANCAttempt { get; set; }

        private DateTime _CustomerCalltime;
        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public DateTime CustomerCalltime
        {
            get
            {
                if (_CustomerCalltime != null && _CustomerCalltime.Kind == DateTimeKind.Utc)
                {
                    _CustomerCalltime = _CustomerCalltime.ToLocalTime();
                }
                return _CustomerCalltime;
            }

            set { _CustomerCalltime = value; }
        }

        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public Int16 CustomerTodayNANC { get; set; }

        private DateTime _ProposalError;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ProposalError
        {
            get
            {
                if (_ProposalError != null && _ProposalError.Kind == DateTimeKind.Utc)
                {
                    _ProposalError = _ProposalError.ToLocalTime();
                }
                return _ProposalError;
            }
            set { _ProposalError = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 NRIAreaCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int64 RevisitCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Decimal LeadPriorityScore { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public short InvestmentTypeId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public DateTime? GraceEndDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime? EmiDueDate;


        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime PFFilledTime
        {
            get
            {
                if (_PFFilledTime != null && _PFFilledTime.Kind == DateTimeKind.Utc)
                {
                    _PFFilledTime = _PFFilledTime.ToLocalTime();
                }
                return _PFFilledTime;
            }
            set { _PFFilledTime = value; }
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime RMCSTime
        {
            get
            {
                if (_RMCSTime != null && _RMCSTime.Kind == DateTimeKind.Utc)
                {
                    _RMCSTime = _RMCSTime.ToLocalTime();
                }
                return _RMCSTime;
            }
            set { _RMCSTime = value; }
        }

        private DateTime _PFFilledTime;
        private DateTime _RMCSTime;

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 ScoreModel { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsAppointed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DncData DNC { get; set; }

        private DateTime _AppointmentTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentTime
        {
            get
            {
                if (_AppointmentTime != null && _AppointmentTime.Kind == DateTimeKind.Utc)
                {
                    _AppointmentTime = _AppointmentTime.ToLocalTime();
                }
                return _AppointmentTime;
            }
            set { _AppointmentTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public AppointmentData Appointment { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public AppointmentTypeEnum AppointmentTypeEnum { get; set; }

    }

    [DataContract]
    public class DncData
    {
        private DateTime DncTime;
        [DataMember(EmitDefaultValue = false)]
        public Int16 CoolingPeriod { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Score { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (DncTime != null && DncTime.Kind == DateTimeKind.Utc)
                {
                    DncTime = DncTime.ToLocalTime();
                }
                return DncTime;
            }
            set { this.DncTime = value; }
        }
    }

    [DataContract]
    public class AppointmentData
    {
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ScheduledOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public AppointmentSourceEnum Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }
    }

    public enum AppointmentSourceEnum
    {
        Default = 0,
    }
    [DataContract]
    public class LeadStatusData
    {
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public List<long> Leads { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte StatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime Statustime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Status { get; set; }
    }
    [DataContract]
    public class SupplierData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 PlanID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SupplierID { get; set; }
    }
    [DataContract]
    public class userData
    {
        [BsonIgnore]
        public Int32 AssignedID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        private DateTime _AssignedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AssignedOn
        {
            get
            {
                if (_AssignedOn != null && _AssignedOn.Kind == DateTimeKind.Utc)
                {
                    _AssignedOn = _AssignedOn.ToLocalTime();
                }
                return _AssignedOn;
            }
            set { _AssignedOn = value; }
        }

        private DateTime _FirstAssignedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime FirstAssignedOn
        {
            get
            {
                if (_FirstAssignedOn == DateTime.MinValue)
                    _FirstAssignedOn = AssignedOn.ToLocalTime().Date;
                else if (_FirstAssignedOn != null && _FirstAssignedOn.Kind == DateTimeKind.Utc)
                {
                    _FirstAssignedOn = _FirstAssignedOn.ToLocalTime();
                }

                return _FirstAssignedOn;
            }
            set { _FirstAssignedOn = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public byte Grade { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool Reassigned { get; set; }
    }

    [DataContract]
    public class RevisitData
    {
        private DateTime RevisitTime;

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (RevisitTime != null && RevisitTime.Kind == DateTimeKind.Utc)
                {
                    RevisitTime = RevisitTime.ToLocalTime();
                }
                return RevisitTime;
            }
            set { this.RevisitTime = value; }
        }



        [DataMember(EmitDefaultValue = false)]
        public RevisitType RevisitType { get; set; }
    }

    [DataContract]
    public class CallBackData
    {
        private DateTime cbTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CBtime
        {
            get
            {
                if (cbTime != null && cbTime.Kind == DateTimeKind.Utc)
                {
                    cbTime = cbTime.ToLocalTime();
                }
                return cbTime;
            }
            set { cbTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public byte Duration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public CallBackTypeEnum CallBackType { get; set; }

        private DateTime CBCreatedTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (CBCreatedTime != null && CBCreatedTime.Kind == DateTimeKind.Utc)
                {
                    CBCreatedTime = CBCreatedTime.ToLocalTime();
                }
                return CBCreatedTime;
            }
            set { this.CBCreatedTime = value; ; }
        }// callBack created Time

        [DataMember(EmitDefaultValue = false)]
        public bool IsPaymentCB { get; set; }
    }

    [DataContract]
    public class CallData
    {
        private DateTime callDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime calltime
        {
            get
            {
                if (callDate != null && callDate.Kind == DateTimeKind.Utc)
                {
                    callDate = callDate.ToLocalTime();
                }
                return callDate;
            }
            set { callDate = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Duration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TalkTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Disposition { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 lastNCallTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TotalTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CallAttempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 NANC_Attempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<string> Shifts { get; set; }// This will contain  M A E for morning ,afternoon and Evening

        [DataMember(EmitDefaultValue = false)]   // flag if true Need to deduct point 
        public bool DeductPoint { get; set; }

        [DataMember(EmitDefaultValue = false)]   // flag if true Need to deduct point 
        public Int16 TodaysAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int16 TodaysNANCAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Week_Attempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Current_Week { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 uid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 TodayAnswered { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Boolean IsProcessed { get; set; }
        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int16 LastNminuteNANCeAttempts { get; set; } // Nminute=30 Minutes as of now
        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int32 TodayTalktime { get; set; }
        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        [BsonIgnore]
        public DateTime LastCallTime { get; set; }


    }

    public enum EventTypeEnum
    {
        Default = 0,
        selection = 1,
        Assignment = 2,
        Call = 3,
        CallBack = 4,
        Revisit = 5,
        statusupdate = 6,
        Reject = 7,
        Book = 8,
        NewLead = 9,
        SingleNA1Hr = 10,
        ReleaseLeads = 11,
        SkipLead = 12,// Not in Use Right Now,
        OneTimeDataPreparation = 13
    }

    public enum RevisitType
    {
        Default = 0,
        Inbound = 1,
        Email = 2,
        WebVisit = 3,
        Ctc = 4,
        BajajCustomer = 5,
        QuoteShared = 6,
        RevisionShared = 7
    }

    public enum CallBackTypeEnum
    {
        Default = 0,
        CustRequested = 1,
        CustAgreed = 2,
        AgentBestGuess = 3,
        SystemSetCB = 4

    }


    public class LeadPointsDTO
    {
        public Int64 LeadId;
        public Int16 Points;
        public byte PointType;
        public byte PointSubType;
        public bool IsReset;
        public bool IsPaymentCB;
        public string Remarks;
        public bool IsPointDeducted;
        public Int16 CurrentWeek;
        public Int16 WeekPoints;
        public DateTime FirstAssignedOn;
    }

    public enum LeadCategoryEnum
    {
        Default = 0,
        PaymentCB = 1,
        ActiveRevisit = 2,
        ActiveNew = 3,
        ActiveCB = 4,
        PassiveRevisit = 5,
        PassiveNew = 6,
        PassiveCB = 7,
        SecondAttemptPCB = 8,
        SecondAttemptActvRevisit = 9,
        SecondAttemptActvNew = 10,
        SecondAttemptActvCB = 11,
        SecondAttemptPasvRevisit = 12,
        SecondAttemptPasvNew = 13,
        SecondAttemptPasvCB = 14,
        UnansweredLeads = 15,
        RestLeads_1 = 16,
        SecondAttemptRestLeads = 17,
        RestLeads_2 = 18,
        CallReleasedLeads = 19,
        RecentExpiry = 20,
        SkippedLeads = 21,
        FutureCallBackLeads = 22,
        BdayLeads = 23,
        UnansweredRecentLeads = 24,
        EmailRevisit = 25,
        SecondAttemptEmailRevisit = 26,
        PaymentFailure = 27,
        SecondAttemptPaymentFailure = 28,
        CTCRevisit = 29,
        BookedLead = 30,
        RevisitAddLead = 31,
        allLeadpopup = 32,
        TopAddLead = 33,
        CTCLead = 33,
        Search = 34,
        CTC = 35,
        SecondAttemptRevisitCTC = 36,
        PredictiveAdd = 37,
        BajajCustomerRevisit = 38,
        MissedCB = 39,
        TicketUpdate = 40,
        StatusChange = 41,
        BookedCB = 42,
        NoLeadPopup = 43,
        ProposalError = 44,
        QuoteShared = 45,
        RestPriorityLeads = 46,
        RevisionShared = 47,
        TodayExpiry = 48,
        SecondAttemptTodayExpiry = 49,
        PFFilled = 50,
        SecondAttemptPFFilled = 51,
        RMLeads = 52,
        CancelBookedLead = 53,
        SOSDocTickets = 54,
        SOSRest = 55
    }


    [DataContract]
    public class CallDataHistory
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CallDate;
        [DataMember(EmitDefaultValue = false)]
        public Int32 TalkTime;
        [DataMember(EmitDefaultValue = false)]
        public Int16 Duration;
        [DataMember(EmitDefaultValue = false)]
        public string CallType;
        [DataMember(EmitDefaultValue = false)]
        public string Status;
        [DataMember(EmitDefaultValue = false)]
        public string CallId;
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId;
        [DataMember(EmitDefaultValue = false)]
        public Int64 CallDataId;
    }
    public class LeadCallDetailsDTO
    {
        public long ParentId;
        public int Totaltalktime;
        public int lastNtalktime;
        public int productId;
        public Int64 userID;
        public string LeadSource;
        public int TotalDuration;
        public MobileStatusEnum MobileStatus;
    }

    public class InternalIps
    {
        public ObjectId _id { get; set; }
        public string IP { get; set; }
    }

    public class SortingPriorityModel
    {
        public PriorityModel PriorityModel { get; set; }
        public DateTime SortingDate { get; set; }
    }

    [DataContract]
    public class Response
    {
        [DataMember]
        public bool status { get; set; }
        [DataMember]
        public string message { get; set; }
        [DataMember]
        public string LeadId { get; set; }
    }

    [DataContract]
    public class ReleasePointData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmployeeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public byte Status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime RequestDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ExpiryDate { get; set; }

    }

    [DataContract]
    public class ReleaseLeadRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Int64> LeadIds { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Status { get; set; }
    }

    [DataContract]
    public class RejectLeads
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Int64> LeadIds { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }
    }

    public enum Actions
    {
        Default = 0,
        LeadOpened = 1,
        CallInitiated = 2,
        CallEnded = 3,
        DoneClicked = 4,
        NoLeadPopup = 5
    }

    [DataContract]
    [Serializable]
    public class ReassignedLead
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReAssignedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string status { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReAssignedTo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReAssignedToGroup { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ReAssignedDate { get; set; }
    }

    public class PODUserDetails
    {

        public string EmployeeId;
        public Int64 UserID;

    }

    public class OnlineCustomerInfo
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        public string Page { get; set; }
        public string CustName { get; set; }
        private DateTime _ts;
        public DateTime ts
        {
            get
            {
                if (_ts != null && _ts.Kind == DateTimeKind.Utc)
                {
                    _ts = _ts.ToLocalTime();
                }
                return _ts;
            }
            set { _ts = value; }
        }
        public Int64 AgentId { get; set; }
        public string RoomCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long visitLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long visitEnquiryID { get; set; }
    }

    public class OnlineCustomerTracking
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        public string Page { get; set; }
        public string CustName { get; set; }
        public DateTime ts { get; set; }
        public Int16 AgentId { get; set; }
        public string status { get; set; }
    }


    [DataContract]
    [Serializable]
    public class UserLeadDistribution
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public ObjectId _id { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> AgentAllLeads;
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsPointGTZero; // out of AgentAllLeads
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsNotCalledIn4Days; // out of LeadsPointGTZero
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsWithNoFCB;// out of LeadsNotCalledIn4Days
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsNotSkipped;// out of LeadsWithNoFCB
        [DataMember(EmitDefaultValue = false)]
        public int TotalTalkTime;
        [DataMember(EmitDefaultValue = false)]
        public byte DaysPresent;
        [DataMember(EmitDefaultValue = false)]
        public UserManagerMapping oUserManagerMapping;
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts;
    }

    public class UserManagerMapping
    {
        public Int64 UserId;
        public string Username;
        public string UserEmpId;
        public Int64 MgrId;
        public string MgrName;
        public string MgrEmpID;

    }

    public class Next5WidgetLead
    {
        public long LeadId;
        public string Name;
        public DateTime ts;
        public string Reason;
        public byte Priority;
        public long CustomerId;
        public short ProductId;
        [BsonIgnoreIfDefault]
        public string CallStatus;
        public short ReasonId;
        public bool EmailOnly;
        public bool IsReligare;
        public Int16 Counter;
        public string ProposalNo;
        public long BookingId;
        public string EncryptedLeadId;
    }

    [DataContract]
    public class CustJourneyTrackingInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId;
        [DataMember(EmitDefaultValue = false)]
        public string Name;
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CBTime;
        [DataMember(EmitDefaultValue = false)]
        public bool IsPaymentAttempted;
        [DataMember(EmitDefaultValue = false)]
        public string CurrentPage;
        [DataMember(EmitDefaultValue = false)]
        public String EligibleNCB;
        [DataMember(EmitDefaultValue = false)]
        public String PreviousNCB;
        [DataMember(EmitDefaultValue = false)]
        public Int16 VoluntaryExcess;
        [DataMember(EmitDefaultValue = false)]
        public bool IsCNG;
    }

    public class UserNext5Leads
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long UserId;
        public List<Next5WidgetLead> Leads;

        [BsonIgnoreIfDefault]
        public List<Next5WidgetLead> BookedLeads;
        [BsonIgnoreIfDefault]
        public bool IsAuto;

        [DataMember(EmitDefaultValue = false)]
        public string BMSUserToken { get; set; }

    }

    public class CountryTimeZone
    {
        public short CountryId { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public short AreaCode { get; set; }
    }


    public class AgentActivityTrackingModel

    {

        public List<DailerData> lstDialerDispDetails { get; set; }
        public Dictionary<Int64, List<DailerData>> UserBookingTimes { get; set; }
        //  public UserLoginDetails oUserLoginDetails { get; set; }
    }

    //public class UserLoginDetails
    //{

    //}
    [DataContract]
    public class DailerData
    {

        [DataMember(EmitDefaultValue = false)]
        public long ClaimID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 Duration { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 talktime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime callDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AgentId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AgentName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime BookingDateTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime AgentLoginTime { get; set; }

    }

    [DataContract]
    [Serializable]
    public class ResetLPData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
    }

    [DataContract]
    public class TicketObj
    {
        [DataMember(EmitDefaultValue = false)]
        public String ReqBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
    }

    [DataContract]
    public class CustomerCallBack
    {
        [DataMember(EmitDefaultValue = false)]
        public String Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ProductID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmpID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string serverIP { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AgentPhone { get; set; }
    }

    [DataContract]
    public class UnAnsweredSummary
    {
        [DataMember(EmitDefaultValue = true)]
        public Int32 WeekAttempts { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CurrentWeek { get; set; }

        private DateTime _AttemptsTillDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AttemptsTillDate
        {
            get
            {
                if (_AttemptsTillDate == DateTime.MinValue)
                {
                    _AttemptsTillDate = DateTime.Now;
                }
                else if (_AttemptsTillDate != null && _AttemptsTillDate.Kind == DateTimeKind.Utc)
                {
                    _AttemptsTillDate = _AttemptsTillDate.ToLocalTime();
                }
                return _AttemptsTillDate;
            }
            set { _AttemptsTillDate = value; }
        }

        [DataMember(EmitDefaultValue = true)]
        public Int32 TodayNANC { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 MaxAttempts { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CustomerNANC { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CustomerTodayNANC { get; set; }
    }

    [DataContract]
    public class NotContactedLeads
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }
    }
    [DataContract]
    public class QuoteSharedTracking
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AgentId { get; set; }
    }
    [DataContract]
    public class OneLeadScoreMapping
    {
        public string Type { get; set; }
        public Int16 MinValue { get; set; }
        public Int16 MaxValue { get; set; }
        public Decimal Score { get; set; }
        public Int16 ProductID { get; set; }
    }

    public enum MobileStatusEnum
    {
        Default = 0,
        Valid = 1,
        Suspicious = 2,
        InValid = 3
    }
    [DataContract]
    public class CustomerProfileData
    {
        [DataMember(EmitDefaultValue = false)]
        public string Memberage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Gender { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? Birthday { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AnnualIncome { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? InvestmentTypeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 IsPED { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string PlanType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string utmSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 MemberCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string selectedInsurer { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string ApplicationNumber { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FV { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NML { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NML20x { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NFL { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string PlanName { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string TermStartDate { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string KYCRemark { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string LeadSocre { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FUPDate { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string TotalPremium { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string AnnualisedPremium { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FullTermPremium { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string BookingFrequency { get; set; }



        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string BajaAnnualIncome { get; set; }

    }

    [DataContract]
    public class claimcomments
    {
        [DataMember(EmitDefaultValue = false)]
        public List<commentslist> Data { get; set; }
    }

    [DataContract]
    public class commentslist
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ClaimId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<CommentsData> Comments { get; set; }

    }

    [DataContract]
    public class CommentsData
    {
        [DataMember(EmitDefaultValue = false)]

        public string Comments { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CommentDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

    }

    public class CustContactInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadId { get; set; }
        public Int32 ProductId { get; set; }

        public Int64 CustomerId { get; set; }



        public string Name { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CountryID { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int32 UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        public string Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsPrimary { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string value { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 CustMobId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string mobNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 customerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 countryId { get; set; }
    }
    public struct ClickToLeadResponse
    {
        public int EnquiryID { get; set; }
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public string RedirectionURL { get; set; }
    }
    public class SOSBookingModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long leadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptedLeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]

        public string BMSUserToken { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime Createdon { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string link { get; set; }

    }

    public class VCLogModel
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long leadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }

    }
    public class WhatsAPPModal
    {
        [DataMember(EmitDefaultValue = false)]
        public string isSuccess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string url { get; set; }



    }



    public class IsOnCall
    {
        public bool onCall { get; set; }
    }

    public class OnCallModal
    {
        public bool IsCustOnCall { get; set; }
        public string message { get; set; }
        public int status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public IsOnCall data { get; set; }
    }
    public class OnCallCustomerModal
    {
        public bool IsCustOnCall { get; set; }
        public string message { get; set; }
        public int status { get; set; }


    }

    public class CityMasterModal
    {
        public Int32 CityId { get; set; }
        public Int32 ZoneId { get; set; }
        public Int32 AppointmentTypeId { get; set; }
        public Int32 AssigntmentId { get; set; }
        public string City { get; set; }
        public string Zone { get; set; }


    }

    public class AppointmentTypeModal
    {
        public Int32 AppointmentId { get; set; }
        public Int32 ProcessId { get; set; }
        public string value { get; set; }


    }
    public class FOSAssignmentModal
    {
        public Int32 AssignmentId { get; set; }
        public Int32 AppointmentId { get; set; }
        public string AssignmentType { get; set; }

    }
    public class FOSModal
    {
        public List<AppointmentTypeModal> fosAppointmentList { get; set; }
        public List<FOSAssignmentModal> fosAssignmentList { get; set; }

    }
    public class InsurerQuotePrice
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 SupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Price { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int OTC { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UpdatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 FamilyId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string FilePath { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 QuoteNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long SMEQuoteId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ControlId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CreatedBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UploadedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime? UpdatedDate { get; set; }

    }

    public class CustomerBookingDocumentDetails
    {
        public string DocumentURL { get; set; }
        public string ShortDocUrl { get; set; }
        public string error { get; set; }
        public string LeadId { get; set; }

    }

    public class BmsLoginUrl
    {
        [DataMember(EmitDefaultValue = false)]
        public bool isSuccess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RedirectUrl { get; set; }

    }
    
    [DataContract]
    public class CityModal
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 CityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CityName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DisplayName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 StateId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string StateName { get; set; }
    }

    public class SubStatusModal
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SubStatusName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ShortSubStatus { get; set; }

    }


   

    [DataContract]
    public class SMEQuoteModal
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public Int16 ControlId { get; set; }

        [DataMember]
        public string Path { get; set; }

        [DataMember]
        public long UserId { get; set; }
        [DataMember]
        public Int16 typeid { get; set; }
        [DataMember]
        public Int16 QuotesStatusId { get; set; }

        [DataMember]
        public string SMEQuote { get; set; }

        [DataMember]
        public string fileName { get; set; }

        [DataMember]
        public int roleId { get; set; }
        [DataMember]
        public string comment { get; set; }
        [DataMember]
        public string ReasonId { get; set; }

        [DataMember]
        public int SubStatusId { get; set; }

        [DataMember]
        public int productId { get; set; }


    }

    [DataContract]
    public class SMELeadQuote
    {
        [DataMember(EmitDefaultValue = false)]
        public long RowId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ControlId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Path { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UploadedBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime UploadedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public QuotesStatusMaster QuotesStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsUploadedByAgent { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FileName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<InsurerQuotePrice> QuotePriceList { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ReasonName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ReasonID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long parentRowId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 QuoteType { get; set; }
    }
    [DataContract]
    public class QuotesStatusMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 QuotesStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusId { get; set; }
    }

    [DataContract]
    public class LeadStatusDetails
    {
        public string LeadStatus { get; set; }
        public int StatusId { get; set; }
        public int AssignToGroupId { get; set; }
        public string LeadId { get; set; }
        public string error { get; set; }
    }

    [DataContract]
    public class SMEQuoteDetailModel
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 SMEQuotesId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 roleId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ControlId { get; set; }
    }

    [DataContract]
    public class SMEAdditionalFileModal
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public Int16 ControlId { get; set; }

        [DataMember]
        public string filePath { get; set; }

        [DataMember]
        public long UserId { get; set; }


        [DataMember]
        public string FileName { get; set; }

        [DataMember]
        public int roleId { get; set; }
        [DataMember]
        public long parentRowId { get; set; }
        [DataMember]
        public bool IsDelete { get; set; }
        [DataMember]
        public int RowId { get; set; }



    }

    [DataContract]
    public class CustAddModal
    {

        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        [DataMember]
        public string AddressType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 AddressTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CityId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CustAddrId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string House { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsPrimary { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Landmark { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Locality { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 PinCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 StateId { get; set; }
        //[DataMember(EmitDefaultValue = false)]
        //public long UserId { get; set; }

    }

    [DataContract]
    public class SubProductListModal
    {
        [DataMember(EmitDefaultValue = false)]
        public long SubProductId { get; set; }
        [DataMember]
        public string SubProductName { get; set; }
        [DataMember]
        public string GroupName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string QueueName { get; set; }
    }
    [DataContract]
    public class AssignLeadModal
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember]
        public long AssignTo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long AssignBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 JobId { get; set; }
    }
    [DataContract]
    public class TTModal
    {

        [DataMember(EmitDefaultValue = false)]
        public string HistoryTalktime;
        [DataMember(EmitDefaultValue = false)]
        public string TodayTalkTime;



    }

    public class CustomerSelection
    {
        [DataMember]
        public Int64 LeadID { get; set; }
        [DataMember]
        public DateTime QuoteCreatedOn { get; set; }
        public int PlanID { get; set; }
        public string PlanName { get; set; }
    }



    [DataContract]
    public class bookingModel
    {
        [DataMember]
        public Int16 statusCode { get; set; }


        [DataMember]
        public Int32 AssigmentId { get; set; }
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string BookingType { get; set; }

    }

    [DataContract]
    public class LeadFlags
    {
        [DataMember]
        public Int16 AttributeID { get; set; }

        [DataMember]
        public Int16 Value { get; set; }

        [DataMember]
        public Int64 LeadId { get; set; }
        [DataMember]
        public Int64 UserId { get; set; }


    }
    [DataContract]
    public class NewSVURLModel
    {
        [DataMember]
        public Int16 StatusCode { get; set; }
        [DataMember]
        public string StatusMessage { get; set; }
        [DataMember]
        public string URL { get; set; }

    }

    [DataContract]
    public class UserInfoModel
    {
        [DataMember]
        public Int16 StatusCode{ get; set; }
        [DataMember]
        public string Status { get; set; }
        [DataMember]
        public int Ok { get; set; }
        [DataMember]
        public List<long> Leads { get; set; }
    }

    [DataContract]
    public class UserInfo
    {
        [DataMember]
        public long Mobile { get; set; }
        [DataMember]
        public long CustId { get; set; }
        [DataMember]
        public int NoOfProfile { get; set; }
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public string Source { get; set; }
    }

    [DataContract]
    public class SelectedQuote
    {
        [DataMember]
        public long ID { get; set; }
        [DataMember]
        public int PlanID { get; set; }
        [DataMember]
        public short ProductID { get; set; }
        [DataMember]
        public decimal Premium { get; set; }
        [DataMember]
        public long LeadID { get; set; }
        [DataMember]
        public int SelectedBy { get; set; }
        [DataMember]
        public string IPAddress { get; set; }
        [DataMember]
        public int NeedId { get; set; }
        [DataMember]
        public int EnquiryId { get; set; }
        [DataMember]
        public int SupplierId { get; set; }
        [DataMember]
        public string SourcePage { get; set; }
    }

    [DataContract]
    public class LeadDeatils
    {
        [DataMember]
        public string UTMSource { get; set; }
        [DataMember]
        public string UTMTerm { get; set; }
        [DataMember]
        public string UTMMedium { get; set; }
        [DataMember]
        public string UTMCampaign { get; set; }
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public string DOB { get; set; }
        [DataMember]
        public string MobileNo { get; set; }
        [DataMember]
        public string EmailId { get; set; }
        [DataMember]
        public int CityID { get; set; }
        [DataMember]
        public int StateID { get; set; }
        [DataMember]
        public int ProductID { get; set; }
        [DataMember]
        public int SupplierId { get; set; }
        [DataMember]
        public int PlanId { get; set; }
        [DataMember]
        public string PlanName { get; set; }
        [DataMember]
        public string SupplierName { get; set; }
        [DataMember]
        public int CustomerID { get; set; }
        [DataMember]
        public string PolicyNo { get; set; }
    }

    [DataContract]
    public class LeadRequest
    {
        [DataMember]
        public string UTMSource { get; set; }
        [DataMember]
        public string UTMTerm { get; set; }
        [DataMember]
        public string UTMMedium { get; set; }
        [DataMember]
        public string UTMCampaign { get; set; }
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public string DOB { get; set; }
        [DataMember]
        public string MobileNo { get; set; }
        [DataMember]
        public string EmailId { get; set; }
        [DataMember]
        public int CityID { get; set; }
        [DataMember]
        public int StateID { get; set; }
        [DataMember]
        public string LeadSource { get; set; }
        [DataMember]
        public int ProductID { get; set; }
        [DataMember]
        public int SupplierId { get; set; }
        [DataMember]
        public int PlanId { get; set; }
        [DataMember]
        public string Source { get; set; }
        [DataMember]
        public int ReferralID { get; set; }
        [DataMember]
        public int leadStatus { get; set; }
        [DataMember]
        public string PlanName { get; set; }
        [DataMember]
        public string SupplierName { get; set; }
        [DataMember]
        public int CustomerID { get; set; }
        [DataMember]
        public string LastYearPolicyNo { get; set; }
        [DataMember]
        public string CustPolicyID { get; set; }
        [DataMember]
        public string PolicyExpiryDate { get; set; }
    }

    [DataContract]
    public class LeadResponse
    {
        [DataMember]
        public Int64 LeadID { get; set; }
        [DataMember]
        public string Error { get; set; }
    }

    [DataContract]
    public class Remarks
    {
        [DataMember]
        public List<string> RemarksList { get; set; }
    }

    [DataContract]
    public class GetLeadDetails
    {
        [DataMember]
        public string LeadId { get; set; }

        [DataMember]
        public bool IsBooking { get; set; }
    }

    [DataContract]
    public class HealthRenewalRemarks
    {
        [DataMember]
        public string Remarks { get; set; }
    }
}