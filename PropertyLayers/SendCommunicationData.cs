﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{
    public class sendcommunicationResponse
    {
        public string TriggerName { get; set; }
        public long LeadId { get; set; }
        public int CommunicationType { get; set; }
        public long MobileNo { get; set; }
        public int CountryCode { get; set; }
        public int ProductId { get; set; }
        public Inputdata InputData { get; set; }
        public string[] To { get; set; }
        public string[] CC { get; set; }
        public string[] BCC { get; set; }
        public bool IsSalesWA { get; set; }
        public string ProductDisplayName { get; set; }
        public string AgentEcode { get; set; }
        public string AgentName { get; set; }
        public string SMSURL { get; set; }
        public string WhatsappURL { get; set; }
        public string DummyRMName { get; set; }
        public string DummyRMEmail { get; set; }
        public string CustomerName { get; set; }
        public Int16 GroupId { get; set; }
    }

    public class Inputdata
    {
        public string CustomerName { get; set; }
        public string InsurerName { get; set; }
        public string TollFreeNo { get; set; }
        public string ProductDisplayName { get; set; }
        public string ContinueLink { get; set; }
        public List<Bookingdetail> BookingDetails { get; set; }
        public string OTP { get; set; }
        public string LeadID { get; set; }
        public string Source { get; set; }
        public string ToMobileNo { get; set; }
        public string ProductName { get; set; }
        public string AgentName { get; set; }
        public string AgentEcode { get; set; }
        public string CallTypeId { get; set; }
        public string CallUId { get; set; }
        public string ScheduleDate { get; set; }
        public string ScheduleDay { get; set; }
        public string ScheduleTime { get; set; }
        public string CustomerAddress { get; set; }
        public string AppointmentDateTime { get; set; }
        public string FOSLink { get; set; }
        public string AppointmentId { get; set; }
        public string AppointmentUID { get; set; }
        public string AppointmentSubStatusID { get; set; }
        public string DynamicURL { get; set; }
        public string DummyRMName { get; set; }
        public string DummyRMEmail { get; set; }
        public string Subject { get; set; }
        public string Content { get; set; }
        public string RenewalLink { get; set; }
        public decimal TotalAmount { get; set; }
        public string MobileNo { get; set; }

        public string SecondaryNumber { get; set; }
        public string VerificationLink { get; set; }
        public Int32 SecondaryCountryCode { get; set; }

        public string SecondaryEmailID { get; set; }
        public Int32 CountryCode { get; set; }

        public string Link { get; set; }
        public string PolicyNo { get; set; }
        public string DueDate { get; set; }
        public decimal Amount { get; set; }
        public decimal SumInsured { get; set; }
        public string PaymentLink { get; set; }
        public string DynamicUrlData { get; set; }
        public string EmployeeName { get; set; }
        public string ECode { get; set; }
        public string AppLink { get; set; }
    }

    public class Bookingdetail
    {
        public string InsurerName { get; set; }
        public DateTime PolicyEndDate { get; set; }

        public string PolicyNo { get; set; }
        public string DueDate { get; set; }
        public string PGPaymentLink { get; set; }
        public string PaymentLink { get; set; }
        public string GraceDate { get; set; }
        public string PlanName { get; set; }
        public long FinalPremium { get; set; }
        public string PaymentPeriodicity { get; set; }
        public string SupplierLogo { get; set; }
        public string ProductDisplayName { get; set; }
        public string PgRegistrationLink { get; set; }

    }

    public class ComAPIV2Response
    {
        [DataMember]
        public string UUID { get; set; }
        [DataMember]
        public string Message { get; set; }
        [DataMember]
        public bool IsSuccess { get; set; }
    }
}
