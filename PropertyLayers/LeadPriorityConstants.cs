﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PropertyLayers
{
    public class LeadPriorityConstants
    {
        public List<LeadPointDetails> lstLeadPointDetails { get; set; }
        public int pullNewAssignmentInterval { get; set; }
        public int DeductNAPointsInterval { get; set; }
        public List<Int16> PriorityGroups { get; set; }
        public byte MorningShift { get; set; }
        public byte AfterNoonShift { get; set; }
        public byte EveningShift { get; set; }
        public List<Int16> SalesRejStatus { get; set; }
        public List<Int16> BookedStatus { get; set; }
        public string pooleventqueuename { get; set; }
        public List<byte> MatrixStatus { get; set; }
        public string priorityEventqueue { get; set; }
        public Int16 ReadPriorityEventInterval { get; set; }
        public bool prioritylogicon { get; set; }
        public DispositionQueue DispositionQueueConstant { get; set; }
        public DispositionQueue DispositionUpdateQueueConstant { get; set; }
        public bool DeductNANCLog { get; set; }
        public bool ReadPullEventLog { get; set; }
        public bool ProcessEventLog { get; set; }
        public bool PriorityEventQueueEntryOn { get; set; }
        public Int16 TimeDifferenceDailerNTata { get; set; }// Dailer is ahead 29 server by 150 sec       
        public List<string> NriIdentification { get; set; }
        public byte ReOpenPoint { get; set; }
        public string CustomerOnCallDailerApi { get; set; }
        public DailerDisposition Disposition { get; set; }

        public Int32 PooleventQExpMinutes { get; set; }
        public Int32 PriorityeventQExpMinutes { get; set; }
        public PriorityLog PriorityLogConstant { get; set; }
        public messages msg { get; set; }
        public byte CallReleaseCount { get; set; }
        public Int16 Releaseleadsshowtime { get; set; }
        public byte RevisitThreadWait { get; set; }
        public Int16 RevisitOrPaymentTimeMorning { get; set; }
        public Int16 RevisitOrPaymentTimeAfterMorning { get; set; }
        public bool SkipChildLead { get; set; }
        public byte StarLeadsLimit { get; set; }
        public byte SkipBucketGapMints { get; set; }
        public byte SkipBucketCallGapMints { get; set; }
        public int RemoveChildLdInterval { get; set; }
        public int STULeadCreationInterval { get; set; }

        public byte LastDaysCount { get; set; }
        public byte AvgUserCallPerDay { get; set; }
        public Int16 RevisitMintsGap { get; set; }
        public bool IsCBRestrictionON { get; set; }
        public byte RecentExpiryGap { get; set; }
        public List<productPriorityconstant> PriorityConstant { get; set; }

        public int BookedInterval { get; set; }
        public List<byte> MatrixBookedStatus { get; set; }
        public List<byte> PolicyIssuedStatus { get; set; }
        public int CommonPoolInterval { get; set; }
        public int LogoutInterval { get; set; }
        public int UpdateChatAssignInterval { get; set; }
        public string MrngstartTime { get; set; }
        public string MrngEndTime { get; set; }
        public string NgtStartTime { get; set; }

        public string NgtEndTime { get; set; }
        public Int16 MrngChatLimit { get; set; }
        public Int16 NgtChatLimit { get; set; }
        public int ChatRoasterAllocationInterval { get; set; }

        public int SyncChatProcessDataInterval { get; set; }

        //public AgentScoringConstants AgentRatingconstant { get; set; }
        public List<AgentRatingConstant> AgentScoreConstants { get; set; }
    }

    public class DispositionQueue
    {
        public int readDispositionqueueInterval { get; set; }
        public string dispositionQueueName { get; set; }
        public int dispositionQueueAlertMSMQCount { get; set; }
        public bool dispositionQueueAlertMail { get; set; }
        public bool DispositionQueueReadLog { get; set; }
    }

    public class LeadPointDetails
    {
        public string LeadPointType { get; set; }
        public Int16 Points { get; set; }
        public byte Count { get; set; }
        public byte Talktime { get; set; }
    }

    public class messages
    {
        public string PaymentCB { get; set; }
        public string ActiveRevisit { get; set; }
        public string ActiveNew { get; set; }
        public string ActiveCB { get; set; }
        public string PassiveCB { get; set; }
        public string PassiveRevisit { get; set; }
        public string PassiveNew { get; set; }
        public string SecondAttemptPCB { get; set; }
        public string SecondAttemptActvCB { get; set; }
        public string SecondAttemptActvNew { get; set; }
        public string SecondAttemptActvRevisit { get; set; }
        public string SecondAttemptPasvCB { get; set; }
        public string SecondAttemptPasvNew { get; set; }
        public string SecondAttemptPasvRevisit { get; set; }
        public string UnansweredLeads { get; set; }
        public string RestLeads_1 { get; set; }
        public string RestLeads_2 { get; set; }
        public string SecondAttemptRestLeads { get; set; }
        public string CallAllowedAfterFewMinutes { get; set; }
        public string CallReleasedLeads { get; set; }
        public string RecentExpriyLeads { get; set; }
        public RevisitTypeMsg RevisitMsg { get; set; }
        public string SkippedLeads { get; set; }
        public string FutureCallBackLeads { get; set; }
        public string ImportantLeadLimitMsg { get; set; }
        public string BdayLeads { get; set; }
        public string PaymentFailure { get; set; }
        public string EmailRevisit { get; set; }
        public string SecondAttemptEmailRevisit { get; set; }
        public string CTCRevisit { get; set; }
        public string SecondAttemptRevisitCTC { get; set; }
        public string BajajCustomerRevisit { get; set; }
        public string MissedCB { get; set; }
        public string BookedCB { get; set; }
        public string TicketUpdate { get; set; }
        public string StatusChange { get; set; }
        public string NoLeadPopup { get; set; }
        public string ProposalError { get; set; }
        public string QuoteShared { get; set; }
        public string RestPriorityLeads { get; set; }
        public string RevisionShared { get; set; }
        public string TodayExpiry { get; set; }
        public string SecondAttemptTodayExpiry { get; set; }
        public string PFFilled { get; set; }
        public string SecondAttemptPFFilled { get; set; }

        public string RMLeads { get; set; }
        public string CancelBookedLead { get; set; }
    }

    public class DailerDisposition
    {
        public string Answered { get; set; }
        public string NoAnswer { get; set; }
        public string Busy { get; set; }
        public string Failed { get; set; }
        public string Congestion { get; set; }

    }

    public class PriorityLog
    {
        public int ReadPriorityLogInterval { get; set; }
        public bool ReadPriorityLogLog { get; set; }
        public string PriorityLogQueueName { get; set; }
        public bool PriorityLogQueueEntryOn { get; set; }
        public bool IsLogSqlDumpOn { get; set; }
        public bool IsPositionLogSqlDumpOn { get; set; }
        public bool IsPositionLogMongoDumpOn { get; set; }

    }

    public class RevisitTypeMsg
    {
        public string Website { get; set; }
        public string Inbound { get; set; }
        public string EmailReply { get; set; }
        public string QuoteShared { get; set; }
        public string RevisionShared { get; set; }
    }

    public class productPriorityconstant
    {
        public Int16 productID { get; set; }
        public string subProduct { get; set; }
        public List<string> prioritySequence { get; set; }
        public Int16 PriorityQueueSize { get; set; }
        public Int16 LastNANCMintsGap { get; set; }
        public Int16 TalkTimeMin { get; set; }
        public Int16 TalkTimeMax { get; set; }
        public Int16 PntStndrdVal { get; set; }
        public Int16 MinDeductPnt { get; set; }
        public Int16 PaymentCBShowTime { get; set; }
        public Int16 ActiveNewLeadShowTime { get; set; }
        public Int16 ActiveCBShowTime { get; set; }
        public Int16 ActiveRevisitShowTime { get; set; }
        public Int16 InvisibleTimeNANC { get; set; }
        public byte NANCMaxAttemInDiffShifts { get; set; }
        public byte NANCLastCallDayGap { get; set; }
        public byte NANCMax1DayAttempt { get; set; }
        public Int16 callnotAllowedInterval { get; set; }
        public byte ReleasePoint { get; set; }
        public Int16 SKipLeadTimeGap { get; set; }
        public Int16 RestLeadCallMintsGap { get; set; }
        public byte RestPriorityCallHrGap { get; set; }
        public Int16 InterestedNRestPntDedMintGap { get; set; }
        public byte CarExpiryDayGap { get; set; }
        public Int16 InvisibleTimeNANC_New { get; set; }
        public Int16 InvisibleTimeNANC_Revisit { get; set; }
        public Int16 TodayCreatedExprTimeGap { get; set; }
        public Int16 BeforeTodayCreatedExprTimeGap { get; set; }
        public byte ExpiryLeadsShowCount { get; set; }
        public Int16 PassiveRevisitEndMints { get; set; }
        public Int16 PassiveCBEndMints { get; set; }
        public Int16 ActiveCBEndTime { get; set; }
        public byte LastCallPCBDoneMintsGap { get; set; }
        public Int16 PCBLastNANCMintsGap { get; set; }
        public Int16 TimeDifferenceDailerNTata { get; set; }// Dailer is ahead 29 server by 150 sec 
        public byte RestLeadLastCall { get; set; }
        public byte RecentExpiryGap { get; set; }
        public byte BdayRange { get; set; }
        public bool IsCBRestrictionON { get; set; }
        public byte UnAnsweredDaysGap { get; set; }
        public Int16 InvisibleTimeNANC_PaymentFailure { get; set; }
        public List<string> bookedPrioritySequence { get; set; }

        public Int16 Week1MaxAttempts { get; set; }
        public Int16 WeekMaxAttempts { get; set; }
        public Int16 MaxAttempts { get; set; }
        public Int16 AnsWeekAttempts { get; set; }
        public Int16 InvisibleTimeNANC_Rest { get; set; }
        public List<Int16> restprioritylogicGroups { get; set; }
        public List<DateTime> TodayExpCall { get; set; }
        public List<Int16> PortGroups { get; set; }
        public List<Int64> RevistRestrictionUsers { get; set; }
        public List<Int64> AIRestUsers { get; set; }

        public List<Int16> SOSGroups { get; set; }
    }

    public class AgentRatingConstant
    {
      
        public Int16 productID { get; set; }
        public double TT_Percentage { get; set; }
        public double IssuedAPE_Percentage { get; set; }
        public double Quiz_Percentage { get; set; }
        public double Quailty_Percentage { get; set; }

        public Int16 IssuedAPEWeigthage { get; set; }
        public Int16 Quality_Score { get; set; }
        public Int16 Quailty_Weigthage { get; set; }
        public Int16 Quiz_Weigthage { get; set; }
        public Int16 TT_Weigthage { get; set; }
        public double PercentileVal { get; set; }

    }
    public class DialerDataConstant
    {
        public string Group { get; set; }
        public Int16 ProductID { get; set; }
        public string ProductName { get; set; }
    }
}
