﻿using Helper;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Runtime.Caching;
using System.Xml;

namespace ReadXmlProject
{
    public static class ReadXml
    {
        //public ReadXml() { }
        public static string AppSettings(this string AppKey)
        {
            string KeyName = string.Empty, keyValue = string.Empty, Enviornment=string.Empty;
            try
            {
                //string xmlPath = Environment.CurrentDirectory + "/ConfigValues.xml";
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                
                Enviornment = CoreCommonMethods.GetEnvironmentVar();
                string xmlPath = string.Empty;
                if (!string.IsNullOrEmpty(Enviornment) && Enviornment.ToLower().Trim() == "live")
                {
                    xmlPath = Environment.CurrentDirectory + "/ConfigValues.xml";
                }
                else
                {
                    xmlPath = Environment.CurrentDirectory + "/QA_ConfigValues.xml";
                }
                Dictionary<string, string> dictionary;
                ObjectCache storeDic = MemoryCache.Default;
                dictionary = (Dictionary<string, string>)storeDic.Get("storeDic");
                if (dictionary == null)
                {
                    //Console.WriteLine("Enter in dictionary");
                    dictionary = new Dictionary<string, string>();
                    CacheItemPolicy objCachePolicies = new CacheItemPolicy();
                    objCachePolicies.ChangeMonitors.Add(new HostFileChangeMonitor(new List<string> { xmlPath }));


                    XmlDocument xDoc = new XmlDocument();
                    xDoc.Load(xmlPath);
                    XmlNodeList oXmlNodeList = xDoc.SelectNodes("ConfigSettings/add");
                    foreach (XmlNode x in oXmlNodeList)
                    {
                       // Console.WriteLine("readxml file");
                        KeyName = x.Attributes["key"].Value;
                        keyValue = x.Attributes["value"].Value;
                        if (!dictionary.ContainsKey(KeyName))
                            dictionary.Add(KeyName, keyValue);
                    }
                    storeDic.Add("storeDic", dictionary, objCachePolicies);
                }
                if (dictionary.ContainsKey(AppKey))
                {
                    return dictionary[AppKey].ToString();
                }
                else
                {
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                string s = KeyName;
                return string.Empty;
            }
        }
        public static string fnGetKeyValue(string KeyName)
        {
            try
            {
                string xmlPath = AppDomain.CurrentDomain.BaseDirectory + "\\ConfigValues.xml";
                //string xmlPath =Request.PhysicalApplicationPath + "/ProductStaticValue.xml";
                Dictionary<string, string> dictionary;
                ObjectCache storeDic = MemoryCache.Default;
                dictionary = (Dictionary<string, string>)storeDic.Get("storeDic");
                if (dictionary == null)
                {
                    dictionary = new Dictionary<string, string>();
                    string keyname = string.Empty, keyValue = string.Empty;
                    CacheItemPolicy objCachePolicies = new CacheItemPolicy();
                    objCachePolicies.ChangeMonitors.Add(new HostFileChangeMonitor(new List<string> { xmlPath }));


                    XmlDocument xDoc = new XmlDocument();
                    xDoc.Load(xmlPath);
                    XmlNodeList oXmlNodeList = xDoc.SelectNodes("ConfigSettings/add");
                    foreach (XmlNode x in oXmlNodeList)
                    {
                        keyname = x.Attributes["key"].Value;
                        keyValue = x.Attributes["value"].Value;
                        dictionary.Add(keyname, keyValue);
                    }
                    storeDic.Add("storeDic", dictionary, objCachePolicies);
                }
                if (dictionary.ContainsKey(KeyName))
                {
                    return dictionary[KeyName].ToString();
                }
                else
                {
                    return string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
