﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
//using ReadXmlProject;
using Helper;
using Microsoft.Extensions.Configuration;

namespace DataAccessLibrary
{
    public class SingletonClass
    {
        static MongoDatabase _CommunicationDB;
        static MongoDatabase _FileDB;
        static MongoDatabase _CommonDB;
        static MongoDatabase _ChatDB;
        static MongoDatabase _CarChatDB;
        static MongoDatabase _HealthChatDB;
        static MongoDatabase _OneLeadDB;
        static IMongoDatabase _OneLeadDBv2;
        private SingletonClass()
        {

        }
        public static MongoDatabase CommunicationDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_CommunicationDB != null)
                {
                    return _CommunicationDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;

                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string MongoDBCommunicationDB = con.GetSection("Communication").GetSection("MongoDBCommunicationDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("MongoDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(MongoDBCommunicationDB);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public static MongoDatabase CommFileDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_FileDB != null)
                {
                    return _FileDB;
                }
                else
                {
                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string MongoGridFSDB = con.GetSection("Communication").GetSection("MongoGridFSDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("MongoGridFSConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(MongoGridFSDB);


                    //var connectionString = ConfigurationManager.AppSettings["MongoGridFSConnection"];
                    //var client = new MongoClient(connectionString);
                    //var server = client.GetServer();
                    //_FileDB = server.GetDatabase(ConfigurationManager.AppSettings["MongoGridFSDB"]);
                    //return _FileDB;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static MongoDatabase ChatDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_ChatDB != null)
                {
                    return _ChatDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string ChatDBcommon = con.GetSection("Communication").GetSection("ChatDBcommon").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("ChatDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(ChatDBcommon);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }



        public static MongoDatabase OneLeadDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(OneLeadDB);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static IMongoDatabase OneLeadDBV2()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDBv2 != null)
                {
                    return _OneLeadDBv2;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    _OneLeadDBv2 = client.GetDatabase(OneLeadDB);
                    return _OneLeadDBv2;

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static MongoDatabase RealTimeDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string RealTimeDB = con.GetSection("Communication").GetSection("RealTimeDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("RealTimeDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(RealTimeDB);


                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static MongoDatabase ResetOneLeadDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("ResetDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(OneLeadDB);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}