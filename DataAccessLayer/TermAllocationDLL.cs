﻿using DataAccessLibrary;
using DataHelper;
using Helper;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.Caching;
using MongoCollection = DataAccessLibrary.MongoCollection;

namespace DataAccessLayer
{
    public class TermAllocationDLL
    {
        #region Common Methods
        private static string SqlParameterNamesString(SqlParameter[] SqlParam)
        {
            string nameString = string.Empty;

            SqlParam.ToList().ForEach(param =>
            {
                nameString += $"{param.ParameterName}, ";
            });
            if (nameString.Length > 2)
            {
                nameString = nameString.Remove(nameString.Length - 2, 2);
            }
            return nameString;

        }
        private static object ExecuteSqlUserDefinedFunction(string Connection, string FunctionName, SqlParameter[] sqlParam)
        {
            string paramsString = SqlParameterNamesString(sqlParam);

            string FunctionCallQuery = "SELECT " + FunctionName + "(" + paramsString + ")";

            return SqlHelper.ExecuteScalar(Connection, CommandType.Text, FunctionCallQuery, sqlParam);
        }

        #endregion

        #region Lead Data 
        public static DataSet GetTermAllocationLeads(long LeadId = 0)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", LeadId);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_GetLeads]", SqlParam);

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static DataSet PopulateLeadTermDetails(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_TermDetails]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static DataSet PopulateLeadAllocationKeyFactors(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_KeyFactors]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static short PopulateLeadRank(TermLeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[30];
            SqlParam[0] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[1] = new SqlParameter("@InsurerId", lead.InsurerID);
            SqlParam[2] = new SqlParameter("@Prebooking", lead.PreviousBooking);
            SqlParam[3] = new SqlParameter("@TotalPayout", lead.TotalPayout);
            SqlParam[4] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[5] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[6] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[7] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[8] = new SqlParameter("@Age", lead.Age);
            SqlParam[9] = new SqlParameter("@RepeatCustomer", lead.RepeatCustomer);
            SqlParam[10] = new SqlParameter("@Country", lead.Country);
            SqlParam[11] = new SqlParameter("@StateID", lead.StateID);
            SqlParam[12] = new SqlParameter("@DOB", lead.DOB);
            SqlParam[13] = new SqlParameter("@Utm_Term", lead.Utm_term);
            SqlParam[14] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[15] = new SqlParameter("@EducationID", lead.educationQualificationId);
            SqlParam[16] = new SqlParameter("@Profession", lead.ProfessionType);
            SqlParam[17] = new SqlParameter("@Source", lead.source);
            SqlParam[18] = new SqlParameter("@IsSmoker", lead.IsTobaccoUser);
            SqlParam[19] = new SqlParameter("@INVTypeID", lead.InvestmentTypeID); 
            SqlParam[20] = new SqlParameter("@LeadCreatedOn", lead.CreatedOn);
            SqlParam[21] = new SqlParameter("@Utm_Content", lead.UtmContent);
            SqlParam[22] = new SqlParameter("@LanguageID", lead.LanguageRegionID);
            SqlParam[23] = new SqlParameter("@Name", lead.Name);
            SqlParam[24] = new SqlParameter("@ProductID", lead.ProductID);
            SqlParam[25] = new SqlParameter("@CityUpdatedBy", lead.CityUpdatedBy);
            SqlParam[26] = new SqlParameter("@IsDerivedIncome", lead.IsDerivedIncome);
            SqlParam[27] = new SqlParameter("@PostCode", lead.PostCode);
            SqlParam[28] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[29] = new SqlParameter("@VisitCount", lead.VisitCount);

            var LeadRank = ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[TermAllocation_GetLeadRank]", SqlParam);
            if (LeadRank == null)
            {
                return 0;
            }
            return (short)LeadRank;
        }

        public static short PopulateLeadRankV2(TermLeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[24];
            SqlParam[0] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[1] = new SqlParameter("@InsurerId", lead.InsurerID);
            SqlParam[2] = new SqlParameter("@Prebooking", lead.PreviousBooking);
            SqlParam[3] = new SqlParameter("@TotalPayout", lead.TotalPayout);
            SqlParam[4] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[5] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[6] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[7] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[8] = new SqlParameter("@Age", lead.Age);
            SqlParam[9] = new SqlParameter("@RepeatCustomer", lead.RepeatCustomer);
            SqlParam[10] = new SqlParameter("@Country", lead.Country);
            SqlParam[11] = new SqlParameter("@StateID", lead.StateID);
            SqlParam[12] = new SqlParameter("@DOB", lead.DOB);
            SqlParam[13] = new SqlParameter("@Utm_Term", lead.Utm_term);
            SqlParam[14] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[15] = new SqlParameter("@EducationID", lead.educationQualificationId);
            SqlParam[16] = new SqlParameter("@Profession", lead.ProfessionType);
            SqlParam[17] = new SqlParameter("@Source", lead.source);
            SqlParam[18] = new SqlParameter("@IsSmoker", lead.IsTobaccoUser);
            SqlParam[19] = new SqlParameter("@INVTypeID", lead.InvestmentTypeID); 
            SqlParam[20] = new SqlParameter("@LeadCreatedOn", lead.CreatedOn);
            SqlParam[21] = new SqlParameter("@Utm_Content", lead.UtmContent);
            SqlParam[22] = new SqlParameter("@Name", lead.Name);
            SqlParam[23] = new SqlParameter("@LanguageID", lead.LanguageRegionID);

            try{
                var SpecialLeadRank = ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[TermAllocation_GetLeadRankV2]", SqlParam);
                if (SpecialLeadRank == null)
                {
                    return 0;
                }
                return (short)SpecialLeadRank;
            } catch (Exception ex){
                Console.WriteLine(ex.ToString());
                return 0;
            }
            
        }

        public static decimal PopulateLeadScore(TermLeadDetails lead)
        { 
            SqlParameter[] SqlParam = new SqlParameter[25];
            SqlParam[0] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[1] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[2] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[3] = new SqlParameter("@IsSelfSelect", lead.InsurerID > 0 ? 1 : 0);
            SqlParam[4] = new SqlParameter("@DOB", lead.DOB);
            SqlParam[5] = new SqlParameter("@Profession", lead.ProfessionType);
            SqlParam[6] = new SqlParameter("@Age", lead.Age);
            SqlParam[7] = new SqlParameter("@ProductID", lead.ProductID);
            SqlParam[8] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[9] = new SqlParameter("@PrevBooking", lead.PreviousBooking);
            SqlParam[10] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[11] = new SqlParameter("@RepeatCustomer", lead.RepeatCustomer);
            SqlParam[12] = new SqlParameter("@UTM_Term", lead.Utm_term);
            SqlParam[13] = new SqlParameter("@DeviceName", lead.brandName);
            SqlParam[14] = new SqlParameter("@Source", lead.source);
            SqlParam[15] = new SqlParameter("@TwoWheelerBookingCount", lead.TwowheelerBookingCount);
            SqlParam[16] = new SqlParameter("@IsSalaryChanged", lead.IsSAChanged);
            SqlParam[17] = new SqlParameter("@IsComparePlan", lead.TermCompare);
            SqlParam[18] = new SqlParameter("@IsLimitedPay", lead.LimitedPay);
            SqlParam[19] = new SqlParameter("@IsTrop", lead.TropSelected);
            SqlParam[20] = new SqlParameter("@Country", lead.Country);
            SqlParam[21] = new SqlParameter("@IsTobaccoUser", lead.IsTobaccoUser);
            SqlParam[22] = new SqlParameter("@educationQualificationId", lead.educationQualificationId);
            SqlParam[23] = new SqlParameter("@CityUpdatedBy", lead.CityUpdatedBy);
            SqlParam[24] = new SqlParameter("@LeadId", lead.LeadId);

            var LeadScore = ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[TermAllocation_GetLeadScore]", SqlParam);
            if (LeadScore == null)
            {
                return 0;
            }
            return Convert.ToDecimal(LeadScore);
        }

        public static short GetLeadRankByLeadScore(TermLeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@LeadScore", lead.LeadScore);
            SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
          
            var LeadRank = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_GetRankByLeadScore]", SqlParam);
            if(LeadRank != null || LeadRank != DBNull.Value){
                return Convert.ToInt16(LeadRank);
            }
            return Convert.ToInt16(0);
        }

        public static DataSet PopulateAdditionalDataUpdate(TermLeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[14];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[2] = new SqlParameter("@GroupCode", lead.GroupCode);
            SqlParam[3] = new SqlParameter("@CustomerID", lead.CustomerId);
            SqlParam[4] = new SqlParameter("@DOB", lead.DOB);
            SqlParam[5] = new SqlParameter("@UtmContent", lead.UtmContent);
            SqlParam[6] = new SqlParameter("@UtmSource", lead.Utm_source);
            SqlParam[7] = new SqlParameter("@UtmMedium", lead.UTM_Medium);
            SqlParam[8] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[9] = new SqlParameter("@AddOnParentID", lead.AddOnParentID);
            SqlParam[10] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[11] = new SqlParameter("@ProfessionType", lead.ProfessionType);
            SqlParam[12] = new SqlParameter("@EducationTypeID", lead.educationQualificationId);
            SqlParam[13] = new SqlParameter("@CreatedOn", lead.CreatedOn);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_AdditionalDataUpdates]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static void GetAgentID_TermAllocation(TermLeadDetails lead, Assign assignment)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[20];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[2] = new SqlParameter("@InsurerID", lead.InsurerID);
            SqlParam[3] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[4] = new SqlParameter("@UserID", lead.UserId);
            SqlParam[5] = new SqlParameter("@CreatedOn", lead.CreatedOn);
            SqlParam[6] = new SqlParameter("@Groupcode", lead.GroupCode);
            SqlParam[7] = new SqlParameter("@ExistingGroupID", lead.GroupID);
            SqlParam[8] = new SqlParameter("@sourcePage", lead.SourcePage);
            SqlParam[9] = new SqlParameter("@IsCTC", lead.IsCTC);
            SqlParam[10] = new SqlParameter("@NewAgentId", DbType.Int64) {   
                Value = 0,
                Direction = ParameterDirection.Output 
            };
            SqlParam[11] = new SqlParameter("@AssignedToGroup", DbType.Byte) {
                Value = 0,
                Direction = ParameterDirection.Output };
            SqlParam[12] = new SqlParameter("@JobId", DbType.Int16) {
                Value = 0,
                Direction = ParameterDirection.Output };
            SqlParam[13] = new SqlParameter("@SameCustFlag", DbType.Int16) {
                Value = 0,
                Direction = ParameterDirection.Output };
            SqlParam[14] = new SqlParameter("@ReassignCount", assignment.reassignCount) {
                Direction = ParameterDirection.Output };
            SqlParam[15] = new SqlParameter("@NoAgent", assignment.NoAgent) {
                Direction = ParameterDirection.Output };
            SqlParam[16] = new SqlParameter("@CrmLeads", assignment.CrmLeads) {
                Direction = ParameterDirection.Output };
            SqlParam[17] = new SqlParameter("@GroupID", DbType.Int16) {
                Value = 0,
                Direction = ParameterDirection.Output };
            SqlParam[18] = new SqlParameter("@AssignmentProcess", lead.AssignmentProcess);
            SqlParam[19] = new SqlParameter("@LeadSource", lead.LeadSource);


            try
            {
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[TermAllocation_GetAssignToAgent]", SqlParam);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

            lead.AssigntoUserID = Convert.ToInt64((SqlParam[10].Value == DBNull.Value || SqlParam[10].Value == null) ? 0 : SqlParam[10].Value);
            lead.AssignedToGroup = Convert.ToByte(SqlParam[11].Value == DBNull.Value || SqlParam[11].Value == null ? 0 : SqlParam[11].Value);
            lead.JobID = Convert.ToInt16(SqlParam[12].Value == DBNull.Value || SqlParam[12].Value == null ? 0 : SqlParam[12].Value);
            lead.SameCustFlag = Convert.ToInt16(SqlParam[13].Value == DBNull.Value || SqlParam[13].Value == null ? 0 : SqlParam[13].Value);
            assignment.reassignCount = Convert.ToInt32(SqlParam[14].Value == DBNull.Value || SqlParam[14].Value == null ? 0 : SqlParam[14].Value);
            assignment.NoAgent = Convert.ToByte(SqlParam[15].Value == DBNull.Value || SqlParam[15].Value == null ? 0 : SqlParam[15].Value);
            assignment.CrmLeads = Convert.ToByte(SqlParam[16].Value == DBNull.Value || SqlParam[16].Value == null ? 0 : SqlParam[16].Value);
            lead.NewGroupID = Convert.ToInt16(SqlParam[17].Value == DBNull.Value || SqlParam[17].Value == null ? 0 : SqlParam[17].Value);
        }

        public static int ReAssignLead(TermLeadDetails lead)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[10];
            SqlParam[0] = new SqlParameter("@AssignedTo_AgentId", lead.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@AssignedBy_AgentId", 1);
            SqlParam[2] = new SqlParameter("@ProductId", lead.ProductID);
            SqlParam[3] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[4] = new SqlParameter("@GroupId", lead.NewGroupID);
            SqlParam[5] = new SqlParameter("@Flag", 1);
            SqlParam[6] = new SqlParameter("@JobId", lead.JobID);
            SqlParam[7] = new SqlParameter("@FirstSelectedPlanId", lead.InsurerID);
            SqlParam[8] = new SqlParameter("@SelectionCount", lead.SelectionCount);
            SqlParam[9] = new SqlParameter("@LeadRank", lead.LeadRank);
            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CRM].[Insert_AssignedToAgent]", SqlParam);
        }

        public static void AllocateLead(TermLeadDetails lead, Assign assignment)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[20];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[2] = new SqlParameter("@InsurerID", lead.InsurerID);
            SqlParam[3] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[4] = new SqlParameter("@Groupcode", lead.GroupCode);
            SqlParam[5] = new SqlParameter("@ProductID", lead.ProductID);
            SqlParam[6] = new SqlParameter("@SelectionCount", lead.SelectionCount);
            SqlParam[7] = new SqlParameter("@LeadScore", lead.ExistingLeadScore);
            SqlParam[8] = new SqlParameter("@LeadStatusId", lead.StatusId);
            SqlParam[9] = new SqlParameter("@LeadSubStatusId", lead.SubStatusId);
            SqlParam[10] = new SqlParameter("@LeadTypeId", 0);
            SqlParam[11] = new SqlParameter("@CustID", lead.CustomerId);
            SqlParam[12] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[13] = new SqlParameter("@IsCTC", lead.IsCTC);
            SqlParam[14] = new SqlParameter("@NewAgentId", lead.AssigntoUserID);
            SqlParam[15] = new SqlParameter("@AssignedToGroup", lead.AssignedToGroup);
            SqlParam[16] = new SqlParameter("@JobId", lead.JobID);
            SqlParam[17] = new SqlParameter("@SameCustFlag", lead.SameCustFlag);
            SqlParam[18] = new SqlParameter("@CrmLeads", assignment.CrmLeads) {
                Direction = ParameterDirection.Output
            };
            SqlParam[19] = new SqlParameter("@GroupId", lead.NewGroupID);

            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[TermAllocation_LeadAssigment]", SqlParam);

            assignment.CrmLeads = Convert.ToByte(SqlParam[18].Value == DBNull.Value || SqlParam[18].Value == null ? 0 : SqlParam[18].Value);
        }

        public static DataSet GetCTCndChurnLeads()
        {
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_CTCndChurnLeads]");

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static void DumpTermLeadDetails(TermLeadDetails lead)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[10];
            SqlParam[0] = new SqlParameter("@AssignedToAgentId", lead.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[2] = new SqlParameter("@JobId", lead.JobID);
            SqlParam[3] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[4] = new SqlParameter("@SpecialLeadRank",lead.IsAllocable ? 1 : 0);
            SqlParam[5] = new SqlParameter("@GroupCode", lead.GroupCode);
            SqlParam[6] = new SqlParameter("@LeadScore", lead.LeadScore);
            // SqlParam[7] = new SqlParameter("@AgentGrade", lead.TempLeadRank);
            SqlParam[7] = new SqlParameter("@AssignmentProcess", lead.AssignmentProcess);
            SqlParam[8] = new SqlParameter("@CreatedOn", DateTime.Now);
            SqlParam[9] = new SqlParameter("@ProductId", lead.ProductID);

           SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure,"[MTX].[AllocationLeadDump]", SqlParam);
        }

        public static DataSet GetTermLeadsPayUIncome(long CustomerID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("CustomerId", CustomerID);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_GetCustPayUIncome]", SqlParam);

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        #endregion

        #region Agent Data
        public static int DumpAgentsforTermAllocation(short ProductId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ProductID", ProductId);

            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[TermAllocation_DumpAgents]", SqlParam);
        }

        public static void UpDateProcessedLead(TermLeadDetails lead)
        {
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@LeadID", lead.LeadId);
                SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
                SqlParam[2] = new SqlParameter("@LeadEntryPPL", lead.LeadScore);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[TermAllocation_UpdateLeadDetails]", SqlParam);
            }
            catch (Exception){

            }
        }

        public static void USP_UpdateAgentGrade_Auto(short productId, short groupId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@ProductID", productId);
            SqlParam[1] = new SqlParameter("@GroupID", groupId);
            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[USP_UpdateAgentGrade_Auto]", SqlParam);
        }
        #endregion

        public static Dictionary<short, short> GetLeadRankMasterMinRankMap()
        {
            try
            {
                Dictionary<short, short> rankMap;
                ObjectCache memcache = MemoryCache.Default;
                rankMap = (Dictionary<short, short>)memcache.Get("LeadRankMasterMinRankMap");

                if (rankMap == null)
                {
                    rankMap = new Dictionary<short, short>();
                    CacheItemPolicy objCachePolicies = new()
                    {
                        AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow.AddHours(24))
                    };

                    SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                    DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_GetMinRankMap]", SqlParam);

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            short LeadRankCode = (row["LeadRankCode"] != DBNull.Value && row["LeadRankCode"] != null) ? Convert.ToInt16(row["LeadRankCode"]) : Convert.ToInt16(0);
                            short minRank = (row["MinRank"] != DBNull.Value && row["MinRank"] != null) ? Convert.ToInt16(row["MinRank"]) : Convert.ToInt16(0);
                            rankMap.TryAdd(LeadRankCode, minRank);
                        }

                        memcache.Add("LeadRankMasterMinRankMap", rankMap, objCachePolicies);
                    }
                    
                    return rankMap;
                }
                else
                    return rankMap;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }


}
