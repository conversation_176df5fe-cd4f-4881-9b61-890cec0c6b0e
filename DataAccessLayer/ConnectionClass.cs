﻿using Helper;
using Microsoft.Extensions.Configuration;


namespace DataAccessLibrary
{
    public class ConnectionClass
    {
        public static string LivesqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string Enviornment = CoreCommonMethods.GetEnvironmentVar();

            string DbConnection = con.GetSection("Communication").GetSection("ConnectionString").GetSection(Enviornment).Value.ToString();
            return DbConnection;
        }

        public static string ReplicasqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string Enviornment = CoreCommonMethods.GetEnvironmentVar();

            string DbConnection = con.GetSection("Communication").GetSection("ReplicaConnectionString").GetSection(Enviornment).Value.ToString();
            return DbConnection;
        }
        public static string ProductDBsqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string Enviornment = CoreCommonMethods.GetEnvironmentVar();

            string DbConnection = con.GetSection("Communication").GetSection("ProductDBConnection").GetSection(Enviornment).Value.ToString();
            return DbConnection;
        }

        public static string BmsDBsqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string Enviornment = CoreCommonMethods.GetEnvironmentVar();

            string DbConnection = con.GetSection("Communication").GetSection("BMSsqlConnectionString").GetSection(Enviornment).Value.ToString();
            return DbConnection;
        }
    }
}
