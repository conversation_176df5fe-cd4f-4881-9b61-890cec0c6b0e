﻿using Newtonsoft.Json;
using PropertyLayers;
//using ReadXmlProject;
using Redis;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace DataAccessLibrary
{
    public class PredictiveAgentStatusRedis
    {
        #region Private Methods
        private static string GetPredictiveAgentKey(string Collection,string Id)
        {
            string Key = string.Empty;
            try
            {
                if (!(string.IsNullOrEmpty(Id) && string.IsNullOrEmpty(Collection)))
                {
                    Key = $"{Collection?.Trim()}:{Id?.Trim()}";
                }
            }
            catch (Exception ex)
            {
               // //LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(Id), ex.ToString(), "GetPredictiveAgentKey", "GetPredictiveAgentKey", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return Key;
        }
        private static PredictiveAgentStatus GetPredictiveAgents(string AgentId)
        {
            PredictiveAgentStatus obj = null;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key =$"{RedisCollection.PredictiveAgent()}:{AgentId.Trim()}";
                    var result = RedisHelper.GetRedisData(Key);
                    if (!string.IsNullOrEmpty(result))
                        obj = JsonConvert.DeserializeObject<PredictiveAgentStatus>(result);
                }
            }
            catch (Exception ex)
            {
               // //LoggingHelper.LoggingHelper.AddloginQueue("",Convert.ToInt32(AgentId), ex.ToString(), "GetPredictiveAgents", "GetPredictiveAgents", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return obj;
        }
        //private static int InitRemainingPauseTime(List<string> UserGroup)
        //{
        //    int RemainingTime = 0;
        //    string remainingpausetime = string.Empty;
        //    try
        //    {
        //        List<string> extragroups = "remainingpausetimeextragroup".AppSettings().Split(',').ToList();
        //        var IsExist = extragroups.Intersect(UserGroup);
        //        if (IsExist.Any())
        //        {
        //            remainingpausetime = ReadXml.fnGetKeyValue("remainingpausetime"); 
        //        }
        //        else
        //        {
        //            remainingpausetime = ReadXml.fnGetKeyValue("remainingpausetimeextra"); 
        //        }
        //        RemainingTime = Convert.ToInt32(remainingpausetime);
        //    }
        //    catch (Exception ex)
        //    {
        //      //  //LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(0), ex.ToString(), "InitRemainingPauseTime", "InitRemainingPauseTime", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
        //    }
        //    return RemainingTime;
        //}
        private static PredictiveAgentCalls GetAgentCallRecord(string AgentId)
        {
            PredictiveAgentCalls obj = null;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    var Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCallTrack(), AgentId);
                    var callData = RedisHelper.GetRedisData(Key);
                    if (!string.IsNullOrEmpty(callData))
                    {
                        obj = JsonConvert.DeserializeObject<PredictiveAgentCalls>(callData);
                    }
                }
            }
            catch (Exception ex)
            {
                ////LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(0), ex.ToString(), "GetAgentCallRecord", "GetAgentCallRecord", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return obj;
        }
        private static void SetAgentCallRecord(string AgentId, PredictiveAgentCalls record)
        {
            try
            {
                if (record != null && !string.IsNullOrEmpty(AgentId))
                {
                    var Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCallTrack(), AgentId);
                    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(record), new TimeSpan(8, 0, 0));
                }
            }
            catch (Exception ex)
            {
               // //LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(0), ex.ToString(), "GetAgentCallRecord", "GetAgentCallRecord", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
        }

        #endregion

        #region Public Methods
        //public static bool updateAgentLoginStatusByRedis(PredictiveAgentStatus PredictiveAgentStatus, DataSet data)
        //{
        //    StringBuilder sb = new StringBuilder();
        //    string strException = string.Empty;
        //    try
        //    {
        //        var obj = data;
        //        string Key = string.Empty;

        //        if (obj?.Tables?.Count > 0)
        //        {
        //            foreach (DataRow item in obj.Tables[0].Rows)
        //            {
        //                List<string> TL = new List<string>();
        //                if (item["ManagerCode"] != DBNull.Value)
        //                {
        //                    TL.Add(Convert.ToString(item["ManagerCode"]));
        //                }
        //                if (item["ManagerCode2"] != DBNull.Value)
        //                {
        //                    TL.Add(Convert.ToString(item["ManagerCode2"]));
        //                }

        //                string TLName = Convert.ToString(item["ManagerName"]);
        //                List<string> UserGroup = new List<string>();
        //                UserGroup.Add(Convert.ToString(item["UserGroupName"]));

        //                PredictiveAgentStatus objPredictiveAgentStatus = new PredictiveAgentStatus()
        //                {
        //                    AgentId = Convert.ToString(item["UserID"]),
        //                    AgentCode = PredictiveAgentStatus.AgentCode,
        //                    status = PredictiveAgentStatus.status != null ? PredictiveAgentStatus.status.ToUpper() : PredictiveAgentStatus.status,
        //                    RoleId = Convert.ToString(item["RoleId"] == DBNull.Value ? "" : item["RoleId"]),
        //                    CallType = PredictiveAgentStatus?.CallType,
        //                    Document = PredictiveAgentStatus.Document,
        //                    IsProgressive = PredictiveAgentStatus.IsProgressive,
        //                    opensv = PredictiveAgentStatus.opensv,
        //                    Grade = Convert.ToString(item["Grade"] == DBNull.Value ? "" : item["Grade"]),
        //                    UserGroup = UserGroup,
        //                    TL = TL,
        //                    TLName = TLName,
        //                    UserName = Convert.ToString(item["UserName"]),
        //                    Context = Convert.ToString(item["Contex"]),
        //                    AgentIP = PredictiveAgentStatus.AgentIP,
        //                    Asterisk_Url = Convert.ToString(item["Asterisk_Url"]),
        //                    DIDNo = Convert.ToString(item["DIDNo"]),
        //                    CallingCompany = Convert.ToString(item["CallingCompany"]),
        //                    IsWFH = Convert.ToBoolean(item["IsWFH"]),
        //                    _updatedAt = DateTime.Now,
        //                    IsCustAnswered = false,
        //                    ProductId = Convert.ToString(item["ProductId"]),
        //                };

        //                if (!string.IsNullOrEmpty(objPredictiveAgentStatus?.AgentId))
        //                {
        //                    sb.Append("Status: " + objPredictiveAgentStatus.status);

        //                    //Set Remaining Time 
        //                    var AgentData = GetPredictiveAgents(objPredictiveAgentStatus?.AgentId);
        //                    if (AgentData?.remainingpausetime == null || AgentData?.remainingpausetime < 0 )
        //                    {
        //                        objPredictiveAgentStatus.remainingpausetime = string.IsNullOrEmpty(Convert.ToString(item["PauseTimer"])) ? -1 : Convert.ToInt32(item["PauseTimer"]);
        //                    }
        //                    else
        //                    {
        //                        objPredictiveAgentStatus.remainingpausetime = AgentData.remainingpausetime;
        //                    }

        //                    // Add AgentData In Redis
        //                    objPredictiveAgentStatus.LastUpdatedOn = AgentData?.LastUpdatedOn != null ? AgentData.LastUpdatedOn : DateTime.MinValue;
        //                    objPredictiveAgentStatus.LeadId = string.IsNullOrEmpty(AgentData?.LeadId) ? string.Empty : AgentData.LeadId;
        //                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), objPredictiveAgentStatus.AgentId);
        //                    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(objPredictiveAgentStatus), new TimeSpan(8, 0, 0));

        //                    // Add AgentIP In Redis
        //                    //Key = GetPredictiveAgentKey(RedisCollection.AgentIP(), PredictiveAgentStatus?.AgentIP);
        //                    //RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));

        //                    // Add AgentCode In Redis
        //                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCode(), PredictiveAgentStatus.AgentCode);
        //                    RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));

        //                    //Add DIDno in redis
        //                    if (!string.IsNullOrEmpty(objPredictiveAgentStatus.DIDNo))
        //                    {
        //                        Key = GetPredictiveAgentKey(RedisCollection.AgentPhone(), objPredictiveAgentStatus.DIDNo);
        //                        RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
        //                    }

        //                    // Add Context In Redis ZAdd
        //                        if (item["Contex"] != DBNull.Value)
        //                    {
        //                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentContext(), Convert.ToString(item["Contex"]).ToLower());
        //                        RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
        //                    }

        //                    // Add Manager1 In Redis ZAdd
        //                    if (item["ManagerCode"] != DBNull.Value)
        //                    {
        //                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentManagerIndex(), Convert.ToString(item["ManagerCode"]));
        //                        RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
        //                        sb.AppendLine("Manager: " + Convert.ToString(item["ManagerCode"]) + " - Agent: "+ objPredictiveAgentStatus.AgentId);
        //                    }
        //                    // Add Manager2 In Redis ZAdd
        //                    if (item["ManagerCode2"] != DBNull.Value)
        //                    {
        //                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentManagerIndex(), Convert.ToString(item["ManagerCode2"]));
        //                        RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
        //                        sb.AppendLine("Manager: " + Convert.ToString(item["ManagerCode2"]) + " - Agent: " + objPredictiveAgentStatus.AgentId);
        //                    }
        //                }

        //                #region Get/Set Agent calls In Redis
        //                PredictiveAgentCalls predictiveAgentCalls = GetAgentCallRecord(objPredictiveAgentStatus.AgentId);
        //                if (predictiveAgentCalls != null)
        //                {
        //                    predictiveAgentCalls.TotalCalls += PredictiveAgentStatus.TotalCalls;
        //                    predictiveAgentCalls.TotalTalkTime += PredictiveAgentStatus.TotalTalkTime;
        //                }
        //                else
        //                {
        //                    predictiveAgentCalls = new PredictiveAgentCalls()
        //                    {
        //                        TotalCalls = PredictiveAgentStatus.TotalCalls,
        //                        TotalTalkTime = PredictiveAgentStatus.TotalTalkTime,
        //                        TotalConnectedCalls = PredictiveAgentStatus.TotalConnectedCalls,
        //                        TotalUniqueCalls = PredictiveAgentStatus.TotalUniqueCalls,
        //                    };
        //                }
        //                SetAgentCallRecord(objPredictiveAgentStatus.AgentId, predictiveAgentCalls);
        //                #endregion

        //                #region Add AsteriskToken In Redis
        //                if (!string.IsNullOrEmpty(PredictiveAgentStatus?.AsteriskToken))
        //                {
        //                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), Convert.ToString(objPredictiveAgentStatus.AgentId));
        //                    RedisHelper.SetRedisData(Key, PredictiveAgentStatus.AsteriskToken, new TimeSpan(8, 0, 0));
        //                }
        //                #endregion
        //            }
        //        }
        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        strException = ex.Message.ToString();
        //        return false;
        //    }
        //    finally
        //    {
        //       // //LoggingHelper.LoggingHelper.AddloginQueue(PredictiveAgentStatus?.AgentCode, 0,strException, "updateAgentLoginStatusByRedis", "updateAgentLoginStatusByRedis", "DialerDataUpdatation", sb.ToString(), "", DateTime.Now, DateTime.Now);
        //    }
        //}
        //public static bool UpdateUniqueDialCountByRedis(string AgentId, Int16 TotalCalls, Int16 UniqueCalls, int Talktime, int ConnectedDial, Int16 VCCount, Int16 VCConnectCount)
        //{
        //    try
        //    {
        //        if (string.IsNullOrEmpty(AgentId))
        //            return false;

        //        PredictiveAgentCalls predictiveAgentCalls = GetAgentCallRecord(AgentId);
        //        if (predictiveAgentCalls != null)
        //        {
        //            predictiveAgentCalls.TotalCalls = TotalCalls;
        //            predictiveAgentCalls.TotalTalkTime = Talktime;
        //            predictiveAgentCalls.TotalConnectedCalls = ConnectedDial;
        //            predictiveAgentCalls.TotalUniqueCalls = UniqueCalls;
        //            predictiveAgentCalls.VCCount = VCCount;
        //            predictiveAgentCalls.VCConnectCount = VCConnectCount;
        //        }
        //        else
        //        {
        //            predictiveAgentCalls = new PredictiveAgentCalls()
        //            {
        //                TotalCalls = TotalCalls,
        //                TotalTalkTime = Talktime,
        //                TotalConnectedCalls = ConnectedDial,
        //                TotalUniqueCalls = UniqueCalls,
        //                VCCount = VCCount,
        //                VCConnectCount = VCConnectCount
        //        };
        //        }
        //        SetAgentCallRecord(AgentId, predictiveAgentCalls);
        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        //LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "UpdateUniqueDialCountByRedis", "UpdateUniqueDialCountByRedis", "DialerDataUpdatation", "", "", DateTime.Now, DateTime.Now);
        //        return false;
        //    }
        //}
        //public static bool updateAgentStatusByRedis(PredictiveAgentStatus predictiveAgentStatus, bool onCall = false)
        //{
        //    try
        //    {
        //        PredictiveAgentStatus update = null;

        //        if (predictiveAgentStatus == null)
        //            return false;

        //        var agentId = GetUserIdByRedis(predictiveAgentStatus?.AgentCode);
        //        if (agentId != 0)
        //            update = GetPredictiveAgents(Convert.ToString(agentId));
        //        else
        //            return false;

        //        if (onCall)
        //        {
        //            if (update != null)
        //            {
        //                if (!string.IsNullOrEmpty(update.status))
        //                {
        //                    if (update.status.ToLower().Equals(predictiveAgentStatus.status.ToLower()))
        //                    {
        //                        update._updatedAt = DateTime.Now;
        //                    }
        //                    else
        //                    {
        //                        update.status = predictiveAgentStatus.status;
        //                        update._updatedAt = DateTime.Now;
        //                        update.LastUpdatedOn = DateTime.Now;
        //                    }
        //                }
        //            }
        //        }
        //        else if (predictiveAgentStatus._updatedAt != DateTime.MinValue)
        //        {
        //            update.status = predictiveAgentStatus.status;
        //            update.IsCustAnswered = predictiveAgentStatus.IsCustAnswered;
        //           // update.Document = predictiveAgentStatus.Document;
        //            update._updatedAt = predictiveAgentStatus._updatedAt;
        //            update.LeadId = predictiveAgentStatus.LeadId;
        //            update.opensv = predictiveAgentStatus.opensv;
        //            update.LastUpdatedOn = predictiveAgentStatus._updatedAt;

        //            // Update Agent Call Records
        //            PredictiveAgentCalls predictiveAgentCalls = GetAgentCallRecord(agentId.ToString());
        //            if (predictiveAgentCalls != null)
        //            {
        //                predictiveAgentCalls.LeadId = predictiveAgentStatus.LeadId;
        //                predictiveAgentCalls.CallType = predictiveAgentStatus.CallType;
        //                predictiveAgentCalls.TotalCalls += predictiveAgentStatus.TotalCalls;
        //                predictiveAgentCalls.TotalTalkTime += predictiveAgentStatus.TotalTalkTime;
        //                predictiveAgentCalls.TotalConnectedCalls += predictiveAgentStatus.TotalTalkTime > 0? 1: 0;
        //                predictiveAgentCalls.TotalUniqueCalls += predictiveAgentStatus.TotalUniqueCalls;
        //                predictiveAgentCalls.CallId = predictiveAgentStatus.CallId;
        //            }
        //            else
        //            {
        //                predictiveAgentCalls = new PredictiveAgentCalls()
        //                {
        //                    LeadId = predictiveAgentStatus.LeadId,
        //                    CallType = predictiveAgentStatus.CallType,
        //                    TotalCalls = predictiveAgentStatus.TotalCalls,
        //                    TotalTalkTime = predictiveAgentStatus.TotalTalkTime,
        //                    TotalConnectedCalls = predictiveAgentStatus.TotalConnectedCalls,
        //                    TotalUniqueCalls = predictiveAgentStatus.TotalUniqueCalls,
        //                    CallId = predictiveAgentStatus.CallId,
        //                };
        //            }
        //            SetAgentCallRecord(agentId.ToString(), predictiveAgentCalls);
        //           // return true;

        //        }
        //        else
        //        {
        //            update.status = predictiveAgentStatus.status;

        //            if (update != null)
        //            {
        //                if (predictiveAgentStatus?.status?.ToUpper() == "HOLD")
        //                {
        //                    update.holdtime = DateTime.Now;
        //                }
        //                else if (predictiveAgentStatus?.status?.ToUpper() == "LOGOUT")
        //                {
        //                    update.logouttime = DateTime.Now;
        //                }
        //            }
        //        }

        //        if (update != null)
        //        {
        //            if (predictiveAgentStatus.lastActiveTime != DateTime.MinValue)
        //            {
        //                update.lastActiveTime = predictiveAgentStatus.lastActiveTime;
        //            }

        //            RedisHelper.SetRedisData(GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), Convert.ToString(agentId)), JsonConvert.SerializeObject(update), new TimeSpan(8, 0, 0));
        //            return true;
        //        }
        //        return false;
        //    }
        //    catch (Exception ex)
        //    {
        //      //  //LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "updateAgentStatusByRedis", "updateAgentStatusByRedis", "DialerDataUpdatation",JsonConvert.SerializeObject(predictiveAgentStatus), "", DateTime.Now, DateTime.Now);
        //        return false;
        //    }
        //}
        public static bool SetPlayRemainingTimeByRedis(string UserId)
        {
            PredictiveAgentStatus _PredictiveAgentStatus = new PredictiveAgentStatus();
            PredictiveAgentStatus update = null;
            try
            {
                _PredictiveAgentStatus = GetPredictiveAgents(UserId);

                if (_PredictiveAgentStatus != null)
                {
                    if (_PredictiveAgentStatus.ispause)
                    {
                        update = _PredictiveAgentStatus;
                        Int32 remainingTime = _PredictiveAgentStatus.remainingpausetime - Convert.ToInt32((DateTime.Now - Convert.ToDateTime(_PredictiveAgentStatus.pausetime)).TotalSeconds);
                        update.remainingpausetime = remainingTime;
                        update.status = "IDLE";
                        update.pausetime = DateTime.MinValue;
                        update.ispause = false;
                    }
                    else
                    {
                        update = _PredictiveAgentStatus;
                        update.status = "PAUSE";
                        update.pausetime = DateTime.Now;
                        update.lastpausetime = DateTime.Now;
                        update.ispause = true;
                    }
                }

                if (update != null)
                {
                    RedisHelper.SetRedisData(GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), UserId), JsonConvert.SerializeObject(update), new TimeSpan(8, 0, 0));
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "SetPlayRemainingTimeByRedis", "SetPlayRemainingTimeByRedis", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
                return false;
            }
        }
        public static string SoftPhoneLoginByRedis(string agentCode)
        {
            string data = string.Empty;
            try
            {
                int AgentId = GetUserIdByRedis(agentCode);
                if (AgentId != 0)
                {
                    string Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), Convert.ToString(AgentId));
                    var AsteriskToken = RedisHelper.GetRedisData(Key);
                    if (!string.IsNullOrEmpty(AsteriskToken))
                        data = "{\"AsteriskToken\":\"" + AsteriskToken + "\"}";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "SoftPhoneLoginByRedis", "SoftPhoneLoginByRedis", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return data;
        }
        //public static void ResetAsteriskToken(string AgentId)
        //{
        //    try
        //    {
        //        if (!string.IsNullOrEmpty(AgentId))
        //        {
        //            string Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), AgentId);
        //            RedisHelper.SetRedisData(Key, "12345678", new TimeSpan(8, 0, 0));
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        //LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "SoftPhoneLoginByRedis", "SoftPhoneLoginByRedis", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
        //    }
        //}
        public static int GetUserIdByRedis(string EmpCode)
        {
            string UserID = "0";
            try
            {
                if (!string.IsNullOrEmpty(EmpCode))
                {
                    string Key = $"{RedisCollection.PredictiveAgentCode()}:{EmpCode.Trim()}";
                    UserID = RedisHelper.GetRedisData(Key);
                    if (string.IsNullOrEmpty(UserID))
                    {
                        UserID = "0";
                    }
                    return Convert.ToInt32(UserID);
                }
            }
            catch (Exception ex)
            {
                //LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetUserIdByRedis", "GetUserIdByRedis", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return Convert.ToInt32(UserID);
        }

        public static void UpdateRedisLeadID(string LeadID, string AgentCode, string Mobile)
        {
            try
            {
                var AgentId = GetUserIdByRedis(AgentCode);
                if (AgentId != 0)
                {
                    string key = "mobile:" + Mobile;
                    string AgentCallStatus = "{\"status\": 200, \"statusText\": \"CallInitiated\", \"data\": {\"customerAnswered\": false, \"leadId\": " + LeadID + "}}";
                    PredictiveAgentStatus AgentStatus = GetPredictiveAgents(Convert.ToString(AgentId));
                    if (AgentStatus != null)
                    {
                        AgentStatus.status = "IDLE";
                        AgentStatus._updatedAt = DateTime.Now;
                        AgentStatus.LastUpdatedOn = DateTime.Now;

                        PredictiveAgentCalls AgentCalls = GetAgentCallRecord(Convert.ToString(AgentId));
                        if (AgentCalls != null)
                        {
                            AgentCalls.LeadId = LeadID;
                            AgentCalls.CallType = "OB";
                        }
                        else
                        {
                            AgentCalls = new PredictiveAgentCalls()
                            {
                                CallType = "OB",
                                LeadId = LeadID,
                                TotalCalls = 0,
                                TotalConnectedCalls = 0,
                                TotalTalkTime = 0,
                                TotalUniqueCalls = 0
                            };
                        }
                        SetAgentCallRecord(Convert.ToString(AgentId), AgentCalls);
                        RedisHelper.SetRedisData(GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), Convert.ToString(AgentId)), JsonConvert.SerializeObject(AgentStatus), new TimeSpan(8, 0, 0));
                        RedisHelper.SetRedisData(key, AgentCallStatus, new TimeSpan(3, 0, 0));
                    }
                }
            }
            catch (Exception ex)
            {
                //LoggingHelper.LoggingHelper.AddloginQueue("LeadID", Convert.ToInt64(LeadID), ex.ToString(), "UpdateRedisLeadID", "UpdateRedisLeadID", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
        }

        public static string GetAgentStatus(Int64 AgentId)
        {
            string status = "IDLE";
            try
            {
                List<string> checkStatus = new List<string>() { "IDLE", "BUSY", "RINGING", "CALLINITIATED", "MIC-OFF", "UNAVAILABLE", "VIDEOMEET" };
                if (AgentId != 0)
                {
                    PredictiveAgentStatus AgentStatus = GetPredictiveAgents(Convert.ToString(AgentId));
                    if (AgentStatus != null && (!string.IsNullOrEmpty(AgentStatus.status)))
                    {
                        status = (checkStatus.Contains(AgentStatus.status.ToUpper()) ? "IDLE" : AgentStatus.status);
                    }
                }
            }
            catch(Exception ex)
            {

            }
            return status;
        }
        public static PredictiveAgentStatus GetAgentDetails(string AgentId)
        {
            PredictiveAgentStatus AgentDetail = GetPredictiveAgents(AgentId);
            
            return AgentDetail;
        }
        #endregion

        public static bool updateAgentLoginRedis(PredictiveAgentStatus objPredictiveAgentStatus)
        {
            StringBuilder sb = new StringBuilder();
            string strException = string.Empty;
            try
            {
                string Key = string.Empty;

                if (!string.IsNullOrEmpty(objPredictiveAgentStatus?.AgentId))
                {
                    sb.Append("Status: " + objPredictiveAgentStatus.status);

                    //Set Remaining Time 
                    var AgentData = GetPredictiveAgents(objPredictiveAgentStatus?.AgentId);
                    if (AgentData?.remainingpausetime != null)
                    {
                        objPredictiveAgentStatus.remainingpausetime = AgentData.remainingpausetime;
                        objPredictiveAgentStatus.lastpausetime = AgentData.lastpausetime;
                    }

                    // Add AgentData In Redis
                    //objPredictiveAgentStatus.LastUpdatedOn = AgentData?.LastUpdatedOn != null ? AgentData.LastUpdatedOn : DateTime.MinValue;
                    objPredictiveAgentStatus.LeadId = string.IsNullOrEmpty(AgentData?.LeadId) ? string.Empty : AgentData.LeadId;
                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), objPredictiveAgentStatus.AgentId);
                    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(objPredictiveAgentStatus), new TimeSpan(8, 0, 0));

                    // Add AgentIP In Redis
                    //Key = GetPredictiveAgentKey(RedisCollection.AgentIP(), PredictiveAgentStatus?.AgentIP);
                    //RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));

                    // Add AgentCode In Redis
                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCode(), objPredictiveAgentStatus.AgentCode.ToUpper());
                    RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));

                    //Add DIDno in redis
                    if (!string.IsNullOrEmpty(objPredictiveAgentStatus.DIDNo))
                    {
                        Key = GetPredictiveAgentKey(RedisCollection.AgentPhone(), objPredictiveAgentStatus.DIDNo);
                        RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                    }

                    // Add Context In Redis ZAdd
                    if (!string.IsNullOrEmpty(objPredictiveAgentStatus.Context))
                    {
                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentContext(), Convert.ToString(objPredictiveAgentStatus.Context).ToLower());
                        RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                    }

                    if(objPredictiveAgentStatus.TL != null && objPredictiveAgentStatus.TL.Count > 0 )
                    {
                        foreach(var TL in objPredictiveAgentStatus.TL)
                        {
                            if (!string.IsNullOrEmpty(TL))
                            {
                                Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentManagerIndex(), Convert.ToString(TL));
                                RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                                sb.AppendLine("Manager: " + Convert.ToString(TL) + " - Agent: " + objPredictiveAgentStatus.AgentId);
                            }
                        }
                    }
                }

                #region Get/Set Agent calls In Redis
                PredictiveAgentCalls predictiveAgentCalls = GetAgentCallRecord(objPredictiveAgentStatus.AgentId);
                if (predictiveAgentCalls != null)
                {
                    predictiveAgentCalls.TotalCalls += objPredictiveAgentStatus.TotalCalls;
                    predictiveAgentCalls.TotalTalkTime += objPredictiveAgentStatus.TotalTalkTime;
                }
                else
                {
                    predictiveAgentCalls = new PredictiveAgentCalls()
                    {
                        TotalCalls = objPredictiveAgentStatus.TotalCalls,
                        TotalTalkTime = objPredictiveAgentStatus.TotalTalkTime,
                        TotalConnectedCalls = objPredictiveAgentStatus.TotalConnectedCalls,
                        TotalUniqueCalls = objPredictiveAgentStatus.TotalUniqueCalls,
                    };
                }
                SetAgentCallRecord(objPredictiveAgentStatus.AgentId, predictiveAgentCalls);
                #endregion

                #region Add AsteriskToken In Redis
                if (!string.IsNullOrEmpty(objPredictiveAgentStatus?.AsteriskToken))
                {
                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), Convert.ToString(objPredictiveAgentStatus.AgentId));
                    RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AsteriskToken, new TimeSpan(8, 0, 0));
                }
                #endregion
                return true;
            }
            catch (Exception ex)
            {
                strException = ex.Message.ToString();
                return false;
            }
            finally
            {
                //LoggingHelper.LoggingHelper.AddloginQueue(objPredictiveAgentStatus?.AgentCode, 0, strException, "updateAgentLoginStatusByRedis", "updateAgentLoginStatusByRedis", "DialerDataUpdatation", sb.ToString(), "", DateTime.Now, DateTime.Now);
            }
        }
        public static string GetAppToken(string AgentId)
        {
            string result = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key = $"{RedisCollection.AppKey()}:{AgentId.Trim()}";
                    result = RedisHelper.GetRedisData(Key);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(AgentId), ex.ToString(), "GetAppToken", "Allocation", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public static string GetMatrixToken(string AgentId)
        {
            string result = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), AgentId);
                    result = RedisHelper.GetRedisData(Key);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(AgentId), ex.ToString(), "GetMatrixToken", "CommAPI", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }
    }
}
