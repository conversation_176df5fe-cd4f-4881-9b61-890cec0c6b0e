﻿using DataHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using PropertyLayers;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using MongoDB.Bson;

namespace DataAccessLibrary
{
    public class MongoDLL
    {

        public static List<SysConfigData> GetConfigValueFromMongo(string Source)
        {
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = null;
                varquery = Query.EQ("source", Source);
                var documents = _CommDB.GetDocuments<SysConfigData>(varquery, MongoCollection.ConfigValues());
                return documents;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetConfigValueFromMongo." + ex.ToString() + "Source "+ Source);
                return null;
            }
        }

        public static List<SysConfigData> GetConfigValueFromMongo()
        {
            string result = string.Empty;
            try
            {
                IMongoFields Field = Fields.Include("_id", "source", "authKey", "clientKey");
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var data = objCommDB.GetDocuments<SysConfigData>(null, Field, MongoCollection.ConfigValues());
                return data;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetConfigValueFromMongo." + ex.ToString());
                return null;
            }
        }
        public static Dictionary<string, object> GetConfigFromMongo(string key)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            try
            {
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.EQ("_id", key);
                var value = objCommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.ConfigFiles());
                result = (Dictionary<string, object>)BsonTypeMapper.MapToDotNetValue(value);
                return result;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static void UpdateDocument(IMongoQuery query, IMongoUpdate updateQuery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            objCommDB.UpdateDocument(query, updateQuery, DataAccessLibrary.MongoCollection.LPDataCollection());
        }

        public static PriorityModel GetLeadDetails(Int64 LeadId)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                IMongoFields Field = Fields.Include("_id", "CustName", "ProductID", "CustID");
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoSortBy OrderBy = SortBy.Descending("LeadCreatedOn");
                var query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                var lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, MongoCollection.LPDataCollection(), OrderBy, Field, 0, 0).ToList();
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                    return lstPriorityModel[0];
                else return null;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, ex.ToString(), "GetPriorityModelMongo_Error", "LeadPrioritizationDLL", "", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static bool IsValidateCustomer(string EncryptLeadId, string Token)
        {
            bool result = false;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.And(Query.EQ("encryptLeadId", EncryptLeadId), Query.EQ("Token", Convert.ToInt64(Token)));
                IMongoFields Field = Fields.Include("encryptLeadId", "Token");
                var value = _CommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.CustomerAuthenticate(), Field);
                if (value != null)
                    result = true;

            }
            catch (Exception ex)
            {
                result = false;
            }
            return result;
        }
        public static long getExistRecord(string EncryptLeadId)
        {
            long result = 0;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.And(Query.EQ("_id", EncryptLeadId));
                IMongoFields Field = Fields.Include("_id", "Token");
                var value = _CommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.CustomerAuthenticate(), Field);
                if (value != null)
                    result= Convert.ToInt64(value.GetValue("Token"));

            }
            catch (Exception ex)
            {
                return result;
            }
            return result;
        }

        public static ACLConfigData FindACLMethod(string Method)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            IMongoQuery varquery = Query.And(Query.EQ("method", Method), Query.EQ("isActive", true));
            return objCommDB.FindOneDocument<ACLConfigData>(varquery, MongoCollection.ACLConfigValues());

        }
        public static List<ACLConfigData> GetACLMongoConfig(IMongoFields Field, IMongoQuery varquery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var data = objCommDB.GetDocuments<ACLConfigData>(varquery, Field, MongoCollection.ACLConfigValues());
            return data;

        }

        public static Dictionary<string, SysConfigData> GetConfiValueFromMongo()
        {
            string result = string.Empty;
            try
            {
                IMongoFields Field = Fields.Include("source", "authKey", "clientKey", "EncKey", "EncIV");
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var data = objCommDB.GetDocuments<SysConfigData>(null, Field, MongoCollection.ConfigValues());

                //Dictionary<string, SysConfigData> dictionary = data.ToDictionary(doc => doc.source.ToLower());

                Dictionary<string, SysConfigData> dictionary = data
                .GroupBy(data => data.source.ToLower())
                .ToDictionary(group => group.Key, group => group.First());

                return dictionary;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString());
                return null;
            }
        }
    }
}
