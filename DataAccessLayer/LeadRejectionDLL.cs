﻿using DataAccessLibrary;
using DataHelper;
using Helper;
using MongoDB.Driver;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.Caching;
//using Amazon.Athena;
//using Amazon.Athena.Model;
//using Amazon.DynamoDBv2;

namespace DataAccessLayer
{
    public class LeadRejectionDLL
    {
        //public static Task<GetQueryResultsResponse> GetAllLeadsFromAthena()
        //{

        //    string query_ans = "SELECT mobileno, leadid, name, createdon, dob, exitpointurl FROM sqldb_athena.matrix_crm_leaddetails limit 10;";
        //    return AthenaQueryExecutor.ExecuteQueryAsync(query_ans);
        //}

        public static DataSet GetAllActiveChildFromParent(Int64 ParentID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ParentID", ParentID);
          

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAllActiveChildFromParent]", SqlParam);

            return ds;
        }

        public static DataSet GetLeadDetailsForRejection(Int64 ParentID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ParentID", ParentID);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadDetailsForRejection]", SqlParam);

            return ds;
        }

        /*
        public static DataSet GetProductDetails(Int64 LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetProductDetails]", SqlParam);

            return ds;
        }

        public static DataSet GetLeadAssignInfo(Int64 ParentID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ParentID", ParentID);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadAssignInfo]", SqlParam);

            return ds;
        }

        public static DataSet GetCallDataFromLead(Int64 LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCDHData]", SqlParam);

            return ds;
        }

        public static DataSet GetCallBackData(Int64 LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCallBackData]", SqlParam);

            return ds;
        }

        */

        public static DataSet GetAllParentIDs(DateTime FromDate, DateTime ToDate)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@FromDate", FromDate);
            SqlParam[1] = new SqlParameter("@ToDate", ToDate);               

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAllParentIDs]", SqlParam);
            
            return ds;
        }

        

        public static DataSet RejectLeads(Int64 LeadID, Int16 StatusID, short ProductID, string reason, int subStatusID=0)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[1] = new SqlParameter("@StatusID", StatusID);
            SqlParam[2] = new SqlParameter("@ProductId", ProductID);
            SqlParam[3] = new SqlParameter("@Reason", reason);
            SqlParam[4] = new SqlParameter("@subStatusID", subStatusID);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[RejectLeads]", SqlParam);

            return ds;
        }

        public static DataSet DumpRejectedLeads(Int64 LeadID, short ProductID, DateTime CurrDate, string reason)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[2] = new SqlParameter("@CurrDate", CurrDate);
            SqlParam[1] = new SqlParameter("@ProductId", ProductID);
            SqlParam[3] = new SqlParameter("@Reason", reason);



            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[DumpRejectedLeadsTemp]", SqlParam);

            return ds;
        }

        public static DataSet GetGroupIdByProcessID(Int16 ProcessID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ProcessID", ProcessID);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetGroupIdFromProdGrpMappingAllc]", SqlParam);

            return ds;
        }

        //Mongo DLL
        public static List<LeadRejectionLogicsDocument> GetLeadRejectionLogicsDocs(Int16 ProductId)
        {
            try
            {
                List<LeadRejectionLogicsDocument> objLeadRejectionLogicsDocuments = new List<LeadRejectionLogicsDocument>();

                string Key = $"{RedisCollection.LeadRejectionLogicsDocument()}_{ProductId}";

                if (MemoryCache.Default[Key] != null)
                    objLeadRejectionLogicsDocuments = (List<LeadRejectionLogicsDocument>)MemoryCache.Default.Get(Key);
                else
                {
                    MongoHelperV2 OneLeadDb = new(SingletonClass.OneLeadDBV2());
                    var filter = Builders<LeadRejectionLogicsDocument>.Filter.And(Builders<LeadRejectionLogicsDocument>.Filter.Eq(item => item.IsActive, true), Builders<LeadRejectionLogicsDocument>.Filter.Eq(item => item.ProductId, ProductId));

                    objLeadRejectionLogicsDocuments = OneLeadDb.GetDocuments<LeadRejectionLogicsDocument>(filter, DataAccessLibrary.MongoCollection.LeadRejectionLogics());
                    if (objLeadRejectionLogicsDocuments.Count > 0)
                        CommonCache.GetOrInsertIntoCache(objLeadRejectionLogicsDocuments, Key, 8 * 60);

                   
                }
                return objLeadRejectionLogicsDocuments;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, ProductId, ex.ToString(), "GetLeadRejectionLogicsDocs", "LeadRejection", "LeadRejectionDLL", "", " ", DateTime.Now, DateTime.Now);
                return null;
            }
        }



    }

}




