﻿using DataAccessLibrary;
using DataHelper;
using Helper;
using Microsoft.Extensions.Configuration;
using System;
using System.Data;
using System.Data.SqlClient;

namespace DataAccessLayer
{
    public class TermPayURanksDLL
    {
        #region Lead Rank
        public static DataSet GetBookingforPayuRank()
        {

            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetBookingforPayuRank]");

        }
        public static void InsertBookingRanks(long LeadID, short LeadRank)
        {
            SqlParameter[] sqlParam = null;
            sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadID", LeadID);
            sqlParam[1] = new SqlParameter("@Rank", LeadRank);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLifeBokingsPAYURank]", sqlParam);
        }

        #endregion

        #region Income Data
        public static DataSet GetLeadsforPayuIncome(DateTime startDate, DateTime endTime)
        {
            SqlParameter[] sqlParam = null;
            sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@startDate", startDate);
            sqlParam[1] = new SqlParameter("@endDate", endTime);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadsForIncomePAYU]", sqlParam);
        }

        public static void InsertPayUIncomeData(long LeadID, long PredictedIncome, long CustomerID)
        {
            SqlParameter[] sqlParam = null;
            sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@CustomerID", CustomerID);
            sqlParam[1] = new SqlParameter("@LeadID", LeadID);
            sqlParam[2] = new SqlParameter("@PredictedIncome", PredictedIncome);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertPayUIncome]", sqlParam);
        }

        public static long GetIncomePayU(long CustomerID)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@CustomerID", CustomerID);

            string strQuery = "SELECT TOP 1 PredictedIncome FROM MTX.IncomeDataPAYU (NOLOCK) WHERE CustomerID=@CustomerID";
            var data = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
            if (data == null || data == DBNull.Value)
            {
                return -3;
            }

            return (long)data;
        }

        #endregion

        #region TermLeadsPayU2lackincome
        public static DataSet GetTermLeadsPayU2lackincome()
        {

            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetTermLeadsPayU2lackincome]");

        }
        public static void Insert2LacLeadsPushtoTerm(long leadId, long AnnualIncome=0, long CustomerID=0, string DerivedFrom="")
        {

            string Connectionstring = ConnectionClass.LivesqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", leadId);
            SqlParam[1] = new SqlParameter("@AnnualIncome", AnnualIncome);
            SqlParam[2] = new SqlParameter("@CustomerID", CustomerID);
            SqlParam[3] = new SqlParameter("@Derivedfrom", DerivedFrom);

            SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[2LacLeads_PushtoTerm_Track]",SqlParam);

            //SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);

        }

        public static void UpdatePayUIncomeTerm(long leadId, long CustomerId, long AnnualIncome=0)
        {

            string Connectionstring = ConnectionClass.LivesqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@LeadID", leadId);
            SqlParam[1] = new SqlParameter("@CustomerID", CustomerId);
            SqlParam[2] = new SqlParameter("@AnnualIncome", AnnualIncome);

            SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdatePayUIncomeTerm]",SqlParam);
        }

        public static void SendEventToTermCJ(dynamic msgObj)
        {
            AmazonSqs amazonSqs = new();
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string SQSLogingQueueUrl = con.GetSection("Communication").GetSection("Term_annual_income_updation_PROD_SQS").Value.ToString();
            //TODO List
            string SQSQueueUrl = SQSLogingQueueUrl;
            amazonSqs.SQSSendMessage(SQSQueueUrl, msgObj);



        }

        #endregion

    }
}