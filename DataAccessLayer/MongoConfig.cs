﻿using DataHelper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using PropertyLayers;
using Redis;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using MongoDB.Bson;

namespace DataAccessLibrary
{
    public class MongoConfig
    {
     
        public static List<SysConfigData> GetConfigValueFromMongo(string Source)
        {
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = null;
                varquery = Query.EQ("source", Source);
                var documents = _CommDB.GetDocuments<SysConfigData>(varquery, MongoCollection.ConfigValues());
                return documents;
            }
            catch (Exception ex)
            {
               // Console.WriteLine("Exception in GetConfigValueFromMongo." + ex.ToString() + "Source "+ Source);
                return null;
            }
        }

        public static List<SysConfigData> GetConfigValueFromMongo()
        {
            string result = string.Empty;
            try
            {
                IMongoFields Field = Fields.Include("_id","source", "authKey", "clientKey");
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var data = objCommDB.GetDocuments<SysConfigData>(null, Field, MongoCollection.ConfigValues());
                return data;
            }
            catch (Exception ex)
            {
               // Console.WriteLine("Exception in GetConfigValueFromMongo." + ex.ToString());
                return null;
            }
        }

       

    }
}
