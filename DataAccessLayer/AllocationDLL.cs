﻿using DataAccessLibrary;
using DataHelper;
using Helper;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.Caching;
using MongoCollection = DataAccessLibrary.MongoCollection;

namespace DataAccessLayer
{
    public class AllocationDLL
    {
        #region Common Methods
        private static string SqlParameterNamesString(SqlParameter[] SqlParam)
        {
            String nameString = String.Empty;

            SqlParam.ToList().ForEach(param =>
            {
                nameString += $"{param.ParameterName}, ";
            });
            if (nameString.Length > 2)
            {
                nameString = nameString.Remove(nameString.Length - 2, 2);
            }
            return nameString;

        }
        private static object ExecuteSqlUserDefinedFunction(string Connection, string FunctionName, SqlParameter[] sqlParam)
        {
            string paramsString = SqlParameterNamesString(sqlParam);

            string FunctionCallQuery = "SELECT " + FunctionName + "(" + paramsString + ")";

            return SqlHelper.ExecuteScalar(Connection, CommandType.Text, FunctionCallQuery, sqlParam);
        }
        public static DataSet GetAllocationConfig()
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            string StrQuery = "SELECT ProductID,IsActive,IsAI,startTime,endTime FROM MTX.AllocationConfigFactor (NOLOCK) WHERE IsActive=1";
            SqlParameter[] SqlParam = new SqlParameter[0];
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, StrQuery, SqlParam);
        }
        #endregion

        #region Lead Data 
        public static DataSet GetLeadsForAllocation(DateTime FromDate, Int64 LeadId = 0)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];

            //SqlParam[0] = new SqlParameter("@FromDate", (FromDate == DateTime.MinValue) ? null : FromDate.AddMinutes(-10));
            SqlParam[1] = new SqlParameter("@LeadID", LeadId);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadsForAllocation_Health]", SqlParam);

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static DataSet GetSMELeadsForAllocation(long LeadId = 0)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadID", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeads_SMEAllocation]", SqlParam);
        }

        public static DataSet GetChurnLeadsForAllocation()
        {
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetChurnLeadsForAllocation]");

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }


        public static DataSet GetLeadHealthDetails(Int64 leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetHealthDetails]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static DataSet GetLeadAllocationKeyFactors(Int64 leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAllocationKeyFactors]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static DataSet GetAdditionalLeadDetails(long leadId, long CustomerId)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            SqlParam[1] = new SqlParameter("@CustomerId", CustomerId);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAdditionalLeadDetails]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static Int16 GetLeadRank(LeadDetails lead, byte isSpecialLeadRank = 0)
        {
            SqlParameter[] SqlParam = new SqlParameter[25];
            SqlParam[0] = new SqlParameter("@Age", lead.Age);
            SqlParam[1] = new SqlParameter("@InsurerId", lead.InsurerID);
            SqlParam[2] = new SqlParameter("@PrevBooking", lead.PreviousBooking);
            SqlParam[3] = new SqlParameter("@RepeatCustomer", lead.RepeatCustomer);
            SqlParam[4] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[5] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[6] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[7] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[8] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[9] = new SqlParameter("@IsPED", lead.IsPED);
            SqlParam[10] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[11] = new SqlParameter("@IsAdult", (lead.AdultCount == 1 && lead.ChildCount == 0) ? 1 : 0);
            SqlParam[12] = new SqlParameter("@LeadCreatedOn", lead.CreatedOn);
            SqlParam[13] = new SqlParameter("@StateID", lead.StateID);
            SqlParam[14] = new SqlParameter("@Utm_Term", lead.Utm_term);
            SqlParam[15] = new SqlParameter("@Utm_Content", null);
            SqlParam[16] = new SqlParameter("@CMDOB", lead.CMDOB);
            SqlParam[17] = new SqlParameter("@Covercount", lead.Covercount);
            SqlParam[18] = new SqlParameter("@IsSpecialLeadRank", isSpecialLeadRank);
            SqlParam[19] = new SqlParameter("@PEDIDs", lead.PEDTypes);
            SqlParam[20] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[21] = new SqlParameter("@IsUnassist", lead.IsUnAssisted);
            SqlParam[22] = new SqlParameter("@Country", lead.Country);
            SqlParam[23] = new SqlParameter("@Language", lead.Language);
            SqlParam[24] = new SqlParameter("@ExistingLeadRank", lead.LastLeadRank);

            var LeadRank = ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[GetLeadRank_Health_Allocation]", SqlParam);
            if (LeadRank == null)
            {
                return 0;
            }
            return (short)LeadRank;
        }

        public static Int16 GetLeadRankV2(LeadDetails lead, Dictionary<string, dynamic> rE_results, byte isSpecialLeadRank = 0)
        {

            SqlParameter[] SqlParam = new SqlParameter[28];
            SqlParam[0] = new SqlParameter("@Age", lead.Age);
            SqlParam[1] = new SqlParameter("@InsurerId", lead.InsurerID);
            SqlParam[2] = new SqlParameter("@PrevBooking", lead.PreviousBooking);
            SqlParam[3] = new SqlParameter("@RepeatCustomer", lead.RepeatCustomer);
            SqlParam[4] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[5] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[6] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[7] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[8] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[9] = new SqlParameter("@IsPED", lead.IsPED);
            SqlParam[10] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[11] = new SqlParameter("@IsAdult", (lead.AdultCount == 1 && lead.ChildCount == 0) ? 1 : 0);
            SqlParam[12] = new SqlParameter("@LeadCreatedOn", lead.CreatedOn);
            SqlParam[13] = new SqlParameter("@StateID", lead.StateID);
            SqlParam[14] = new SqlParameter("@Utm_Term", lead.Utm_term);
            SqlParam[15] = new SqlParameter("@Utm_Content", null);
            SqlParam[16] = new SqlParameter("@CMDOB", lead.CMDOB);
            SqlParam[17] = new SqlParameter("@Covercount", lead.Covercount);
            SqlParam[18] = new SqlParameter("@IsSpecialLeadRank", isSpecialLeadRank);
            SqlParam[19] = new SqlParameter("@PEDIDs", lead.PEDTypes);
            SqlParam[20] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[21] = new SqlParameter("@IsUnassist", lead.IsUnAssisted);
            SqlParam[22] = new SqlParameter("@Country", lead.Country);
            SqlParam[23] = new SqlParameter("@Language", lead.Language);
            if (rE_results.ContainsKey("IsCoreLead"))
            {
                SqlParam[24] = new SqlParameter("@IsCoreLead", rE_results.GetValueOrDefault("IsCoreLead"));
            }

            SqlParam[25] = new SqlParameter("@ExistingLeadRank", lead.LastLeadRank);

            if (rE_results.ContainsKey("CoreLeadRank"))
            {
                SqlParam[26] = new SqlParameter("@CoreLeadRank", rE_results.GetValueOrDefault("CoreLeadRank"));
            }
            SqlParam[27] = new SqlParameter("@IPCountryId", lead.IPCountryId);

            var LeadRank = ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[GetLeadRank_Health_Allocation_V2]", SqlParam);
            if (LeadRank == null)
            {
                return 0;
            }
            return (short)LeadRank;
        }

        public static string GetLeadGroupCode(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[19];
            SqlParam[0] = new SqlParameter("@ChatStatus", lead.ChatStatus);
            SqlParam[1] = new SqlParameter("@LeadCreatedON", lead.CreatedOn);
            SqlParam[2] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[3] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[4] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[5] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[6] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[7] = new SqlParameter("@Utm_Term", lead.Utm_term);
            SqlParam[8] = new SqlParameter("@IsUnassisted", lead.IsUnAssisted);
            SqlParam[9] = new SqlParameter("@source", lead.source);
            SqlParam[10] = new SqlParameter("@CommunicationID", lead.CommunicationId);
            SqlParam[11] = new SqlParameter("@InsurerId", lead.InsurerID);
            SqlParam[12] = new SqlParameter("@IsMarkExchange", lead.IsMarkExchange);
            SqlParam[13] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[14] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[15] = new SqlParameter("@SpecialLeadRank", lead.SpecialLeadRank);
            SqlParam[16] = new SqlParameter("@Country", lead.Country);
            SqlParam[17] = new SqlParameter("@StateID", lead.StateID);
            SqlParam[18] = new SqlParameter("@UserConsent", lead.UserConsent);


            string GroupCode = (string)SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadGroupCode_Allocation]", SqlParam);

            return GroupCode;
        }

        public static Decimal GetLeadScore(LeadDetails lead)
        {

            string PEDValue = string.Empty;
            try {
                var pedIdsList = !string.IsNullOrEmpty(lead.PEDTypes) 
                ? lead.PEDTypes.Trim(',').Split(',') 
                : null;

                PEDValue = pedIdsList != null && pedIdsList.Length > 1 ? $"{pedIdsList.Length}PEDs" : lead.PEDTypes;
            } 
            catch (Exception) { }

            // CustPrevProduct experiment for HEALTH-TELUGU-PED process
            short prevProduct = -1;
            if (lead.ProcessName == "HEALTH-TELUGU-PED")
            {
                prevProduct = lead.CustPrevProduct;
            }
           
            SqlParameter[] SqlParam = new SqlParameter[27];
            SqlParam[0] = new SqlParameter("@IsSelfSelect", lead.InsurerID);
            SqlParam[1] = new SqlParameter("@PrevBooking", lead.PreviousBooking);
            SqlParam[2] = new SqlParameter("@RepeatCustomer", lead.RepeatCustomer);
            SqlParam[3] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[4] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[5] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[6] = new SqlParameter("@Source", "");
            SqlParam[7] = new SqlParameter("@UTM_Term", lead.Utm_term);
            SqlParam[8] = new SqlParameter("@Utm_campaign", lead.Utm_campaign);
            SqlParam[9] = new SqlParameter("@BrandName", "");
            SqlParam[10] = new SqlParameter("@Country", lead.Country);
            SqlParam[11] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[12] = new SqlParameter("@Age", lead.Age);
            SqlParam[13] = new SqlParameter("@CMAge", lead.CMDOB);
            SqlParam[14] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[15] = new SqlParameter("@IsPED", lead.IsPED);
            SqlParam[16] = new SqlParameter("@LeadCreatedOn", lead.CreatedOn);
            SqlParam[17] = new SqlParameter("@CMCount", lead.Covercount);
            SqlParam[18] = new SqlParameter("@AdultCount", lead.AdultCount);
            SqlParam[19] = new SqlParameter("@ChildCount", lead.ChildCount);
            SqlParam[20] = new SqlParameter("@ClusterId", lead.ClusterId);
            SqlParam[21] = new SqlParameter("@ProcessName", lead.ProcessName);
            SqlParam[22] = new SqlParameter("@AnnualIncome", lead.AnnualIncome);
            SqlParam[23] = new SqlParameter("@PED", PEDValue);
            SqlParam[24] = new SqlParameter("@CustPrevProduct", prevProduct);
            SqlParam[25] = new SqlParameter("@PayuAffluence", lead.payu_affluence_score);
            SqlParam[26] = new SqlParameter("@PayuPropensity", lead.payu_propensity_score);

            var LeadScore = ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[GetHealthLeadScoreV1]", SqlParam);
            if (LeadScore == null)
            {
                return 0;
            }
            return (Decimal)LeadScore;
        }

        public static string GetLeadFraud(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            string FraudStatus = (string)SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CheckLeadFraud]", SqlParam);
            return FraudStatus ?? "none";
        }

        public static void UpdateFraudDetectionData(long LeadID, bool IsFraudCase, string FraudCategory)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            string StrQuery = "[MTX].[UpdateFraudDetectioData]";
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[1] = new SqlParameter("@IsFraudCase", IsFraudCase);
            SqlParam[2] = new SqlParameter("@FraudCategory", FraudCategory);
            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, StrQuery, SqlParam);
        }

        public static bool ChkLeadAssignedinMultiproduct(long CustomerId, short ProductId, long leadId)
        {

            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@CustID", CustomerId);
            SqlParam[1] = new SqlParameter("@ProductID", ProductId);
            SqlParam[2] = new SqlParameter("@IsMotorUnassign", 1);
            SqlParam[3] = new SqlParameter("@NewLeadID", leadId);
            object data = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[ChkLeadAssignedinMultiproduct]", SqlParam);
            int count = (data == DBNull.Value || data == null) ? 0 : (int)data;
            return count > 0;
        }

        public static bool CustomerIsBoookedAlready(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@CustID", lead.CustomerId);
            SqlParam[1] = new SqlParameter("@ProductID", lead.ProductID);
            SqlParam[2] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[3] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[4] = new SqlParameter("@LeadCreatedOn", lead.CreatedOn);
            int count = (int)SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CustomerIsBoookedAlready]", SqlParam);
            return count > 0;
        }

        public static Int16 GetLeadRankByLeadScore(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadScore", lead.LeadScore);
            SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[2] = new SqlParameter("@ProcessName", lead.ProcessName);
            SqlParam[3] = new SqlParameter("@LeadID", lead.LeadId);

            var LeadRank = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadRankByLeadScore]", SqlParam);
            if (LeadRank != null && LeadRank != DBNull.Value && LeadRank.ToString() != "0")
            {
                return Convert.ToInt16(LeadRank);
            }
            return Convert.ToInt16(0);
        }

        public static void DumpLeadDetails(LeadDetails _AllocationDetails, string AssignmentProcess = "")
        {
            string assignProcess = AssignmentProcess;
            if (assignProcess == "CHAT" && _AllocationDetails.hitPayUScoreAPI)
            {
                assignProcess = "CHAT-PayU";
            }
            string Connectionstring = ConnectionClass.LivesqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[11];
            SqlParam[0] = new SqlParameter("@AssignedToAgentId", _AllocationDetails.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@LeadId", _AllocationDetails.LeadId);
            SqlParam[2] = new SqlParameter("@JobId", _AllocationDetails.JobID);
            SqlParam[3] = new SqlParameter("@LeadRank", _AllocationDetails.LeadRank);
            SqlParam[4] = new SqlParameter("@SpecialLeadRank", _AllocationDetails.ChatLeadRank == 0 ? _AllocationDetails.SpecialLeadRank : _AllocationDetails.ChatLeadRank);
            SqlParam[5] = new SqlParameter("@GroupCode", _AllocationDetails.GroupCode);
            SqlParam[6] = new SqlParameter("@LeadScore", _AllocationDetails.LeadScore);
            SqlParam[7] = new SqlParameter("@AgentGrade", _AllocationDetails.AgentGrade);
            SqlParam[8] = new SqlParameter("@AssignmentProcess", assignProcess);
            SqlParam[9] = new SqlParameter("@CreatedOn", DateTime.Now);
            SqlParam[10] = new SqlParameter("@ProductId", _AllocationDetails.ProductID);

            SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[AllocationLeadDump]", SqlParam);

            //string strQuery = "INSERT INTO MTX.NewAllocationLeadDump(LeadId,JobId,LeadRank,SpecialLeadRank,GroupCode,LeadScore,AgentGrade, AssignedToAgentId,AssignmentProcess,CreatedOn) " +
            //    "values (@LeadId,@JobId,@LeadRank,@SpecialLeadRank,@GroupCode,@LeadScore,@AgentGrade,@AssignedToAgentId,@AssignmentProcess,@CreatedOn)";
            //SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);

        }


        public static short GetDumpedLeadRank(long leadId)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@leadId", leadId);

            string strQuery = "SELECT TOP 1 leadRank FROM MTX.NewAllocationLeadDump (NOLOCK) WHERE leadId=@leadId ORDER BY id DESC";
            var data = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
            if (data == null)
            {
                return 0;
            }
            return (short)data;
            //return LeadRank;
        }

        public static DataSet GetFosReopenLeadConfig()
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();

            string strQuery = "SELECT * FROM MTX.fosReopenLeadConfig (NOLOCK) WHERE isActive = 1";
            DataSet ds = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery);

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;
            return ds;
        }
        public static long GetLowConversionValue(LeadDetails lead)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@Leadsource", lead.LeadSource);
            SqlParam[1] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[2] = new SqlParameter("@UTM_Medium", lead.UTM_Medium);
            SqlParam[3] = new SqlParameter("@Utm_Term", lead.Utm_term);

            string strQuery = "SELECT TOP 1 Leadrank FROM [MTX].[HealthSEOLeadRankParameters] WHERE Leadsource=@LeadSource AND Utm_source=@Utm_source AND UTM_Medium=@UTM_Medium AND (Utm_Term=@Utm_Term OR Utm_Term = '')";
            object LowConversionValue = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);

            //if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
            //    return 0;
            return Convert.ToInt64(LowConversionValue);
        }

        public static string GetCustSource(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[7];

            SqlParam[0] = new SqlParameter("@UtmSource", lead.Utm_source);
            SqlParam[1] = new SqlParameter("@UtmMedium", lead.UTM_Medium);
            SqlParam[2] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[3] = new SqlParameter("@UtmTerm", lead.Utm_term);
            SqlParam[4] = new SqlParameter("@Utmcampaign", lead.Utm_campaign);
            SqlParam[5] = new SqlParameter("@UtmContent", "");
            SqlParam[6] = new SqlParameter("@ProductID", 2);

            string CustSource = (string)ExecuteSqlUserDefinedFunction(ConnectionClass.ReplicasqlConnection(), "[MTX].[GetCustomUTM]", SqlParam);
            return CustSource;

        }
        #endregion

        #region Agent Data

        public static int DumpAgentsforAllocation(short ProductId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ProductID", ProductId);

            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[DumpAgentsforAllocation]", SqlParam);
        }

        public static List<SpecialGroup> GetSpecialGroups()
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            string StrQuery = "SELECT  IsAgent,IsGroup,ProductId,SGA.GroupCode,UGM.UserGroupID  " +
                              "FROM		MTX.SpecialGroup_Allocation SGA(NOLOCK) " +
                              "INNER JOIN	CRM.UserGroupMaster UGM(NOLOCK) ON  SGA.GroupCode=UGM.GroupCode ";
            SqlParameter[] SqlParam = new SqlParameter[0];
            DataSet ds = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, StrQuery, SqlParam);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                return (from dr in ds.Tables[0].AsEnumerable()
                        select new SpecialGroup
                        {

                            IsAgent = dr.Field<bool>("IsAgent"),
                            IsGroup = dr.Field<bool>("IsGroup"),
                            ProductId = dr.Field<byte>("ProductId"),
                            GroupCode = dr.Field<string>("GroupCode"),
                            GroupID = dr.Field<Int16>("UserGroupID"),
                        }).ToList();
            }
            else
                return null;
        }

        public static Int64 GetAgentID_HealthAllocation(Int16 GroupID, Int16 ModelGrade, Int32 InsurerID, DateTime LeadDate, Int32 Age, ref Int16 OutLeadRank, ref string AssignedToEcode, ref string AssignedToAgentName, Int16 LeadCount = 0, string GroupCode = null, Int16 LastAssignedGroupID = 0, Int16 SpecialLeadRank = 0)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[14];
            SqlParam[0] = new SqlParameter("@GroupId", GroupID);

            SqlParam[1] = new SqlParameter("@P_AgentId", DbType.Int64);
            SqlParam[1].Value = 0;
            SqlParam[1].Direction = ParameterDirection.Output;

            SqlParam[2] = new SqlParameter("@LeadRank", ModelGrade);
            SqlParam[3] = new SqlParameter("@InsurerId", InsurerID);
            SqlParam[4] = new SqlParameter("@CreatedOn", LeadDate);
            SqlParam[5] = new SqlParameter("@AGE", Age);
            SqlParam[6] = new SqlParameter("@LeadCount", LeadCount);
            SqlParam[7] = new SqlParameter("@GroupCode", GroupCode);
            SqlParam[8] = new SqlParameter("@Realtime", false);
            SqlParam[9] = new SqlParameter("@LastAssignedGroupID", LastAssignedGroupID);
            SqlParam[10] = new SqlParameter("@SpecialLeadRank", SpecialLeadRank);

            SqlParam[11] = new SqlParameter("@outleadrank", DbType.Int16);
            SqlParam[11].Value = 0;
            SqlParam[11].Direction = ParameterDirection.Output;

            SqlParam[12] = new SqlParameter("@AssignedToEcode", SqlDbType.VarChar, 30)
            {
                Value = "",
                Direction = ParameterDirection.Output
            };

            SqlParam[13] = new SqlParameter("@AssignedToAgentName", SqlDbType.VarChar, 100)
            {
                Value = "",
                Direction = ParameterDirection.Output
            };

            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetHealthAgentId_NewApp]", SqlParam);
            Int64 res = Convert.ToInt64(SqlParam[1].Value == DBNull.Value || SqlParam[1].Value == null ? 0 : SqlParam[1].Value);
            OutLeadRank = Convert.ToInt16(SqlParam[11].Value == DBNull.Value || SqlParam[11].Value == null ? 0 : SqlParam[11].Value);
            AssignedToEcode = Convert.ToString(SqlParam[12].Value == DBNull.Value || SqlParam[12].Value == null ? "" : SqlParam[12].Value);
            AssignedToAgentName = Convert.ToString(SqlParam[13].Value == DBNull.Value || SqlParam[13].Value == null ? "" : SqlParam[13].Value);
            return res;
        }


        public static int AssignLead(LeadDetails _AllocationDetails)
        {
            _AllocationDetails.AllocationTrackingEntryFlag = 1;
            _AllocationDetails.SelectionCount = 0;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[18];
            SqlParam[0] = new SqlParameter("@AssignedToAgentId", _AllocationDetails.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@AssignedByAgentId", _AllocationDetails.AssignbyUserID);
            SqlParam[2] = new SqlParameter("@ProductId", _AllocationDetails.ProductID);
            SqlParam[3] = new SqlParameter("@LeadId", _AllocationDetails.LeadId);
            SqlParam[4] = new SqlParameter("@GroupId", _AllocationDetails.GroupID);
            SqlParam[5] = new SqlParameter("@Flag", _AllocationDetails.AllocationTrackingEntryFlag);
            SqlParam[6] = new SqlParameter("@JobId", _AllocationDetails.JobID);
            SqlParam[7] = new SqlParameter("@FirstSelectedPlanId", _AllocationDetails.InsurerID);
            SqlParam[8] = new SqlParameter("@SelectionCount", _AllocationDetails.SelectionCount);
            SqlParam[9] = new SqlParameter("@LeadRank", _AllocationDetails.LeadRank);
            SqlParam[10] = new SqlParameter("@AgentGrade", _AllocationDetails.AgentGrade);
            SqlParam[11] = new SqlParameter("@LeadGrade", 0);
            SqlParam[12] = new SqlParameter("@LeadScore", _AllocationDetails.LeadScore);
            SqlParam[13] = new SqlParameter("@LeadStatusId", _AllocationDetails.StatusId);
            SqlParam[14] = new SqlParameter("@LeadSubStatusId", _AllocationDetails.SubStatusId);
            SqlParam[15] = new SqlParameter("@LeadTypeId", 0);
            SqlParam[16] = new SqlParameter("@CustID", _AllocationDetails.CustomerId);
            SqlParam[17] = new SqlParameter("@AgentAssignedToEmpId", _AllocationDetails.AssignedToEmpId);
            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[Insert_AssignedToAgent_NewApp]", SqlParam);
        }

        public static int ReAssignLead(LeadDetails _AllocationDetails)
        {
            _AllocationDetails.AllocationTrackingEntryFlag = 1;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[10];
            SqlParam[0] = new SqlParameter("@AssignedTo_AgentId", _AllocationDetails.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@AssignedBy_AgentId", _AllocationDetails.AssignbyUserID);
            SqlParam[2] = new SqlParameter("@ProductId", _AllocationDetails.ProductID);
            SqlParam[3] = new SqlParameter("@LeadId", _AllocationDetails.LeadId);
            SqlParam[4] = new SqlParameter("@GroupId", _AllocationDetails.GroupID);
            SqlParam[5] = new SqlParameter("@Flag", _AllocationDetails.AllocationTrackingEntryFlag);
            SqlParam[6] = new SqlParameter("@JobId", _AllocationDetails.JobID);
            SqlParam[7] = new SqlParameter("@FirstSelectedPlanId", _AllocationDetails.InsurerID);
            SqlParam[8] = new SqlParameter("@SelectionCount", _AllocationDetails.SelectionCount);
            SqlParam[9] = new SqlParameter("@LeadRank", _AllocationDetails.LeadRank);
            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CRM].[Insert_AssignedToAgent]", SqlParam);
        }

        public static DataSet GetAgentdetails(Int64 UserID)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@UserID", UserID);
            string strQuery = "SELECT ISNULL(Grade,0) AS Grade,GroupId,AgentCode FROM MTX.GetAgents_Health (NOLOCK) WHERE AgentID=@UserID";
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
        }

        public static Int16 UpdateAgentAllocationCounter(Int64 LeadID, Int64 UserID, Int16 ProductID, Int16 LeadRank, string GroupCode)
        {
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[5];
                SqlParam[0] = new SqlParameter("@LeadID", LeadID);
                SqlParam[1] = new SqlParameter("@UserID", UserID);
                SqlParam[2] = new SqlParameter("@ProductID", ProductID);
                SqlParam[3] = new SqlParameter("@LeadRank", LeadRank);
                SqlParam[4] = new SqlParameter("@Groupcode", GroupCode);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateAgentDailyAllocationCounter]", SqlParam);
                return 1;
            }
            catch (Exception)
            {
                return 0;
            }
        }
        public static Int16 UpDateProcessedLead(LeadDetails lead, Byte BULeadRank, bool IsAI, bool IsAssigned)
        {
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@LeadID", lead.LeadId);
                SqlParam[1] = new SqlParameter("@LeadRank", lead.LeadRank);
                SqlParam[2] = new SqlParameter("@BULeadRank", BULeadRank);
                SqlParam[3] = new SqlParameter("@LastLeadRank", lead.LastLeadRank);
                SqlParam[4] = new SqlParameter("@IsAI", IsAI);
                SqlParam[5] = new SqlParameter("@IsAssigned", IsAssigned);
                SqlParam[6] = new SqlParameter("@LeadScore", lead.LeadScore);
                SqlParam[7] = new SqlParameter("@LeadGrade", lead.LeadGrade);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateLeadAssignmentProcess]", SqlParam);
                return 1;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public static Int16 UpdateSchedulerendtime(Int16 ProductID)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            string StrQuery = "UPDATE MTX.NWHLogic_Allocation SET LastJobRun=GETDATE() WHERE productid=@ProductID";
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ProductID", ProductID);
            var Jobcount = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, StrQuery, SqlParam);
            return Convert.ToInt16(Jobcount);
        }

        public static DataSet GetAssignedAgentdetails(Int64 UserID)
        {

            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@UserID", UserID);
            string strQuery = "SELECT TOP 1 0 Grade,ISNULL(GroupId,0) AS GroupId,'' AS AgentCode FROM CRM.Usergrouprolemapnew (NOLOCK) WHERE userid=@UserID";
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
        }

        public static long GetLeadAssignedAgent(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            object data = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadAssignedAgent]", SqlParam);
            long assignedToUserId = (data == DBNull.Value || data == null) ? 0 : (long)data;
            return assignedToUserId;
        }

        public static void USP_UpdateAgentGrade_Auto(short productId, short groupId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@ProductID", productId);
            SqlParam[1] = new SqlParameter("@GroupID", groupId);
            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[USP_UpdateAgentGrade_Auto]", SqlParam);
        }


        public static DataTable GetCustomerFeatureData(long CustomerId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@CustomerId", CustomerId);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerFeatureData]", SqlParam);


            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0) {
                return ds.Tables[0];
            }

            return null;
        }

        public static DataSet PopulateAssignmentDetailsForCTC(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", lead.LeadId);
            SqlParam[1] = new SqlParameter("@Country", lead.Country);
            SqlParam[2] = new SqlParameter("@StateID", lead.StateID);
            SqlParam[3] = new SqlParameter("@LeadSource", lead.LeadSource);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CTC_OB_AssignmentData_Allocation]", SqlParam);
        }

        #endregion

        #region Zone Data
        public static List<CityZone> GetAllZonesFromMongo()
        {
            List<CityZone> FOSZones = new();
            string Key = $"{RedisCollection.FOSZones()}";

            if (MemoryCache.Default[Key] != null)
                FOSZones = (List<CityZone>)MemoryCache.Default.Get(Key);
            else
            {
                MongoHelper objCommDB = new(SingletonClass.OneLeadDB());
                FOSZones = objCommDB.GetDocuments<CityZone>(null, Key);

                if (FOSZones.Count > 0)
                    CommonCache.GetOrInsertIntoCache(FOSZones, Key, 8 * 60);

            }

            return FOSZones;
        }

        public static DataSet GetCityList()
        {

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[Master].[GetCityList]");

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static Int16 ResetAgentBatchAssignlimit()
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            string StrQuery = "UPDATE	[MTX].[GetAgents_Health] SET  BatchAssignLead=0";
            SqlParameter[] SqlParam = new SqlParameter[0];
            var Jobcount = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, StrQuery, SqlParam);
            return Convert.ToInt16(Jobcount);
        }

        public static DataSet GetPredectiveLeadsRejection(int day, string SPName)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@nthDays ", day);
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, SPName, SqlParam);

        }

        public static DataSet RejectLeads(Int64 LeadID, Int32 StatusId, Int32 ProductId, string Reason)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[1] = new SqlParameter("@StatusId", StatusId);
            SqlParam[2] = new SqlParameter("@ProductId", ProductId);
            SqlParam[3] = new SqlParameter("@Reason", Reason);
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[RejectLeads]", SqlParam);

        }

        public static Dictionary<string, NriCityTimeZone> GetCityTimeZone()
        {
            try
            {
                Dictionary<string, NriCityTimeZone> lstCityZones;
                ObjectCache memcache = MemoryCache.Default;
                lstCityZones = (Dictionary<string, NriCityTimeZone>)memcache.Get("NriCityZones");

                if (lstCityZones == null)
                {
                    DateTime Requestdate = DateTime.Now;
                    lstCityZones = new();
                    CacheItemPolicy objCachePolicies = new()
                    {
                        AbsoluteExpiration =
                            new DateTimeOffset(
                                DateTime.UtcNow.AddHours(24))
                    };

                    SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                    DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetNriCityTimeZones]", SqlParam);

                    if (ds != null && ds.Tables.Count > 0)
                    {
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            NriCityTimeZone zone = new()
                            {
                                NriCity = (row["NriCity"] != DBNull.Value && row["NriCity"] != null) ? row["NriCity"].ToString() : string.Empty,
                                StartTime = TimeSpan.Parse(row["StartTime"].ToString()),
                                EndTime = TimeSpan.Parse(row["EndTime"].ToString()),
                                TimeZone = (row["TimeZone"] != DBNull.Value && row["TimeZone"] != null) ? row["TimeZone"].ToString() : string.Empty,
                                TimeDiffFromIST = (row["TimeDiffFromIST"] != DBNull.Value && row["TimeDiffFromIST"] != null) ? Convert.ToString(row["TimeDiffFromIST"]) : "+00:00"
                            };
                            lstCityZones.TryAdd(zone.NriCity, zone);
                        }
                    }

                    //Add into cache start
                    memcache.Add("NriCityZones", lstCityZones, objCachePolicies);
                    
                    //Add into cache end
                    return lstCityZones;
                }
                else
                    return lstCityZones;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static Dictionary<short, short> GetLeadRankMasterMinRankMap()
        {
            try
            {
                Dictionary<short, short> rankMap;
                ObjectCache memcache = MemoryCache.Default;
                rankMap = (Dictionary<short, short>)memcache.Get("LeadRankMasterMinRankMap_Health");

                if (rankMap == null)
                {
                    rankMap = new Dictionary<short, short>();
                    CacheItemPolicy objCachePolicies = new()
                    {
                        AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow.AddHours(24))
                    };

                    SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                    DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetMinRankMap_HealthAllocation]", SqlParam);

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            short LeadRankCode = (row["LeadRankCode"] != DBNull.Value && row["LeadRankCode"] != null) ? Convert.ToInt16(row["LeadRankCode"]) : Convert.ToInt16(0);
                            short minRank = (row["MinRank"] != DBNull.Value && row["MinRank"] != null) ? Convert.ToInt16(row["MinRank"]) : Convert.ToInt16(0);
                            rankMap.TryAdd(LeadRankCode, minRank);
                        }

                        memcache.Add("LeadRankMasterMinRankMap_Health", rankMap, objCachePolicies);
                    }
                    
                    return rankMap;
                }
                else
                    return rankMap;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        #endregion

        #region RuleEngine
        public static List<Engine> GetAllEnginesByProduct(int ProductId)
        {
            try
            {
                List<Engine> engines = new();
                string Key = $"{RedisCollection.LeadRankRE_Engine()}_{ProductId}";

                if (MemoryCache.Default[Key] != null)
                    engines = (List<Engine>)MemoryCache.Default.Get(Key);
                else
                {
                    MongoHelperV2 OneLeadDb = new(SingletonClass.OneLeadDBV2());

                    var filter = Builders<Engine>.Filter.Eq(engine => engine.ProductId, ProductId) & Builders<Engine>.Filter.Eq(engine => engine.IsActive, 1);

                    engines = OneLeadDb.GetDocuments<Engine>(filter, MongoCollection.LeadRankRE_Engines());
                    if (engines.Count > 0)
                        CommonCache.GetOrInsertIntoCache(engines, Key, 1 * 60);
                }
                return engines;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static List<AllocationRule> GetRulesForEngine(string engineName, int productId)
        {
            List<Engine> engineList = GetAllEnginesByProduct(productId);
            Engine engine = engineList.Find((engine) => engine.EngineName == engineName);

            List<AllocationRule> rules = new();
            List<AllocationRule> SortedRules = new();
            if (engine == null)
            {
                return SortedRules;
            }
            string Key = $"{RedisCollection.LeadRankRE_Rules()}:{engine._id}";

            if (MemoryCache.Default[Key] != null)
                SortedRules = (List<AllocationRule>)MemoryCache.Default.Get(Key);
            else
            {
                MongoHelperV2 OneLeadDb = new(SingletonClass.OneLeadDBV2());
                var RuleFilter =
                    Builders<AllocationRule>.Filter.In("_id", engine.Rules) & Builders<AllocationRule>.Filter.Eq(rule => rule.IsActive, 1);

                rules = OneLeadDb.GetDocuments(RuleFilter, MongoCollection.LeadRankRE_Rules());

                // TODO: optimize sorting, sorting based on rule sequence
                engine.Rules.ForEach((ruleId) =>
                {
                    var matchingRule = rules.Find(rule => rule._id == ruleId);
                    if (matchingRule != null)
                    {
                        SortedRules.Add(matchingRule);
                    }
                });

                LoadConditionsForRules(SortedRules);

                if (SortedRules.Count > 0)
                    CommonCache.GetOrInsertIntoCache(SortedRules, Key, 1 * 60);
            }



            return SortedRules;
        }

        private static void LoadConditionsForRules(List<AllocationRule> rules)
        {
            Dictionary<ObjectId, List<RuleCondition>> RuleConditions = new();


            MongoHelperV2 OneLeadDb = new(SingletonClass.OneLeadDBV2());

            var conditionFilter = Builders<RuleCondition>.Filter.In("Rule", rules.Select(rule => rule._id))
            & Builders<RuleCondition>.Filter.Eq(condition => condition.IsActive, 1);

            var Conditions = OneLeadDb.GetDocuments(conditionFilter, MongoCollection.LeadRankRE_Conditions());

            Conditions.ForEach((Condition) =>
            {
                if (!RuleConditions.ContainsKey(Condition.Rule))
                {
                    RuleConditions.Add(Condition.Rule, new List<RuleCondition>());
                }
                RuleConditions[Condition.Rule].Add(Condition);
            });

            rules.ForEach(rule =>
            {
                if (RuleConditions.ContainsKey(rule._id))
                {
                    rule.Conditions = RuleConditions.GetValueOrDefault(rule._id);
                }
            });


        }

        public static dynamic GetSampleLeadsForPayuScore()
        {
            try
            { 
                MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());

                return OneLeadDb.GetDocuments<dynamic>(null, "PayuRenewalDataSample");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetSampleLeadsForPayuScore: {ex.Message}");
                return null;
            }
        }

        public static dynamic GetSampleLeadsForPayuIncome()
        {
            try
            { 
                MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());

                return OneLeadDb.GetDocuments<dynamic>(null, "PayuIncomeDataSample");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetSampleLeadsForPayuIncome: {ex.Message}");
                return null;
            }  
        }

        public static DataSet GetCustDetails(string CustId, string LeadId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@CustId", Convert.ToInt64(CustId));
                sqlParam[1] = new SqlParameter("@LeadId", Convert.ToInt64(LeadId));
                ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerDetails]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }

        public static DataSet GetAdditionalSMEData(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[10];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[1] = new SqlParameter("@CustomerId", lead.CustomerId);
            SqlParam[2] = new SqlParameter("@CreatedOn", lead.CreatedOn);
            SqlParam[3] = new SqlParameter("@IsChurn", lead.IsChurn);
            SqlParam[4] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[5] = new SqlParameter("@InvestmentTypeID", lead.InvestmentTypeID);
            SqlParam[6] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[7] = new SqlParameter("@GroupID", lead.GroupID);
            SqlParam[8] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[9] = new SqlParameter("@EmployeeID", lead.EmployeeID);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAdditionaldData_SMEAllocation]", SqlParam);
        }

        public static void UpdateAgentsDataForAllocation()
        {
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateAgents_SMEAllocation]", null);
        }

        public static void UpdateAgentGrade()
        {
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateAgentGrade_SMEAllocation]", null);
        }

        public static DataSet GetAgentAllocation(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[16];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[1] = new SqlParameter("@CustID", lead.CustomerId);
            SqlParam[2] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[3] = new SqlParameter("@UserID", lead.UserID);
            SqlParam[4] = new SqlParameter("@GroupId", lead.GroupID);
            SqlParam[5] = new SqlParameter("@AssignedGroupId", lead.GroupID);
            SqlParam[6] = new SqlParameter("@AgentGrade", lead.AgentGrade);
            SqlParam[7] = new SqlParameter("@IsAllocable", lead.IsAllocable);
            SqlParam[8] = new SqlParameter("@IsChurn", lead.IsChurn);
            SqlParam[9] = new SqlParameter("@EmployeeID", lead.EmployeeID);
            SqlParam[10] = new SqlParameter("@JobID", lead.JobID);
            SqlParam[11] = new SqlParameter("@InvestID", lead.InvestmentTypeID);
            SqlParam[12] = new SqlParameter("@UtmSource", lead.Utm_source);
            SqlParam[13] = new SqlParameter("@TypeOfPolicy", lead.TypeOfPolicy);
            SqlParam[14] = new SqlParameter("@LeadGrade", lead.LeadGrade);
            SqlParam[15] = new SqlParameter("@LeadSource", lead.LeadSource);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentFor_SMEAllocation]", SqlParam);
        }

        public static void AssignSMELead(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[20];
            SqlParam[0] = new SqlParameter("@LeadId", lead.LeadId);
            SqlParam[1] = new SqlParameter("@CustID", lead.CustomerId);
            SqlParam[2] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[3] = new SqlParameter("@UserID", lead.UserID);
            SqlParam[4] = new SqlParameter("@GroupId", lead.GroupID);
            SqlParam[5] = new SqlParameter("@AssignedGroupId", lead.GroupID);
            SqlParam[6] = new SqlParameter("@AgentGrade", lead.AgentGrade);
            SqlParam[7] = new SqlParameter("@IsChurn", lead.IsChurn);
            SqlParam[8] = new SqlParameter("@JobID", lead.JobID);
            SqlParam[9] = new SqlParameter("@InvestID", lead.InvestmentTypeID);
            SqlParam[10] = new SqlParameter("@UtmSource", lead.Utm_source);
            SqlParam[11] = new SqlParameter("@LeadStatusId", lead.StatusId);
            SqlParam[12] = new SqlParameter("@LeadSubStatusId", lead.SubStatusId);
            SqlParam[13] = new SqlParameter("@LeadTypeId", 0);
            SqlParam[14] = new SqlParameter("@IsAgentGrading", lead.IsAgentGrading);
            SqlParam[15] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[16] = new SqlParameter("@InsurerId", lead.InsurerID);
            SqlParam[17] = new SqlParameter("@UTM_Campaign", lead.Utm_campaign);
            SqlParam[18] = new SqlParameter("@LeadScore", lead.LeadScore);
            SqlParam[19] = new SqlParameter("@EmployeeID", lead.EmployeeID);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[AssignLead_SMEAllocation]", SqlParam);
        }

        public static short GetSmeLeadRank(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[25];
            SqlParam[0] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[1] = new SqlParameter("@UtmSource", lead.Utm_source);
            SqlParam[2] = new SqlParameter("@UtmMedium", lead.UTM_Medium);
            SqlParam[3] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[4] = new SqlParameter("@Country", lead.Country);
            SqlParam[5] = new SqlParameter("@UtmTerm", lead.Utm_term);
            SqlParam[6] = new SqlParameter("@Source", lead.source);
            SqlParam[7] = new SqlParameter("@INVType", lead.InvestmentTypeID);
            SqlParam[8] = new SqlParameter("@CustomerID", lead.CustomerId);
            SqlParam[9] = new SqlParameter("@InsurerID", lead.InsurerID);
            SqlParam[10] = new SqlParameter("@IsMarinBooking", lead.IsMarineBooking);
            SqlParam[11] = new SqlParameter("@IsWorkManBooking", lead.IsWorkManBooking);
            SqlParam[12] = new SqlParameter("@CustBooking", lead.CustBooking);
            SqlParam[13] = new SqlParameter("@SA", lead.SA);
            SqlParam[14] = new SqlParameter("@TypepofPolicy", lead.TypeOfPolicy);
            SqlParam[15] = new SqlParameter("@PolicyTenure", lead.PolicyTenure);
            SqlParam[16] = new SqlParameter("@TotalNoOFLives", lead.TotalNoOfLives);
            SqlParam[17] = new SqlParameter("@IsCMB", lead.IsCMB);
            SqlParam[18] = new SqlParameter("@IsCPED", lead.IsCPED);
            SqlParam[19] = new SqlParameter("@PolicyType", lead.PolicyType);
            SqlParam[20] = new SqlParameter("@EmployeeRange", lead.EmployeeRange);
            SqlParam[21] = new SqlParameter("@TotalNoOFEmployees", lead.TotalNoOFEmployees);
            SqlParam[22] = new SqlParameter("@ContinuePQ", lead.ContinuePQ);
            SqlParam[23] = new SqlParameter("@OccupencyID", lead.OccupancyId);
            SqlParam[24] = new SqlParameter("@CustomerType", lead.CustomerType);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadRank_SMEAllocation]", SqlParam);
            if (result != null)
                return Convert.ToInt16(result);
            else
                return 0;
        }

        public static short GetAssignGroupId(LeadDetails lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[17];
            SqlParam[0] = new SqlParameter("@LeadRank", lead.LeadRank);
            SqlParam[1] = new SqlParameter("@GroupID", lead.GroupID);
            SqlParam[2] = new SqlParameter("@InvestmentTypeID", lead.InvestmentTypeID);
            SqlParam[3] = new SqlParameter("@InsurerID", lead.InsurerID);
            SqlParam[4] = new SqlParameter("@CityID", lead.CityID);
            SqlParam[5] = new SqlParameter("@Utm_source", lead.Utm_source);
            SqlParam[6] = new SqlParameter("@Utm_medium", lead.UTM_Medium);
            SqlParam[7] = new SqlParameter("@LeadSource", lead.LeadSource);
            SqlParam[8] = new SqlParameter("@Utm_term", lead.Utm_term);
            SqlParam[9] = new SqlParameter("@TypeOfPolicy", lead.TypeOfPolicy);
            SqlParam[10] = new SqlParameter("@Utm_Campaign", lead.Utm_campaign);
            SqlParam[11] = new SqlParameter("@ContinuePQ", lead.ContinuePQ);
            SqlParam[12] = new SqlParameter("@TotalNoOfLives", lead.TotalNoOfLives);
            SqlParam[13] = new SqlParameter("@CustomerType", lead.CustomerType);
            SqlParam[14] = new SqlParameter("@RolloverCheck", lead.RolloverCheck);
            SqlParam[15] = new SqlParameter("@OccupancyId", lead.OccupancyId);
            SqlParam[16] = new SqlParameter("@CPMRTO", lead.CPMRTO);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetGroupId_SMEAllocation]", SqlParam);
            if (result != null)
                return Convert.ToInt16(result);
            else
                return 0;
        }

        public static void SaveCustomerFeatureData(long customerId, string Feature, int Value)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@CustomerId", customerId);
            SqlParam[1] = new SqlParameter("@Feature", Feature);
            SqlParam[2] = new SqlParameter("@Value",Value);
            

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveCustomerFeatureData]", SqlParam);
        }

        public static DataSet GetPayUScoreRankMapping()
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetPayUScoreRankMapping]");
        }

        public static DataSet GetGroupLeadsCount()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetGroupLeadsCount_SMEAllocation]");
        }

        public static DataSet GetPIForDocTriggeredLeads()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSmePIForDocTriggeredLeads]");
        }

        public static void AddPIForDocLeadForTrigger(long leadId, string triggerName, string processName)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            SqlParam[1] = new SqlParameter("@TriggerName", triggerName);
            SqlParam[2] = new SqlParameter("@ProcessType", processName);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[AddLeadForSmeTrigger]", SqlParam);
        }

        public static void MarkTriggersInactive(string processName)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ProcessType", processName);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[MarkPRBTriggersInactive]", SqlParam);
        }
        #endregion
    }
}