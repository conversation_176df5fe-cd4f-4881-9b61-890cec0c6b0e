﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
//using ReadXmlProject;
using Helper;
using Microsoft.Extensions.Configuration;

namespace DataAccessLibrary
{
    public class MongoDBConnection
    {
        static MongoDatabase _CommunicationDB;
        static MongoDatabase _FileDB;
        static MongoDatabase _OneLeadDB;
        private MongoDBConnection()
        {

        }
        public static MongoDatabase CommunicationDB()
        {
            try
            {
                if (_CommunicationDB != null)
                {
                    return _CommunicationDB;
                }
                else
                {
                    string Enviornment = string.Empty;
                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string MongoDBCommunicationDB = con.GetSection("Communication").GetSection("MongoDBCommunicationDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("MongoDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(MongoDBCommunicationDB);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public static MongoDatabase CommFileDB()
        {
            try
            {
                if (_FileDB != null)
                {
                    return _FileDB;
                }
                else
                {
                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    string Enviornment = string.Empty;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string MongoGridFSDB = con.GetSection("Communication").GetSection("MongoGridFSDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("MongoGridFSConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(MongoGridFSDB);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        
        public static MongoDatabase OneLeadDB()
        {
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {
                    string Enviornment = string.Empty;
                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();
                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    return server.GetDatabase(OneLeadDB);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
}
