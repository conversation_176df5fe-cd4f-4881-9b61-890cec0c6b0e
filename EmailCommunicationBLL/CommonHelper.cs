﻿using DataAccessLayer;
using DataAccessLibrary;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmailCommunicationBLL
{
    internal class CommonHelper
    {
        public static bool SendCommunication(sendcommunicationResponse oSendcommunicationResponse)
        {
            bool result = false;
            string response = string.Empty;
            string Json = string.Empty;
            string strexception = string.Empty;
            string url = "pbserviceapi".AppSettings();
            ComAPIV2Response comAPIV2Response = new();
            DateTime dt = DateTime.Now;
            try
            {
                Json = JsonConvert.SerializeObject(oSendcommunicationResponse);

                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"REQUESTINGSYSTEM", "Matrix"},{"TOKEN", "pbservicetoken".AppSettings()}};
                response = CommonAPICall.CallAPI(url, Json, "POST", 3000, "application/json", header);

                if (!string.IsNullOrEmpty(response))
                {
                    comAPIV2Response = JsonConvert.DeserializeObject<ComAPIV2Response>(response);
                }
                result = true;
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {

                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(oSendcommunicationResponse.LeadId), oSendcommunicationResponse.LeadId, strexception, "SendCommunication", "Allocation", "KafkaConsumer", JsonConvert.SerializeObject(Json), response, dt, DateTime.Now);
                Customerdetaildll.TriggerCommunicationInsertSMSLead(oSendcommunicationResponse.LeadId, oSendcommunicationResponse.TriggerName, comAPIV2Response?.UUID);
            }

            return result;
        }
    }
}
