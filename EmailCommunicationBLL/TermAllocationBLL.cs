﻿using System;
using System.Collections.Generic;
using System.Data;
using DataAccessLayer;
using Newtonsoft.Json;
using PropertyLayers;
using System.Threading.Tasks;
using System.Linq;
using System.Collections;
using System.Runtime.Caching;
using Helper;


namespace EmailCommunicationBLL
{

    public class TermAllocationBLL : ITermAllocationBLL
    {
        private static readonly List<TermLeadDetails> leads = new();
        private static readonly List<TermLeadDetails> churnedLeadsForAllocation = new();
        Dictionary<long, string> LeadErrors;

        public static readonly int[] RanksExemptFromUpdate = {465,466,467,468,469,537,538,539,565,566,567,568,569,547,548,549};

        public async Task<string> LeadsAllocation_TermAsync()
        {
            short productId = 7;
            string Error = string.Empty;
            var assignment = new Assign{ reassignCount = 0, NoAgent = 0, CrmLeads = 0 };
            LeadErrors = new();
            DateTime requestTime = DateTime.Now;
            try
            {
                USP_UpdateAgentGrade_Auto(productId);
            }
            catch (Exception ex)
            {
                string error = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, error, "USP_UpdateAgentGrade_Auto", "Allocation", "TermAllocationBLL", "", "", requestTime, DateTime.Now);
            }

            try
            {
                DateTime currDate = DateTime.Now;
                DateTime startDateTime = DateTime.Now;
                
                DumpAgentsforTermAllocation(productId);
                double dumpAgentsforTermAllocation = (DateTime.Now - startDateTime).TotalMilliseconds;


                // Fetch Leads for Allocation
                startDateTime = DateTime.Now;
                GetTermAllocationLeads(leads);
                double getTermAllocationLeads = (DateTime.Now - startDateTime).TotalMilliseconds;

                await PopulateDataAndAllocateLead(leads, assignment);

                startDateTime = DateTime.Now;
                GetCTCndChurnLeads();
                double getCTCndChurnLeads = (DateTime.Now - startDateTime).TotalMilliseconds;

                await PopulateDataAndAllocateLead(churnedLeadsForAllocation, assignment);

                Console.WriteLine("TermMethodExecutionTime - DumpAgentsforTermAllocation: "+ dumpAgentsforTermAllocation.ToString()+"---GetTermAllocationLeads: "+ getTermAllocationLeads.ToString()+"---GetCTCndChurnLeads: "+getCTCndChurnLeads.ToString());

                PurgeAssignedLeads();
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, Error, "GetTermAllocationRanks", "Allocation", "TermAllocationBLL", "", "", requestTime, DateTime.Now);
                if (LeadErrors.Count > 0)
                {
                    try
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(null, 0, JsonConvert.SerializeObject(LeadErrors), "Term Error leads", "Allocation", "TermAllocationBLL", "", "", DateTime.Now, DateTime.Now);
                    }
                    catch (Exception) { }
                }

            }

            return "Fin";
        }

        private static void USP_UpdateAgentGrade_Auto(short productId, short groupId = 0)
        {
            string key = "UpdateAgentGradeRunDateTime";
            DateTime dt = DateTime.Now.AddDays(-1);

            if (MemoryCache.Default[key] != null)
            {
                ObjectCache CacheConfig = MemoryCache.Default;
                dt = (DateTime)CacheConfig.Get(key);
            } else {
                CommonCache.GetOrInsertIntoCache(DateTime.Now, key, 24 * 60);
            }

            if (DateTime.Now.Hour == 10 && DateTime.Now.Minute >= 28 && dt.Date != DateTime.Now.Date)
            {
                TermAllocationDLL.USP_UpdateAgentGrade_Auto(productId, groupId);
                CommonCache.GetOrInsertIntoCache(DateTime.Now, key, 24 * 60);

                Console.WriteLine("Term - USP_UpdateAgentGrade_AutoRun" + DateTime.Now.ToString());

            }
        }

        private static void DumpAgentsforTermAllocation(short ProductId)
        {
            TermAllocationDLL.DumpAgentsforTermAllocation(ProductId);
        }

        private static void GetTermAllocationLeads(List<TermLeadDetails> leadList, long LeadId = 0)
        {
            DataSet leadsDataSet = TermAllocationDLL.GetTermAllocationLeads(LeadId);

            if (leadsDataSet != null && leadsDataSet.Tables[0].Rows.Count != 0)
            {
                leadList.Clear();
                foreach (DataRow leadRow in leadsDataSet.Tables[0].Rows)
                {
                    TermLeadDetails leadData = new()
                    {
                        _LEADTYPE = LEADTYPE.NORMAL,
                        LeadId = Convert.ToInt64(leadRow["LeadID"]),
                        CustomerId = leadRow["CustomerID"] == null || leadRow["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(leadRow["CustomerID"]),
                        AnnualIncome = leadRow["AnnualIncome"] == DBNull.Value ? 0 : Convert.ToInt64(leadRow["AnnualIncome"]),
                        CityID = leadRow["CityID"] == null || leadRow["CityID"] == DBNull.Value ? (short)0 : Convert.ToInt16(leadRow["CityID"]),
                        StatusId = leadRow["StatusID"] == null || leadRow["StatusID"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(leadRow["StatusID"]),
                        SubStatusId = leadRow["SubStatusID"] == null || leadRow["SubStatusID"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["SubStatusID"]),
                        LeadSource = leadRow["LeadSource"] == DBNull.Value ? string.Empty : leadRow["LeadSource"].ToString(),
                        Utm_source = leadRow["Utm_source"] == DBNull.Value ? string.Empty : leadRow["Utm_source"].ToString(),
                        UTM_Medium = leadRow["UTM_Medium"] == DBNull.Value ? string.Empty : leadRow["UTM_Medium"].ToString(),
                        Utm_campaign = leadRow["Utm_campaign"] == DBNull.Value ? string.Empty : leadRow["Utm_campaign"].ToString(),
                        StateID = leadRow["StateID"] == null || leadRow["StateID"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(leadRow["StateID"]),
                        source = leadRow["Source"] == DBNull.Value ? string.Empty : leadRow["Source"].ToString(),
                        CreatedOn = leadRow["CreatedOn"] == null || leadRow["CreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["CreatedOn"]),
                        Country = leadRow["Country"] == null || leadRow["Country"] == DBNull.Value ? string.Empty : leadRow["Country"].ToString(),
                        ProductID = leadRow["ProductID"] == null || leadRow["ProductID"] == DBNull.Value ? (short)0 : Convert.ToInt16(leadRow["ProductID"]),
                        LeadGrade = leadRow["LeadGrade"] != null && leadRow["LeadGrade"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadGrade"]) : (short)0,
                        MobileNo = leadRow["MobileNo"] != null && leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : "",
                        Age = leadRow["Age"] != null && leadRow["Age"] != DBNull.Value ? Convert.ToInt32(leadRow["Age"]) : 0,
                        DOB = leadRow["DOB"] != null && leadRow["DOB"] != DBNull.Value ? Convert.ToDateTime(leadRow["DOB"]) : DateTime.MinValue,
                        UtmContent = leadRow["utm_content"] == DBNull.Value ? string.Empty : Convert.ToString(leadRow["utm_content"]),
                        Name = leadRow["Name"] == DBNull.Value ? string.Empty : Convert.ToString(leadRow["Name"]),
                        Utm_term = leadRow["Utm_term"] == DBNull.Value ? string.Empty : Convert.ToString(leadRow["Utm_term"]),
                        ChatStatus = leadRow["ChatStatus"] != null && leadRow["ChatStatus"] != DBNull.Value ? Convert.ToByte(leadRow["ChatStatus"]) : Convert.ToByte(0),
                        ExistingLeadScore = leadRow["LeadScore"] != null && leadRow["LeadScore"] != DBNull.Value ? Convert.ToDecimal(leadRow["LeadScore"]) : Convert.ToDecimal(0),
                        AddOnParentID = leadRow["AddOnParentID"] == null || leadRow["AddOnParentID"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(leadRow["AddOnParentID"]),
                        InvestmentTypeID = leadRow["InvestmentTypeID"] == DBNull.Value || leadRow["InvestmentTypeID"] == null ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["InvestmentTypeID"]),
                        PostCode = leadRow["PostCode"] == DBNull.Value || leadRow["PostCode"] == null ? 0 : Convert.ToInt64(leadRow["PostCode"]),
                        VisitCount = leadRow["VisitCount"] == DBNull.Value || leadRow["VisitCount"] == null ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["VisitCount"]),
                    };
                    leadList.Add(leadData);
                    // var idx = IsLeadDataAvailable(leadData, leadList);

                    // if (idx < 0)
                    // {
                    //     UpdateLeadDataState(leadData, DataStatus.DataPending);
                    //     leadList.Insert(~idx, leadData);
                    // }
                }
            }
        }

        private async Task PopulateDataAndAllocateLead(List<TermLeadDetails> leadList, Assign assignment)
        {
            double populateLeadAllocationKeyFactors = 0;
            double populateLeadTermDetails = 0;
            double populatePayUIncome = 0;
            double populateLeadRank = 0;
            double populateLeadScore = 0;
            double populateGroupCode = 0;
            double rankLeadByLeadScore = 0;
            double populateAdditionalDataUpdate  = 0;
            double chkLeadAssignedinMultiproduct  = 0;
            double getAgentID_TermAllocation  = 0;
            double allocateLead = 0;
            double dumpTermLeadDetails = 0;

            foreach (TermLeadDetails lead in leadList)
            {
                try
                {
                    DateTime startDateTime = DateTime.Now;
                    if (lead.DataState == DataStatus.LeadAssigned)
                    {
                        continue;
                    }
                    if (lead._LEADTYPE != LEADTYPE.CHURN)
                    {
                        PopulateLeadAllocationKeyFactors(lead);
                        populateLeadAllocationKeyFactors += (DateTime.Now - startDateTime).TotalMilliseconds;

                        startDateTime = DateTime.Now;
                        PopulateLeadTermDetails(lead);
                        populateLeadTermDetails += (DateTime.Now - startDateTime).TotalMilliseconds;

                        if (RanksExemptFromUpdate.Contains(lead.LeadGrade))
                        {
                            lead.LeadRank = lead.LeadGrade;

                            if (lead.AssignmentProcess == "CTC")
                            {
                                lead.IsAllocable = false;
                            }
                        }
                        else
                        {

                            startDateTime = DateTime.Now;
                            PopulatePayUIncome(lead);
                            populatePayUIncome += (DateTime.Now - startDateTime).TotalMilliseconds;

                            startDateTime = DateTime.Now;
                            PopulateLeadRank(lead);

                            populateLeadRank += (DateTime.Now - startDateTime).TotalMilliseconds;

                            // PopulateLeadRankV2(lead);

                            startDateTime = DateTime.Now;
                            PopulateLeadScore(lead);
                            populateLeadScore += (DateTime.Now - startDateTime).TotalMilliseconds;

                            startDateTime = DateTime.Now;
                            PopulateGroupCode(lead);
                            populateGroupCode += (DateTime.Now - startDateTime).TotalMilliseconds;

                            startDateTime = DateTime.Now;
                            RankLeadByLeadScore(lead);
                            rankLeadByLeadScore += (DateTime.Now - startDateTime).TotalMilliseconds;

                            startDateTime = DateTime.Now;
                            PopulateAdditionalDataUpdate(lead);
                            populateAdditionalDataUpdate += (DateTime.Now - startDateTime).TotalMilliseconds;
                        }

                        if (lead.AssignmentProcess != "CTC")
                        {
                            startDateTime = DateTime.Now;
                            ChkLeadAssignedinMultiproduct(lead);
                            chkLeadAssignedinMultiproduct += (DateTime.Now - startDateTime).TotalMilliseconds; 
                        }
                    }
                    
                    if(lead.IsAllocable)
                    {
                        startDateTime = DateTime.Now;
                        GetAgentID_TermAllocation(lead, assignment);
                        getAgentID_TermAllocation += (DateTime.Now - startDateTime).TotalMilliseconds;

                        startDateTime = DateTime.Now;
                        AllocateLead(lead, assignment);
                        allocateLead += (DateTime.Now - startDateTime).TotalMilliseconds;
                    }

                    // DUMP LEAD DATA, Remove
                    startDateTime = DateTime.Now;
                    TermAllocationDLL.DumpTermLeadDetails(lead);
                    dumpTermLeadDetails += (DateTime.Now - startDateTime).TotalMilliseconds;

                }
                catch (Exception ex)
                {
                    var err = ex.ToString();
                    LeadErrors.TryAdd(lead.LeadId, err);
                }
            }
            
            Console.WriteLine("TermMethodExecutionTime - Records Count: "+ leadList.Count.ToString()+"---==---PopulateLeadAllocationKeyFactors: "+ populateLeadAllocationKeyFactors.ToString()+"---PopulateLeadTermDetails: "+ populateLeadTermDetails.ToString()+"---PopulatePayUIncome: "+ populatePayUIncome.ToString()+"---PopulateLeadRank: "+ populateLeadRank.ToString()+"---PopulateLeadScore: "+ populateLeadScore.ToString()+"---PopulateGroupCode: "+ populateGroupCode.ToString()+"---RankLeadByLeadScore: "+ rankLeadByLeadScore.ToString()+"---PopulateAdditionalDataUpdate: "+ populateAdditionalDataUpdate.ToString()+"---ChkLeadAssignedinMultiproduct: "+ chkLeadAssignedinMultiproduct.ToString()+"---GetAgentID_TermAllocation: "+ getAgentID_TermAllocation.ToString()+"---AllocateLead: "+ allocateLead.ToString()+"---DumpTermLeadDetails: "+dumpTermLeadDetails.ToString());
        }

        private static void PopulateLeadAllocationKeyFactors(TermLeadDetails leadData)
        {
            var KFAdataset = TermAllocationDLL.PopulateLeadAllocationKeyFactors(leadData.LeadId);
            if (KFAdataset != null)
            {
                var leadKFA = KFAdataset.Tables[0].Rows[0];

                leadData.InsurerID = leadKFA["InsurerID"] == null || leadKFA["InsurerID"] == DBNull.Value ? 0 : Convert.ToInt32(leadKFA["InsurerID"]);
                leadData.PreviousBooking = leadKFA["PreviousBooking"] == null || leadKFA["PreviousBooking"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(leadKFA["PreviousBooking"]);
                leadData.RepeatCustomer = leadKFA["RepeatCustomer"] == null || leadKFA["RepeatCustomer"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(leadKFA["RepeatCustomer"]);
                leadData.TwowheelerBookingCount = leadKFA["TwowheelerBookingCount"] == null || leadKFA["TwowheelerBookingCount"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(leadKFA["TwowheelerBookingCount"]);
                leadData.SourcePage = leadKFA["SourcePage"] == null || leadKFA["SourcePage"] == DBNull.Value ? string.Empty : Convert.ToString(leadKFA["SourcePage"]);
            }
        }

        private static void PopulateLeadTermDetails(TermLeadDetails leadData)
        {
            var termData = TermAllocationDLL.PopulateLeadTermDetails(leadData.LeadId);
            if (termData != null)
            {
                var TDRow = termData.Tables[0].Rows[0];

                // TermDetails
                leadData.TotalPayout = TDRow["TotalPayout"] == DBNull.Value ? 0 : Convert.ToInt32(TDRow["TotalPayout"]);
                leadData.educationQualificationId = TDRow["educationQualificationId"] == DBNull.Value ? null : Convert.ToInt16(TDRow["educationQualificationId"]);
                leadData.ProfessionType = TDRow["ProfessionType"] == DBNull.Value ? string.Empty : Convert.ToString(TDRow["ProfessionType"]);
                leadData.CreditScore = TDRow["CreditScore"] == DBNull.Value ? string.Empty : Convert.ToString(TDRow["CreditScore"]);
                leadData.IsTobaccoUser = TDRow["IsTobaccoUser"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(TDRow["IsTobaccoUser"]);
                leadData.LanguageRegionID = TDRow["LanguageRegionID"] == DBNull.Value ? 0 : Convert.ToInt32(TDRow["LanguageRegionID"]);
                leadData.brandName = TDRow["brandName"] == DBNull.Value ? string.Empty : Convert.ToString(TDRow["brandName"]);
                leadData.IsSAChanged = TDRow["IsSAChanged"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(TDRow["IsSAChanged"]);
                leadData.SubProductTypeId = TDRow["SubProductTypeId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(TDRow["SubProductTypeId"]);
                leadData.CityUpdatedBy = TDRow["CityUpdatedBy"] == DBNull.Value ? 0 : Convert.ToInt64(TDRow["CityUpdatedBy"]);
                leadData.IsDerivedIncome = TDRow["IsDerivedIncome"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(TDRow["IsDerivedIncome"]);

                // LeadAction
                leadData.TermCompare = TDRow["TermCompare"] == null || TDRow["TermCompare"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(TDRow["TermCompare"]);
                leadData.LimitedPay = TDRow["LimitedPay"] == null || TDRow["LimitedPay"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(TDRow["LimitedPay"]);
                leadData.TropSelected = TDRow["TropSelected"] == null || TDRow["TropSelected"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(TDRow["TropSelected"]);
            }
        }

        private static void PopulatePayUIncome(TermLeadDetails lead)
        {
            if (lead.AnnualIncome == 0)
            {
                try {
                    bool IsNRI = false;
                    if(!new[] {"392","91","999","INDIA","0",""}.Contains(lead.Country) || lead.InvestmentTypeID == 15 || (!string.IsNullOrEmpty(lead.Utm_source) && lead.Utm_source.ToLower() == "facebook" && !string.IsNullOrEmpty(lead.Utm_campaign) && CoreCommonMethods.Like(lead.Utm_campaign,"%NRI%")))
                    {
                        IsNRI = true;
                    }
                    
                    DataSet payUIncomeDataset = TermAllocationDLL.GetTermLeadsPayUIncome(lead.CustomerId);

                    if (payUIncomeDataset != null)
                    {
                        var ds = payUIncomeDataset.Tables[0].Rows[0];

                        lead.AnnualIncome = ds["AnnualIncome"] == null || ds["AnnualIncome"] == DBNull.Value ? 0 : Convert.ToInt64(ds["AnnualIncome"]);

                        if(lead.AnnualIncome == 0 && !string.IsNullOrEmpty(lead.brandName) && lead.brandName.ToLower() == "apple")
                        {
                            if(IsNRI) {
                                lead.AnnualIncome = 2000000;
                            } else {
                                lead.AnnualIncome = 499999;
                            }
                           
                            TermPayURanksDLL.Insert2LacLeadsPushtoTerm(lead.LeadId, lead.AnnualIncome, lead.CustomerId, "apple");
                        }
                    } 
                    else {
                        string DerivedFrom = "NA";
                        long PredictedIncome = TermPayURanksBLL.GetIncomeFromPayUAPI(lead.MobileNo, "");

                        if (PredictedIncome > 0)
                        {
                            lead.AnnualIncome = PredictedIncome*12;
                            DerivedFrom = "PayU";
                        }
                        else {
                            string brandName = string.IsNullOrEmpty(lead.brandName) ? string.Empty : lead.brandName.ToLower();

                            if(brandName == "apple")
                            {
                                if(IsNRI) { 
                                    lead.AnnualIncome = 2000000; 
                                } else { 
                                    lead.AnnualIncome = 499999; 
                                }
                                DerivedFrom = "apple";
                            }
                        }
                        TermPayURanksDLL.Insert2LacLeadsPushtoTerm(lead.LeadId, lead.AnnualIncome, lead.CustomerId, DerivedFrom);

                        TermPayURanksDLL.UpdatePayUIncomeTerm(lead.LeadId, lead.AnnualIncome);

                    }
                } catch (Exception ex)
                {
                    string error = ex.ToString();
                    LoggingHelper.LoggingHelper.AddloginQueue(null, lead.LeadId, error, "PopulatePayUIncome", "Allocation", "TermAllocationBLL", "", "", DateTime.Now, DateTime.Now);
                }
            }
        }

        private static void PopulateLeadRank(TermLeadDetails lead)
        {
            short LeadRank = TermAllocationDLL.PopulateLeadRank(lead);
            lead.LeadRank = LeadRank;
            lead.TempLeadRank = LeadRank;
        }

        private static void PopulateLeadRankV2(TermLeadDetails lead)
        {
            short SpecialLeadRank = TermAllocationDLL.PopulateLeadRankV2(lead);
            lead.SpecialLeadRank = SpecialLeadRank;
        }

        public static void PopulateLeadScore(TermLeadDetails lead)
        {
            decimal LeadScore = TermAllocationDLL.PopulateLeadScore(lead);
            lead.LeadScore = LeadScore;
        }

        public static void PopulateGroupCode(TermLeadDetails lead)
        {
            string GroupCode = "";
            string utm_source = string.IsNullOrEmpty(lead.Utm_source) ? string.Empty : lead.Utm_source.ToLower();
            string utm_medium = string.IsNullOrEmpty(lead.UTM_Medium) ? string.Empty : lead.UTM_Medium.ToLower();
            string utm_campaign = string.IsNullOrEmpty(lead.Utm_campaign) ? string.Empty : lead.Utm_campaign.ToLower();
            string lead_source = string.IsNullOrEmpty(lead.LeadSource) ? string.Empty : lead.LeadSource.ToLower();

            if (CoreCommonMethods.Like(utm_source, "%pbuae_offline_affiliate%")) {
                GroupCode = "PBUAE_OfflineAffiliate"; 
            }
            else if (utm_source == "offlineaffiliate" && utm_medium == "unassisted" && utm_campaign == "posp") {
                GroupCode = "PBP_VRM_Influencer_Term";
            }
            else if (utm_source == "offlineaffiliate" || new[] {"acaff","acaffapp"}.Contains(lead_source)) {
                GroupCode = "PARTNERSHIPS";
            }
            else if (lead_source == "crosssell" && utm_source != "termonmotor" && utm_campaign != "termonmotor") {
                GroupCode = "";
            }
            else if (utm_source == "term_nri_mkt_base") {
                GroupCode = "Super Sellers";
            }
            else if (lead.source == "1" && utm_source == "crmsms_full_journey" && CoreCommonMethods.Like(utm_campaign,"%2w_mt1bike%")) {
                GroupCode = "CRM_Term_Experitment";
            }
            else if (CoreCommonMethods.Like(utm_campaign, "%pnb_sat%") && 1 == ( lead.educationQualificationId == 4 || (lead.AnnualIncome < 300000 && !string.IsNullOrEmpty(lead.ProfessionType) && lead.ProfessionType.ToLower() == "salaried") || (lead.AnnualIncome < 400000 && lead.ProfessionType.ToLower() == "self-employed") ? 0 : 1 )) {
                GroupCode = "RetainerswithPNB";
            }
            // else if (CoreCommonMethods.Like(utm_campaign, "%bajaj_sat%") && 1 == ( lead.educationQualificationId == 4 || (lead.AnnualIncome < 300000 && !string.IsNullOrEmpty(lead.ProfessionType) && lead.ProfessionType.ToLower() == "salaried") || (lead.AnnualIncome < 400000 && lead.ProfessionType.ToLower() == "self-employed") ? 0 : 1 )) {
            //     GroupCode = "Retainers on Bajaj";
            // }
            else if (utm_source == "offlineaffiliate") {
                GroupCode = "OfflineAffiliateTerm";
            }
            else if (CoreCommonMethods.Like(utm_source, "%pbuae_offline_affiliate%")) {
                GroupCode = "PBUAE_OfflineAffiliate";
            }
            else if (CoreCommonMethods.Like(utm_campaign, "%tataaia_sat%")) {
                GroupCode = "RetainerswithTaTa";
            }
            else if (CoreCommonMethods.Like(utm_campaign, "%pnb_sat%")) {
                GroupCode = "RetainerswithPNB";
            }
            else if (utm_campaign == "termonmotor") {
                GroupCode = "TermOnMotor";
            }
            else if (lead_source == "crosssell" && utm_source == "term_booked" && lead.CityID == 398) {
                GroupCode = "Health Ludhiana Team";
            }
            else if (lead_source == "crosssell" && utm_source == "term_booked" && lead.CityID == 499) {
                GroupCode = "Lucknow";
            }
            else if (lead_source == "crosssell" && utm_source == "term_rejected") {
                GroupCode = "FOS OFFICES";
            }
            else if (utm_source == "hdfc_term_on_inv") {
                GroupCode = "HDFC_Reopen_Retainer";
            }
            else if (utm_source == "fos rejected") {
                GroupCode = "Term FOS Appointment";
            }
            else if (utm_medium == "nri-reopen") {
                GroupCode = "Super Sellers";
            }
           
            lead.GroupCode = GroupCode;
        }

        private static void RankLeadByLeadScore(TermLeadDetails lead)
        {
            short LeadRank = TermAllocationDLL.GetLeadRankByLeadScore(lead);

            if (LeadRank > 0)
            {
                lead.LeadRank = LeadRank;
            }
        }

        private static void PopulateAdditionalDataUpdate(TermLeadDetails leadData)
        {
            if (leadData.AssignmentProcess == "CTC")
            {
                int[] testLeadRank = new []{ 477, 478, 479, 480, 571, 572, 573, 574, 371};
                if(
                    leadData.LeadId % 2 == 0
                    && (
                        (leadData.LeadRank >= 41 && leadData.LeadRank <= 44)
                        || (leadData.LeadRank >= 1 && leadData.LeadRank <= 4)
                        || (leadData.LeadRank >= 21 && leadData.LeadRank <= 24)
                        || (leadData.LeadRank >= 525 && leadData.LeadRank <= 528)
                        || (leadData.LeadRank >= 541 && leadData.LeadRank <= 544)
                        || (leadData.LeadRank >= 291 && leadData.LeadRank <= 293)
                        || (leadData.LeadRank >= 391 && leadData.LeadRank <= 393)
                        || (leadData.LeadRank >= 294 && leadData.LeadRank <= 296)
                        || (leadData.LeadRank >= 394 && leadData.LeadRank <= 396)
                        || (leadData.LeadRank >= 297 && leadData.LeadRank <= 299)
                        || (leadData.LeadRank >= 397 && leadData.LeadRank <= 399)
                        || (leadData.LeadRank >= 261 && leadData.LeadRank <= 263)
                        || (leadData.LeadRank >= 267 && leadData.LeadRank <= 268)
                        || testLeadRank.Contains(leadData.LeadRank)
                    ) 
                ) 
                {
                    var rankMaster = TermAllocationDLL.GetLeadRankMasterMinRankMap();
                    if (rankMaster.ContainsKey(leadData.LeadRank))
                    {
                        leadData.LeadRank = rankMaster[leadData.LeadRank];
                    }
                }
                else 
                {
                    leadData.IsAllocable = false;
                }
            }
            else
            {
                var ADdataset = TermAllocationDLL.PopulateAdditionalDataUpdate(leadData);
                if (ADdataset != null)
                {
                    var AD = ADdataset.Tables[0].Rows[0];

                    leadData.LeadRank = Convert.ToInt16(AD["LeadRank"]);
                    leadData.GroupCode = Convert.ToString(AD["GroupCode"]);
                    leadData.UserId = Convert.ToInt64(AD["UserID"]);
                    leadData.IsCTC = Convert.ToByte(AD["IsCTC"]);
                }
            }
        }

        private static void ChkLeadAssignedinMultiproduct(TermLeadDetails lead)
        {
            bool isLeadAssignedInMultiProduct = AllocationDLL.ChkLeadAssignedinMultiproduct(lead.CustomerId, lead.ProductID, lead.LeadId);
            if (isLeadAssignedInMultiProduct)
            {
                lead.LeadRank = 200;
            }
        }
        
        private static void GetAgentID_TermAllocation(TermLeadDetails lead, Assign assignment)
        {
            TermAllocationDLL.GetAgentID_TermAllocation(lead, assignment);
        }

        private static void AllocateLead(TermLeadDetails lead, Assign assignment)
        {
            if (lead.AssignmentProcess == "CTC") 
            {
                lead.JobID = 12;
                TermAllocationDLL.ReAssignLead(lead);
            }
            else 
                TermAllocationDLL.AllocateLead(lead, assignment);

            if(lead._LEADTYPE != LEADTYPE.CHURN){
                TermAllocationDLL.UpDateProcessedLead(lead);
            }
            UpdateLeadDataState(lead, DataStatus.LeadAssigned);
        }

        private static void GetCTCndChurnLeads()
        {
            // clear previous data
            churnedLeadsForAllocation.Clear();

            // churned lead data
            DataSet leadsDataSet = TermAllocationDLL.GetCTCndChurnLeads();

            if (leadsDataSet != null && leadsDataSet.Tables[0].Rows.Count != 0)
            {
                foreach (DataRow leadRow in leadsDataSet.Tables[0].Rows)
                {
                    TermLeadDetails leadData = new()
                    {
                        _LEADTYPE = LEADTYPE.CHURN,
                        LeadId = Convert.ToInt64(leadRow["LeadID"]),
                        ProductID = leadRow["ProductID"] != null && leadRow["ProductID"] != DBNull.Value ? Convert.ToInt16(leadRow["ProductID"]) : (short)0,
                        MobileNo = leadRow["MobileNo"] != null && leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : "",
                        Utm_source = leadRow["Utm_source"] == DBNull.Value ? string.Empty : leadRow["Utm_source"].ToString(),
                        CityID = leadRow["CityID"] == null || leadRow["CityID"] == DBNull.Value ? (short)0 : Convert.ToInt16(leadRow["CityID"]),
                        UTM_Medium = leadRow["UTM_Medium"] == DBNull.Value ? string.Empty : leadRow["UTM_Medium"].ToString(),
                        LeadRank = leadRow["LeadRank"] != null && leadRow["LeadRank"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadRank"]) : (short)0,
                        LeadGrade = leadRow["LeadGrade"] != null && leadRow["LeadGrade"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadGrade"]) : (short)0,
                        LeadScore = leadRow["LeadScore"] != null && leadRow["LeadScore"] != DBNull.Value ? Convert.ToDecimal(leadRow["LeadScore"]) : Convert.ToDecimal(0),
                        StatusId = leadRow["StatusID"] == null || leadRow["StatusID"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(leadRow["StatusID"]),
                        SubStatusId = leadRow["SubStatusID"] == null || leadRow["SubStatusID"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["SubStatusID"]),
                        LeadSource = leadRow["LeadSource"] == DBNull.Value ? string.Empty : leadRow["LeadSource"].ToString(),
                        CustomerId = leadRow["CustomerID"] == null || leadRow["CustomerID"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(leadRow["CustomerID"]),
                        CreatedOn = leadRow["CreatedON"] != null && leadRow["CreatedON"] != DBNull.Value ? Convert.ToDateTime(leadRow["CreatedON"]) : DateTime.MinValue,
                        GroupCode = leadRow["GroupCode"] != null && leadRow["GroupCode"] != DBNull.Value ? leadRow["GroupCode"].ToString() : string.Empty,
                        InsurerID = leadRow["InsurerID"] == null || leadRow["InsurerID"] == DBNull.Value ? 0 : Convert.ToInt32(leadRow["InsurerID"]),
                        GroupID = leadRow["GroupID"] == null || leadRow["GroupID"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(leadRow["GroupID"]),
                        UserId = leadRow["UserId"] == null || leadRow["UserId"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(leadRow["UserId"])
                    };

                    var idx = IsLeadDataAvailable(leadData, churnedLeadsForAllocation);

                    if (idx < 0)
                    {
                        UpdateLeadDataState(leadData, DataStatus.DataPending);
                        churnedLeadsForAllocation.Insert(~idx, leadData);
                    }
                }
            }
        }

        #region Utility
        private static void PurgeAssignedLeads()
        {
            // purge leads which are assigned and older than 1 hour
            leads.RemoveAll(lead => lead.DataState == DataStatus.LeadAssigned && lead.CreatedOn < DateTime.Now.AddHours(-1));
        }

        private static void UpdateLeadDataState(TermLeadDetails lead, DataStatus state)
        {
            lead.DataState = state;
        }

        private static int IsLeadDataAvailable(TermLeadDetails lead, List<TermLeadDetails> leadList)
        {
            CompareLeadId comparator = new();

            int index = leadList.BinarySearch(lead, comparator);
            return index;
        }

        public class CompareLeadId : IComparer<TermLeadDetails>
        {
            public int Compare(TermLeadDetails x, TermLeadDetails y)
            {
                if (x.LeadId == 0) return -1;
                if (y.LeadId == 0) return 1;

                return (int)(x.LeadId - y.LeadId);
            }
        }

        #endregion Utility


        private static void HidePIIFromLeads(List<TermLeadDetails> leadList)
        {
            foreach (var lead in leadList)
            {
                lead.MobileNo = "";
            }
        }
        
        // API
        public async Task<AllocateTermLeadResponse> AllocateTermLeadAPI(AllocateLeadsData allocateLeadsData)
        {
            AllocateTermLeadResponse response = new();
            var assignment = new Assign{ reassignCount = 0, NoAgent = 0, CrmLeads = 0 };
            string Error = string.Empty;
            List<TermLeadDetails> leadList = new();
            LeadErrors = new();
            DateTime requestTime = DateTime.Now;

            try
            {
                if(allocateLeadsData.LeadId > 0) 
                {
                    GetTermAllocationLeads(leadList, allocateLeadsData.LeadId);

                    if (leadList != null && leadList.Count > 0)
                    {
                        leadList[0].AssignmentProcess = allocateLeadsData == null || allocateLeadsData.AssignmentProcess == null ? string.Empty : allocateLeadsData.AssignmentProcess.ToUpper();

                        await PopulateDataAndAllocateLead(leadList, assignment);
                    }
                }
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {

                HidePIIFromLeads(leadList);

                response.IsSuccess = true;
                response.LeadDetails = leadList;

                if (Error != "" || LeadErrors.Count > 0 || (leadList != null && leadList.Count > 0 && !leadList[0].IsAllocable))
                {
                    response.IsSuccess = false;
                    response.LeadErrors = LeadErrors;
                    response.Error = Error;
                }


                LoggingHelper.LoggingHelper.AddloginQueue(null, allocateLeadsData.LeadId, Error, "AllocateTermLeadAPI", "Allocation", "TermAllocationBLL", JsonConvert.SerializeObject(allocateLeadsData), "", requestTime, DateTime.Now);
                if (LeadErrors.Count > 0)
                {
                    try
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(null, 0, JsonConvert.SerializeObject(LeadErrors), "Error leads - AllocateTermLeadAPI", "Allocation", "TermAllocationBLL", JsonConvert.SerializeObject(allocateLeadsData), "", DateTime.Now, DateTime.Now);
                    }
                    catch (Exception) { }
                }

            }
            return response;
        }

    }
}
