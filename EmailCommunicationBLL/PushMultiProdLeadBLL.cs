using DataAccessLayer;
using Redis;
using System;
using System.Collections.Generic;
using System.Data;



namespace EmailCommunicationBLL
{
    public class PushMultiProdLeadBLL: IPushMultiProdLeadBLL
    {
        public string PushMultiProdLeadInRedis()
        {
            DateTime dt = DateTime.Now;
            try
            {
                DataSet ds = Customerdetaildll.GetMultiProdLead();
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count != 0)
                {
                    Dictionary<string, string> dict = new();
                    foreach (DataRow leadRow in ds.Tables[0].Rows)
                    {
                        string CustomerId = leadRow["CustomerID"] == null || leadRow["CustomerID"] == DBNull.Value ? "" : Convert.ToString(leadRow["CustomerID"]);
                        string ProductID = leadRow["ProductID"] == null || leadRow["ProductID"] == DBNull.Value ? "" : Convert.ToString(leadRow["ProductID"]);

                        if(CustomerId != "" && ProductID != "")
                        {
                            string key = CustomerId+":"+ProductID;
                            if (!dict.ContainsKey(key)) {
                                dict.Add(key, "true");
                            }
                        }
                    }
                    MultiProdRedisHelper.MSetMultiProdRedisData(dict, TimeSpan.FromDays(7));
                }
                return "true";
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("",0, ex.ToString(), "PushMultiProdLeadInRedis", "Allocation", "", "", string.Empty, dt, DateTime.Now);
                return ex.ToString();
            }
        }
    }
}
