﻿using Amazon.S3;
using Confluent.Kafka;
using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Timers;
using static Confluent.Kafka.ConfigPropertyNames;


namespace EmailCommunicationBLL
{
    public class KafkaConsumer
    {
        private readonly System.Timers.Timer _timer;
        private readonly int interval;

        public KafkaConsumer()
        {
            if (CoreCommonMethods.IsDevMode())
            {
                //donot run kafka consumer on local
                return;
            }
            interval = 120 * 1000; // Interval in milliseconds (2 Minute)
            _timer = new(interval);
            _timer.Elapsed += Timer_Elapsed;
            _timer.Start();
        }


        private void Timer_Elapsed(object? sender, ElapsedEventArgs e)
        {
            IConsumer<Null, string>? consumer = null;
            try
            {
                _timer.Stop();
                string groupId = "KafkaGroupId".AppSettings();
                List<string> KafkaTopics = "KafkaWrapperTopic".AppSettings().Split(',').ToList();
                groupId = "Matrix";
                if (!string.IsNullOrEmpty(groupId))
                {
                    var config = KafkaWrapper.GetConsumerConfig(groupId);
                    if (config != null)
                    {
                        //Console.WriteLine("TopicName: " + settings.TopicName);


                        consumer = KafkaWrapper.CreateConsumer(config, KafkaTopics);

                        //consume CB events
                        ConsumeMessage(consumer);
                    }
                    else
                        Console.WriteLine("Kafka settings not found");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception Occurred in Timer_Elapsed: " + ex.ToString());
            }
            finally
            {
                _timer.Start();
                consumer?.Close();
            }
        }


        private static void ConsumeMessage(IConsumer<Null, string> consumer)
        {
            try
            {
                bool response = false;
                while (true)
                {
                    try
                    {

                        var consumerResult = consumer.Consume();

                        if (consumerResult != null && consumerResult.Message != null && !string.IsNullOrEmpty(consumerResult.Message.Value))
                        {
                            var result = consumerResult.Value;
                            if (consumerResult.Value != null)
                            {
                                if (consumerResult.Topic == "bms-unsubscribe-event")
                                {
                                    response = Topic_CustomerUnsubscription(result);
                                }
                                else if (consumerResult.Topic == "Unsubscribe_Event")
                                {
                                    response = Topic_CustomerUnsubscription(result);
                                }else if(consumerResult.Topic == "bms_missell_data")
                                {
                                    response = MisSellBookings.Topic_MissSellData(result);
                                }

                                if (response)
                                {
                                    consumer.Commit(consumerResult);
                                }
                            }
                        }
                      
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Exception Occurred in consumeMessage: " + ex.ToString());
                    }

                }
            }
            catch (OperationCanceledException e)
            {
                Console.WriteLine("Exception Occurred in consumeMessage OperationCanceledException: " + e.ToString());
            }
            finally
            {
                consumer?.Close();
            }
        }

        public static bool Topic_CustomerUnsubscription(string result)
        {
            DateTime reqTime = DateTime.Now;
            long customerId = 0;
            bool response = false;
            List<string> AllProductIds = "UnsubscribeProductIds".AppSettings().Split(',').ToList();
            List<string> CategoriesId = "UnsubscribeCategoryIds".AppSettings().Split(',').ToList();
            List<string> ChannelIds = "UnsubscribeChannelIds".AppSettings().Split(',').ToList();
            List<string> AlteredProducts = null;
            bool IsPreviousEntry0 = false;
            bool IsPreviousEntry0Active = false;
            DateTime start = DateTime.Now;
            DateTime end = DateTime.MinValue;
            try
            {
                CustomerUnsubscriptionData oCustomerUnsubscriptionData = JsonConvert.DeserializeObject<CustomerUnsubscriptionData>(result);

                if (oCustomerUnsubscriptionData != null && oCustomerUnsubscriptionData.CustomerId > 0)
                {
                    customerId = oCustomerUnsubscriptionData.CustomerId;
                    string mobileNo = oCustomerUnsubscriptionData.MobileNo;
                    long MobileNo = 0;
                    MobileNo = string.IsNullOrEmpty(mobileNo) ? 0 : Convert.ToInt64(mobileNo);

                    //As discussed with Comm team, catering all the requests!
                    if(oCustomerUnsubscriptionData.CommPreferences == null && oCustomerUnsubscriptionData.IsUnsubscribeAll == false)
                    {
                        foreach (var product in AllProductIds)
                        {
                            foreach (var category in CategoriesId)
                            {
                                Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                           Convert.ToInt32(category),
                                                                                           Convert.ToInt32(ChannelIds[0]),
                                                                                           false,
                                                                                           oCustomerUnsubscriptionData.RequestPushTime,
                                                                                           Convert.ToInt32(product),
                                                                                           0);
                                response = true;
                            }
                        }
                    }
                    //If unsubscription for all is true, means all categoryid, channelid for every productid is to be added in database
                    else if (oCustomerUnsubscriptionData.CommPreferences == null && oCustomerUnsubscriptionData.IsUnsubscribeAll == true)
                    {
                        foreach(var product in AllProductIds)
                        {
                            foreach(var category in CategoriesId)
                            {
                                Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                           Convert.ToInt32(category),
                                                                                           Convert.ToInt32(ChannelIds[0]),
                                                                                           true,
                                                                                           oCustomerUnsubscriptionData.RequestPushTime, 
                                                                                           Convert.ToInt32(product),
                                                                                           0);
                                response = true;
                            }
                        }
                    }
                    else
                    {
                        foreach (var item in oCustomerUnsubscriptionData.CommPreferences)
                        {
                            AlteredProducts = new List<string>(AllProductIds);
                            
                            //For specific categoryId and ChannelId
                            if (CategoriesId.Contains(Convert.ToString(item.CategoryId)) && ChannelIds.Contains(Convert.ToString(item.ChannelId)))
                            {
                                //Checking whether Productid 0 is in database
                                    var ds1 = Customerdetaildll.GetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                        item.CategoryId,
                                                                                                        item.ChannelId);
                                    if (ds1 != null && ds1.Tables.Count > 0 && ds1.Tables[0].Rows.Count > 0)
                                    {
                                        IsPreviousEntry0 = ds1.Tables[0].Rows[0]["ProductId"] != null && ds1.Tables[0].Rows[0]["ProductId"] != DBNull.Value ? Convert.ToBoolean(Convert.ToInt32(ds1.Tables[0].Rows[0]["ProductId"]) == 0 ? true : false) : false;
                                        IsPreviousEntry0Active = ds1.Tables[0].Rows[0]["IsActive"] != null && ds1.Tables[0].Rows[0]["IsActive"] != DBNull.Value ? Convert.ToBoolean(ds1.Tables[0].Rows[0]["IsActive"]) : false;
                                    }

                                //For Subscribing Case where a specific productid is given
                                if (item.UnSubscribed == false && !item.ProductIds.Contains(0) && IsPreviousEntry0Active)
                                {
                                    //Updating database for productid given, for subscribing case
                                    foreach (var product in item.ProductIds)
                                    {
                                        response = Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                        item.CategoryId,
                                                                                                        item.ChannelId,
                                                                                                        item.UnSubscribed,
                                                                                                        oCustomerUnsubscriptionData.RequestPushTime, 
                                                                                                        product,
                                                                                                        MobileNo);
                                        response = true;
                                        AlteredProducts.Remove(Convert.ToString(product));
                                    }

                                    //If productid was 0 in database, then we enter all other productid and set unsubscription
                                    if(IsPreviousEntry0 == true)
                                    {
                                        foreach (var product in AlteredProducts)
                                        {
                                            response = Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                            item.CategoryId,
                                                                                                            item.ChannelId,
                                                                                                            !item.UnSubscribed,
                                                                                                            oCustomerUnsubscriptionData.RequestPushTime, 
                                                                                                            Convert.ToInt32(product),
                                                                                                            MobileNo);
                                            response = true;
                                        }
                                    }
                                }
                                else if (item.UnSubscribed == true && !item.ProductIds.Contains(0) && IsPreviousEntry0Active)
                                {
                                    //Updating database for productid given, for subscribing case
                                    foreach (var product in item.ProductIds)
                                    {
                                        response = Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                        item.CategoryId,
                                                                                                        item.ChannelId,
                                                                                                        item.UnSubscribed,
                                                                                                        oCustomerUnsubscriptionData.RequestPushTime,
                                                                                                        product,
                                                                                                        MobileNo);
                                        response = true;
                                        AlteredProducts.Remove(Convert.ToString(product));
                                    }

                                    //If productid was 0 in database, then we enter all other productid and set unsubscription
                                    if (IsPreviousEntry0 == true)
                                    {
                                        foreach (var product in AlteredProducts)
                                        {
                                            response = Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                            item.CategoryId,
                                                                                                            item.ChannelId,
                                                                                                            item.UnSubscribed,
                                                                                                            oCustomerUnsubscriptionData.RequestPushTime,
                                                                                                            Convert.ToInt32(product),
                                                                                                            MobileNo);
                                            response = true;
                                        }
                                    }
                                }
                                //For all unsuscribing case
                                else
                                {
                                    //if productid given is 0, then add all products in db
                                    if (item.ProductIds.Contains(0))
                                    {
                                        foreach (var product in AllProductIds)
                                        {
                                            response = Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                            item.CategoryId,
                                                                                                            item.ChannelId,
                                                                                                            item.UnSubscribed,
                                                                                                            oCustomerUnsubscriptionData.RequestPushTime, 
                                                                                                            Convert.ToInt32(product),
                                                                                                            MobileNo);
                                            response = true;
                                        }
                                    }
                                    //else add all products given
                                    else
                                    {
                                        foreach (var product in item.ProductIds)
                                        {
                                            response = Customerdetaildll.SetCustomerUnsubscription(oCustomerUnsubscriptionData.CustomerId,
                                                                                                            item.CategoryId,
                                                                                                            item.ChannelId,
                                                                                                            item.UnSubscribed,
                                                                                                            oCustomerUnsubscriptionData.RequestPushTime, 
                                                                                                            product,
                                                                                                            MobileNo);
                                            response = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                }
                end = DateTime.Now;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", customerId, ex.ToString(), "Topic_CustomerUnError", "FOSallocationJobNew", "KafkaConsumer", result, ex.ToString(), reqTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", customerId,"", "Topic_bmsUnsubscribeEvent", "FOSallocationJobNew", "KafkaConsumer", result, "", reqTime, DateTime.Now);
            }

            return response;
        }

    }
}