﻿using System;
using DataAccessLayer;
using System.Data;
using PropertyLayers;
using EmailCommunicationBLL.RejectionHelpers;
using System.Collections.Generic;
using DataAccessLibrary;
using System.Runtime.Caching;
using Helper;
using System.Linq;
using MongoConfigProject;


namespace EmailCommunicationBLL
{
    public class LeadRejectionBLL : ILeadRejectionBLL
    {

        private static void PopulateLeadDetails(ParentDetails ParentLead, DataRow leadRow)
        {


            /*ParentLead.LeadID = leadRow["LeadID"] == null|| leadRow["LeadID"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(leadRow["LeadID"]);
            ParentLead.Name = leadRow["Name"]== null || leadRow["Name"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["Name"]);
            ParentLead.ParentID = leadRow["ParentID"] == null || leadRow["ParentID"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(leadRow["ParentID"]);*/


            ParentLead.CreatedOn = leadRow["CreatedOn"] == null || leadRow["CreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["CreatedOn"]);
            ParentLead.ProductId = leadRow["ProductID"] == null || leadRow["ProductID"] == DBNull.Value ?  (Int16)0 : Convert.ToInt16(leadRow["ProductID"]);
            ParentLead.LeadSource = leadRow["LeadSource"] == null || leadRow["LeadSource"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["LeadSource"]);
            ParentLead.UTM_Source = leadRow["UTM_source"] == null || leadRow["UTM_source"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["UTM_source"]);
            ParentLead.StatusID = leadRow["StatusID"] == null || leadRow["StatusID"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["StatusID"]);
            ParentLead.LeadRank = leadRow["LeadRank"] == null || leadRow["LeadRank"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["LeadRank"]);
            ParentLead.Country = leadRow["Country"] == null || leadRow["Country"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["Country"]);
            ParentLead.Source = leadRow["Source"] == null || leadRow["Source"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["Source"]);
            ParentLead.UTM_Term = leadRow["UTM_Term"] == null || leadRow["UTM_Term"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["UTM_Term"]);




            //LAD data
            ParentLead.LeadAssignData = new();
            LeadAssignData LeadAssignInfo = new LeadAssignData();

            LeadAssignInfo.AssignedDate = leadRow["AssignedDate"] == null || leadRow["AssignedDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["AssignedDate"]);
            LeadAssignInfo.AssignToGroupId = leadRow["AssignToGroupId"] == null || leadRow["AssignToGroupId"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["AssignToGroupId"]);

            ParentLead.LeadAssignData.Add(LeadAssignInfo);



            //CallBackData
            ParentLead.CallBackData = new();
            CallBackDataED callBack = new CallBackDataED();

            callBack.EventDate = leadRow["EventDate"] == null || leadRow["EventDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["EventDate"]);
            callBack.CallBackType = leadRow["CallbackType"] == null || leadRow["CallbackType"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["CallbackType"]);

            ParentLead.CallBackData.Add(callBack);



            //ProdDetails Data
            ParentLead.InvestmentTypeID = leadRow["InvestmentTypeID"] == null || leadRow["InvestmentTypeID"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["InvestmentTypeID"]);

            //BookingDetails
            ParentLead.OfferCreatedOn = leadRow["OfferCreatedOn"] == null || leadRow["OfferCreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["OfferCreatedOn"]);
            ParentLead.PaymentStatus = leadRow["PaymentStatus"] == null || leadRow["PaymentStatus"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(leadRow["PaymentStatus"]);

            //TravelDetails
            PopulateTravelDetails(ParentLead, leadRow);  //TravelStartDate


            //AppointmentData
            ParentLead.AppointmentDateTime = leadRow["AppointmentDateTime"] == null || leadRow["AppointmentDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["AppointmentDateTime"]);

            //RenewDetails
            ParentLead.PolicyExpiryDate = leadRow["PolicyExpiryDate"] == null || leadRow["PolicyExpiryDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["PolicyExpiryDate"]);

            // Car Details
            ParentLead.IsPreviousPolicyDateAssumed = leadRow["IsPreviousPolicyDateAssumed"] == null || leadRow["IsPreviousPolicyDateAssumed"] == DBNull.Value ? false : Convert.ToBoolean(leadRow["IsPreviousPolicyDateAssumed"]);
            ParentLead.PolicyType = leadRow["PolicyType"] == null || leadRow["PolicyType"] == DBNull.Value ? string.Empty : Convert.ToString(leadRow["PolicyType"]);

            //CustomerUnsubscriptionData
            ParentLead.CategoryId = leadRow["CategoryId"] == null || leadRow["CategoryId"] == DBNull.Value ? 0 : Convert.ToInt32(leadRow["CategoryId"]);
            ParentLead.ChannelId = leadRow["ChannelId"] == null || leadRow["ChannelId"] == DBNull.Value ? 0: Convert.ToInt32(leadRow["ChannelId"]);
            ParentLead.CustUnsubscribeCreatedOn = leadRow["CustUnsubscribeCreatedOn"] == null || leadRow["CustUnsubscribeCreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(leadRow["CustUnsubscribeCreatedOn"]);
        }


        public static void PopulateTravelDetails(ParentDetails ParentLead, DataRow leadRow)
        {
            string date = leadRow["TravelStartDate"] == null || leadRow["TravelStartDate"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["TravelStartDate"]);

            if (string.IsNullOrEmpty(date) || !(DateTime.TryParse(date, out DateTime result)))
                ParentLead.TravelStartDate = DateTime.MinValue;

            else
            {
                DateTime.TryParse(date, out DateTime TravelStartDate);
                ParentLead.TravelStartDate = TravelStartDate;
            }

        }

        private void PopulateCallData(ParentDetails ParentLead, DataTable CallTable)
        {
            int TotalTalkTime = 0;
            int TotalCallCount = 0;
            ParentLead.CallHistory = new();

            if (CallTable != null && CallTable.Rows.Count > 0)
            {
                
                ParentLead.LastCallDate = DateTime.MinValue;
                foreach (DataRow callrow in CallTable.Rows)
                {
                    CallDataCDH call = new CallDataCDH();

                    call.TalkTime = callrow["talktime"] == null || callrow["talktime"] == DBNull.Value ? (Int32)0 : Convert.ToInt32(callrow["talktime"]);
                    call.CallDate = callrow["CallDate"] == null || callrow["CallDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(callrow["CallDate"]);
                    call.CallDataID = callrow["CallDataID"] == null || callrow["CallDataID"] == DBNull.Value ?  (Int64)0 : Convert.ToInt64(callrow["CallDataID"]);
                    call.Duration = callrow["Duration"] == null || callrow["Duration"] == DBNull.Value ? (Int32)0 : Convert.ToInt32(callrow["Duration"]);
                    call.Context = callrow["Context"] == null || callrow["Context"] == DBNull.Value ? String.Empty : Convert.ToString(callrow["Context"]);
                    call.CallType = callrow["CallType"] == null || callrow["CallType"] == DBNull.Value ? String.Empty : Convert.ToString(callrow["CallType"]);



                    if (call.CallDate > ParentLead.LastCallDate)
                    { 
                        ParentLead.LastCallDate = call.CallDate;
                    }
                    ParentLead.CallHistory.Add(call);
                    TotalTalkTime += call.TalkTime;
                    TotalCallCount++;
                    

                }
            }

            ParentLead.TotalTalkTime = TotalTalkTime;
            ParentLead.TotalCallCount = TotalCallCount;

        }

        public static List<Int16> GetAssignToGroupIDs(Int16 ProcessID)
        {
            try
            {
                List<Int16> GroupIDs = new List<Int16>();

                string Key = $"{RedisCollection.GetAssignToGroupIDsFromDB()}";

                if (MemoryCache.Default[Key] != null)
                    GroupIDs = (List<Int16>)MemoryCache.Default.Get(Key);

                else
                {
                    DataSet ds = LeadRejectionDLL.GetGroupIdByProcessID(ProcessID);

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow dataRow in ds.Tables[0].Rows)
                        {
                            GroupIDs.Add(Convert.ToInt16(dataRow["GroupID"]));
                        }

                    }

                    if (GroupIDs.Count > 0)
                        CommonCache.GetOrInsertIntoCache(GroupIDs, Key, 8 * 60);
                }
                return GroupIDs;
            }

            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetGroupIdFromProdGrpMappingAllc", "LeadRejection", "LeadRejectionBLL", "", " ", DateTime.Now, DateTime.Now);
                return null;
            }


        }


        /*
        private void PopulateCallBackdata(ParentDetails ParentLead)
        {
            DataSet CallBackDs = LeadRejectionDLL.GetCallBackData(ParentLead.ParentID);
            if (CallBackDs != null && CallBackDs.Tables.Count > 0)
            {
                ParentLead.CallBackData = new();

                foreach(DataRow CallBackRow in CallBackDs.Tables[0].Rows)
                {
                    CallBackDataED callBack = new CallBackDataED();  
                    
                    callBack.EventDate = CallBackRow["EventDate"] == null || CallBackRow["EventDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(CallBackRow["EventDate"]);
                    callBack.CallBackType = CallBackRow["CallbackType"] == null || CallBackRow["CallbackType"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(CallBackRow["CallbackType"]);

                    ParentLead.CallBackData.Add(callBack);
                
                }
            }
        }


        private void PopulateLeadAssignData(ParentDetails ParentLead)
        {
            DataSet LeadAssignDs = LeadRejectionDLL.GetLeadAssignInfo(ParentLead.ParentID);
            if (LeadAssignDs != null && LeadAssignDs.Tables.Count > 0)
            {
                ParentLead.LeadAssignData = new();

                foreach (DataRow LeadAssignRow in LeadAssignDs.Tables[0].Rows)
                {
                    LeadAssignData LeadAssignInfo = new LeadAssignData();

                    LeadAssignInfo.AssignedDate = LeadAssignRow["CreatedOn"] == null || LeadAssignRow["CreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(LeadAssignRow["CreatedOn"]);
                    LeadAssignInfo.AssignToGroupId = LeadAssignRow["AssignToGroupId"] == null || LeadAssignRow["AssignToGroupId"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(LeadAssignRow["AssignToGroupId"]);
                    
                    ParentLead.LeadAssignData.Add(LeadAssignInfo);

                }
            }
        }

        private void PopulateProductDetailsData(ParentDetails ParentLead)
        {
            DataSet ProductDs = LeadRejectionDLL.GetProductDetails(ParentLead.ParentID);
            if (ProductDs != null && ProductDs.Tables.Count > 0 && ProductDs.Tables[0].Rows.Count > 0)
            {
                DataRow ProdRow = ProductDs.Tables[0].Rows[0];
                ParentLead.InvestmentTypeID = ProdRow["InvestmentTypeID"] == null || ProdRow["InvestmentTypeID"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(ProdRow["InvestmentTypeID"]);
            }
        }

        */

        public List<LeadDetailsRejection> LoggingRejectedLeads = new List<LeadDetailsRejection>();
        public bool LeadRejectionBySystem()
        {

            try
            {

                DateTime ToDate = DateTime.Now.Date;
                DateTime FromDate = DateTime.Now.AddDays(-65).Date; // 255
                TimeSpan interval = new(1,0,0,0);
                DateTime date = FromDate;
                

                //For Checking Time
                //---------------------------------------------------------------
                string CheckProcessTime = "";
                int CheckCountForLogging = 0;
                CheckProcessTime += $"Starting Time : {DateTime.Now} \n";
                DateTime start = DateTime.Now;
                //---------------------------------------------------------------

                //sme fost groups 
                int[] SMEFosGroupIds = "SMEFosGroupIds".AppSettings().Split(',').Select(int.Parse).ToArray();


                while (date <= ToDate)
                {
                    try
                    {


                        DataSet ParentDs = LeadRejectionDLL.GetAllParentIDs(date, date.Add(interval));

                        if (CheckCountForLogging < 10) CheckProcessTime += $"ParentLeadsFetching Time : {DateTime.Now - start} \n";





                        if (ParentDs != null && ParentDs.Tables.Count > 0)
                        {
                            foreach (DataRow ParentLeadRow in ParentDs.Tables[0].Rows)
                            {
                                ParentDetails ParentLead = new ParentDetails();
                                try
                                {  
                                    ParentLead.ParentID = ParentLeadRow["ParentID"] == null || ParentLeadRow["ParentID"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(ParentLeadRow["ParentID"]);
                                    /*
                                        PopulateCallData(ParentLead);
                                        //------------------------------------------------------------------------------
                                        if (CheckCountForLogging < 10) CheckProcessTime += $"PopulateCallData Time : {DateTime.Now - start}  \n";
                                        //------------------------------------------------------------------------------

                                        PopulateCallBackdata(ParentLead);
                                        //------------------------------------------------------------------------------
                                        if (CheckCountForLogging < 10) CheckProcessTime += $"PopulateCallBackData Time : {DateTime.Now - start}  \n";
                                        //------------------------------------------------------------------------------

                                        PopulateLeadAssignData(ParentLead);
                                        //------------------------------------------------------------------------------
                                        if (CheckCountForLogging < 10) CheckProcessTime += $"PopulateLeadsAssign Time : {DateTime.Now - start}  \n";
                                        //------------------------------------------------------------------------------


                                        //ParentLead.LeadDetailsList = new();
                                        */

                                    DataSet ds = LeadRejectionDLL.GetLeadDetailsForRejection(ParentLead.ParentID);
                                    if (CheckCountForLogging < 10) CheckProcessTime += $"FetchingLeadDetailsOfParent Time : {DateTime.Now - start}  \n";


                                    if (ds != null && ds.Tables.Count > 2 && ds.Tables[0].Rows.Count > 0 && ds.Tables[1].Rows.Count > 0 && Convert.ToInt16(ds.Tables[0].Rows[0]["Success"]) == 1)
                                    {
                                        var leadRow = ds.Tables[1].Rows[0];

                                        try
                                        {
                                            PopulateLeadDetails(ParentLead, leadRow);
                                            if (CheckCountForLogging < 10) CheckProcessTime += $"PopulateLeadDetails Time : {DateTime.Now - start}  \n";


                                            
                                            var CallTable = ds.Tables[2];
                                            PopulateCallData(ParentLead, CallTable);
                                            if (CheckCountForLogging < 10) CheckProcessTime += $"PopulateCallData Time : {DateTime.Now - start}  \n";


                                            //sme fos leads should not get rejected before 150 days.
                                            if (ParentLead.ProductId != 117 ) {
                                                continue;
                                            }
                                            if (ParentLead?.ProductId == 131 &&
                                                ParentLead.LeadAssignData != null &&
                                                ParentLead?.LeadAssignData?.Count > 0 &&
                                                ParentLead.LeadAssignData[0].AssignToGroupId > 0 &&
                                                SMEFosGroupIds.Length > 0 &&
                                                SMEFosGroupIds.Contains(ParentLead.LeadAssignData[0].AssignToGroupId))
                                            {
                                                continue;
                                            }
                                            else
                                            {
                                                CheckAndRejectLead(ParentLead);
                                            }



                                            //------------------------------------------------------------------------------
                                            if (CheckCountForLogging < 10)
                                            {
                                                CheckProcessTime += $"CheckAndReject Time : {DateTime.Now - start}   \n";
                                                CheckCountForLogging++;
                                                LoggingHelper.LoggingHelper.AddloginQueue(null, ParentLead.ParentID, "", "LeadRejectionTime", "LeadRejection", "LeadRejectionBLL", "", CheckProcessTime, DateTime.Now, DateTime.Now);
                                                Console.WriteLine(CheckProcessTime);
                                                CheckProcessTime = "";

                                            }
                                            //------------------------------------------------------------------------------

                                        }

                                        catch (Exception e)
                                        {
                                            LoggingHelper.LoggingHelper.AddloginQueue(null, ParentLead.ParentID, e.ToString(), "LeadRejectionBySystem", "LeadRejection", "LeadRejectionBLL", "", " ", DateTime.Now, DateTime.Now);
                                        }


                                        /*foreach (LeadDetailsRejection lead in ParentLead.LeadDetailsList)
                                            //{

                                            //    CheckAndRejectLead(lead, ParentLead);
                                            //    //------------------------------------------------------------------------------
                                            //    if (CheckCountForLogging < 10)
                                            //    {
                                            //        CheckProcessTime += $"CheckAndReject Time : {DateTime.Now - start}   \n";
                                            //        CheckCountForLogging++;
                                            //        LoggingHelper.LoggingHelper.AddloginQueue(null, lead.LeadID, "", "LeadRejectionTime", "LeadRejection", "LeadRejectionBLL", "", CheckProcessTime, DateTime.Now, DateTime.Now);
                                            //        Console.WriteLine(CheckProcessTime);
                                            //        CheckProcessTime = "";

                                            //    }
                                            //    //------------------------------------------------------------------------------
                                            //    if (ParentLead.isRejectAll) break;

                                            //}
                                            */

                                    }
                                }

                                catch (Exception e)
                                {

                                    LoggingHelper.LoggingHelper.AddloginQueue(null, ParentLead.ParentID, e.ToString(), "LeadRejectionBySystem", "LeadRejection", "LeadRejectionBLL", "", " ", DateTime.Now, DateTime.Now);
                                }

                            }

                        }
                    }


                    catch (Exception e)
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(null, 0, e.ToString(), "LeadRejectionBySystem", "LeadRejection", "LeadRejectionBLL", "", " ", DateTime.Now, DateTime.Now);
                    }


                    date = date.Add(interval);
                }
                

                

                CheckProcessTime += $"EndTime : {DateTime.Now - start}   \n";
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, "", "LeadRejectionEndTime", "LeadRejection", "LeadRejectionBLL", "", CheckProcessTime, start, DateTime.Now);
                //LoggingHelper.LoggingHelper.AddloginQueue(null,0, "", "RejectedLeadsList", "LeadRejection", "LeadRejectionBLL", "", JsonConvert.SerializeObject(LoggingRejectedLeads), DateTime.Now, DateTime.Now);
                /*
                //var resultResponse = await LeadRejectionDLL.GetAllLeadsFromAthena();

                //if (resultResponse == null)
                //{
                //    return false;
                //}

                //Console.WriteLine($"Result Response : {resultResponse} \n");

                ////var serializeddata = JsonConvert.SerializeObject(resultResponse);
                ////Console.WriteLine($" Serialized Data : {serializeddata} \n");

                //Dictionary<string, int> Col_Name = new Dictionary<string, int>();

                //foreach (var index in resultResponse.ResultSet.Rows)
                //{
                //    int i = 0;
                //    foreach(var data in index.Data)
                //    {
                //        string columnName = data.VarCharValue;
                //        Col_Name.Add(columnName,i);
                //        i++;
                //    }
                //    break;

                //}

                //foreach (var row in resultResponse.ResultSet.Rows)
                //{
                //    //var serializedrow = JsonConvert.SerializeObject(row);
                //    //Console.WriteLine($" Serialized Row : {serializedrow} \n");

                //    //var serializedrowdata = JsonConvert.SerializeObject(row.Data);
                //    //Console.WriteLine($" Serialized Row Data : {serializedrowdata} \n");



                //    string Name = row.Data[Col_Name["name"]].VarCharValue;
                //    string LeadId = row.Data[Col_Name["leadid"]].VarCharValue;
                //    string MobileNo = row.Data[Col_Name["mobileno"]].VarCharValue;
                //    string CreatedOn = row.Data[Col_Name["createdon"]].VarCharValue;
                //    string DOB = row.Data[Col_Name["dob"]].VarCharValue;
                //    string ExitPointURL = row.Data[Col_Name["exitpointurl"]].VarCharValue;

                //    Console.WriteLine(LeadId + " " + Name + " " + MobileNo + " " + CreatedOn + " " + DOB + " " + ExitPointURL);
                //    //foreach (var data in row.Data)
                //    //{
                //    //    Console.Write($"final values {data.VarCharValue}\t");
                //    //}
                //    Console.WriteLine();
                */
                return true;

            }

            catch (Exception ex)
            {
                Console.WriteLine($"RunCheckAsync exception: {ex}");
                return false;
            }


            return true;
        }

        private void CheckAndRejectLead(ParentDetails ParentLead)
        {
            Boolean IsRejectLead = false;
            /*
            //LeadRejectionFactory factory = new LeadRejectionFactory();
            //ILeadRejectionLogics rejectionLogic = factory.GetRejectionLogic(ParentLead);
            */
            ILeadRejectionLogics rejectionLogic = new EvaluationLogics();

            var collection = LeadRejectionDLL.GetLeadRejectionLogicsDocs(ParentLead.ProductId);

            foreach (var document in collection)
            {
                IsRejectLead = rejectionLogic.GenericEvaluate(ParentLead,document);
                if (IsRejectLead) 
                {
                    ParentLead.Reason = document.RejectionReason;
                    ParentLead.SubStatusID = document.SubStatusID;
                    break;
                }
                
            }


            

            if (IsRejectLead)
            {
                return;
                //REJECT_ALL_CHILD
                DataSet ds = LeadRejectionDLL.GetAllActiveChildFromParent(ParentLead.ParentID);

                if (ds != null && ds.Tables.Count > 0)
                {

                    foreach (DataRow leadRow in ds.Tables[0].Rows)
                    {

                        LeadDetailsRejection lead = new LeadDetailsRejection();
                        try
                        {
                            lead.LeadID = leadRow["LeadID"] == null || leadRow["LeadID"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(leadRow["LeadID"]);
                            lead.ProductId = leadRow["ProductID"] == null || leadRow["ProductID"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["ProductID"]);
                            lead.StatusID = leadRow["StatusID"] == null || leadRow["StatusID"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(leadRow["StatusID"]);
                            lead.LeadSource = leadRow["LeadSource"] == null || leadRow["LeadSource"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["LeadSource"]);
                            lead.UTM_Source = leadRow["UTM_source"] == null || leadRow["UTM_source"] == DBNull.Value ? String.Empty : Convert.ToString(leadRow["UTM_source"]);

                            //LoggingHelper.LoggingHelper.AddloginQueue(null,ProductID , "", "LeadRejectedBySystem1", "LeadRejection", "LeadRejectionBLL", "", JsonConvert.SerializeObject(RejectedLead), DateTime.Now, DateTime.Now);
                            if(ParentLead.DontRejectChildWithLeadSource != null && ParentLead.DontRejectChildWithLeadSource.Count != 0)
                            {
                                if (RejectionHelper.CompareStrings(lead.LeadSource, ParentLead.DontRejectChildWithLeadSource))
                                    continue;
                            }

                            if (ParentLead.DontRejectChildWithUtmSource != null && ParentLead.DontRejectChildWithUtmSource.Count != 0)
                            {
                                if (RejectionHelper.CompareStrings(lead.UTM_Source, ParentLead.DontRejectChildWithUtmSource))
                                    continue;
                            }

                            if(lead.StatusID>=13) continue;

                            LeadRejectionDLL.DumpRejectedLeads(lead.LeadID, lead.ProductId, DateTime.Now, ParentLead.Reason);
                            LeadRejectionDLL.RejectLeads(lead.LeadID, lead.StatusID, lead.ProductId, ParentLead.Reason, ParentLead.SubStatusID);
                            
                        }

                        catch (Exception e)
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue(null, ParentLead.ParentID, e.ToString(), "LeadRejectionBySystem", "LeadRejection", "LeadRejectionBLL", "", " ", DateTime.Now, DateTime.Now);
                        }
                    }
                }
                
                
                /*else
                {
                    LoggingRejectedLeads.Add(lead);
                    Int64 ProductID = lead.ProductId;
                    //LoggingHelper.LoggingHelper.AddloginQueue(null, ProductID, "", "LeadRejectedBySystem1", "LeadRejection", "LeadRejectionBLL", "", JsonConvert.SerializeObject(lead), DateTime.Now, DateTime.Now);
                    LeadRejectionDLL.DumpRejectedLeads(lead.LeadID, lead.ProductId,DateTime.Now, lead.Reason);
                }
                */
                

            }

            
        }

        
    }

            
}

