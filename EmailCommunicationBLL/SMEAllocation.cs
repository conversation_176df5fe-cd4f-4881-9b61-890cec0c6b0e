﻿using DataAccessLayer;
using DataAccessLibrary;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Timers;

namespace EmailCommunicationBLL
{
    public class SMEAllocation
    {
        private static int reassignCount;
        private static Dictionary<long, string> leadErrors;
        private static readonly List<LeadDetails> smeLeads = new();
        private static Timer timer;
        private static bool isProcessing = false;

        public SMEAllocation()
        {
            timer = new(300000); //Five minutes
            timer.Elapsed += Timer_Elapsed;
            timer.Start();
        }

        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            LeadsAllocationSME();
        }

        private static void LeadsAllocationSME()
        {
            reassignCount = 0;
            leadErrors = new();
            DateTime currDate = DateTime.Now;
            try
            {
                timer.Stop();
                if (!isProcessing)
                {
                    isProcessing = true;
                    int totalAssignedLeads = 0;
                    UpdateAgentGrade();
                    GetSMELeadsForAllocation(smeLeads);
                    UpdateAgentsDataForAllocation();

                    List<GroupLeads> subProductGroupLeads = GetGroupLeadsCount();
                    if (subProductGroupLeads != null && subProductGroupLeads.Count > 0)
                    {
                        totalAssignedLeads = subProductGroupLeads.Sum(x => x.LeadsAllocated);
                    }

                    PopulateDataAndAllocateLeadAsync(smeLeads, leadErrors, subProductGroupLeads, totalAssignedLeads);
                    AllocationDLL.UpdateSchedulerendtime(131);

                    //PI for doc trigger code after the allocation
                    ProcessPIForDocTrigger(smeLeads);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "LeadsAllocationSME", "Allocation", "SMEAllocation", "", "", currDate, DateTime.Now);
            }
            finally
            {
                try
                {
                    timer.Start();
                    isProcessing = false;
                    if (leadErrors.Count > 0)
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(null, 0, JsonConvert.SerializeObject(leadErrors), "LeadsAllocationSME", "Allocation", "SMEAllocation", "", "", DateTime.Now, DateTime.Now);
                    }
                }
                catch (Exception) { }
            }
        }

        private static void ProcessPIForDocTrigger(List<LeadDetails> smeLeads)
        {
            long leadId = 0;
            try
            {
                if (smeLeads != null && smeLeads.Count > 0)
                {
                    List<LeadDetails> piLeads = smeLeads.Where(x => x.InvestmentTypeID == 14 && x.IsAllocable && !x.IsChurn).ToList();

                    if (piLeads != null && piLeads.Count > 0)
                    {
                        AllocationDLL.MarkTriggersInactive("PIDocAll");

                        List<TriggeredLeads> triggeredLeads = GetPIForDocTriggeredLeads().OrderBy(p => p.TriggerSent).ToList();
                        int totalTriggeredLeads = triggeredLeads.Sum(x => x.TriggerSent);

                        foreach (var lead in piLeads)
                        {
                            leadId = lead.LeadId;
                            string triggerName = GetTriggerName(triggeredLeads, totalTriggeredLeads, leadId);
                            AllocationDLL.AddPIForDocLeadForTrigger(lead.LeadId, triggerName, "PIDocAll");

                            totalTriggeredLeads = totalTriggeredLeads + 1;
                            foreach (var tLead in triggeredLeads)
                            {
                                if (tLead.TriggerName == triggerName)
                                {
                                    tLead.TriggerSent = tLead.TriggerSent + 1;
                                    break;
                                }
                            }
                        }

                        try
                        {
                            string url = "commapiurl".AppSettings() + "?ProductID=131&SPName=[MTX].[GetSmePIForDocTriggerData]";
                            var res = CommonAPICall.CallAPI(url, "", "GET", 5000, "application/json", null);
                        }
                        catch (Exception ex)
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, ex.ToString(), "ProcessPIForDocTriggerCommAPI", "Allocation", "SMEAllocation", "", "", DateTime.Now, DateTime.Now);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, ex.ToString(), "ProcessPIForDocTrigger", "Allocation", "SMEAllocation", "", "", DateTime.Now, DateTime.Now);
            }
        }

        private static string GetTriggerName(List<TriggeredLeads> triggeredLeads, int totalTriggeredLeads, long leadId)
        {
            string triggerName = string.Empty;
            try
            {
                if (totalTriggeredLeads > 0)
                {
                    foreach (var triggeredLead in triggeredLeads)
                    {
                        double result = (Convert.ToDouble(triggeredLead.TriggerSent) / Convert.ToDouble(totalTriggeredLeads)) * 100;
                        if (result > triggeredLead.TriggerPercent)
                        {
                            continue;
                        }
                        triggerName = triggeredLead.TriggerName;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "GetTriggerName", "Allocation", "SMEAllocation", "", "", DateTime.Now, DateTime.Now);
            }

            if (string.IsNullOrEmpty(triggerName))
            {
                triggerName = triggeredLeads[0].TriggerName;
            }
            return triggerName;
        }

        internal static List<TriggeredLeads> GetPIForDocTriggeredLeads()
        {
            List<TriggeredLeads> triggeredLeads = null;
            try
            {
                DataSet data = AllocationDLL.GetPIForDocTriggeredLeads();
                if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                {
                    triggeredLeads = (from dr in data.Tables[0].AsEnumerable()
                                      select new TriggeredLeads
                                      {
                                          TriggerSent = dr["TriggerSent"] != null && dr["TriggerSent"] != DBNull.Value ? Convert.ToInt32(dr["TriggerSent"]) : default,
                                          TriggerName = dr["TriggerName"] != null && dr["TriggerName"] != DBNull.Value ? Convert.ToString(dr["TriggerName"]) : default,
                                          TriggerPercent = dr["TriggerPercent"] != null && dr["TriggerPercent"] != DBNull.Value ? Convert.ToInt32(dr["TriggerPercent"]) : default
                                      }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetPIForDocTriggeredLeads", "Allocation", "SMEAllocation", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return triggeredLeads;
        }

        internal static void GetSMELeadsForAllocation(List<LeadDetails> leadList)
        {
            DataSet leadsDataSet = AllocationDLL.GetSMELeadsForAllocation(0);

            if (leadsDataSet != null && leadsDataSet.Tables != null && leadsDataSet.Tables[0].Rows != null && leadsDataSet.Tables[0].Rows.Count > 0)
            {
                leadList.Clear();
                foreach (DataRow leadRow in leadsDataSet.Tables[0].Rows)
                {
                    LeadDetails leadData = new()
                    {
                        LeadId = (long)leadRow["LeadID"],
                        MobileNo = leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : default,
                        LeadRank = leadRow["LeadRank"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadRank"]) : default,
                        CreatedOn = leadRow["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(leadRow["CreatedOn"]) : DateTime.MinValue,
                        CustomerId = leadRow["CustId"] == DBNull.Value ? default : (long)leadRow["CustId"],
                        StatusId = leadRow["StatusID"] != DBNull.Value ? (byte)leadRow["StatusID"] : (byte)0,
                        SubStatusId = leadRow["SubStatusID"] != DBNull.Value ? Convert.ToInt16(leadRow["SubStatusID"]) : (short)0,
                        IsAllocable = leadRow["IsAllocable"] != DBNull.Value && Convert.ToBoolean(leadRow["IsAllocable"]),
                        PolicyExpiryDate = leadRow["policyexpirydate"] != DBNull.Value ? Convert.ToDateTime(leadRow["policyexpirydate"]) : null,
                        GroupID = leadRow["GroupID"] != DBNull.Value ? Convert.ToInt16(leadRow["GroupID"]) : (short)0,
                        LeadGrade = leadRow["LeadGrade"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadGrade"]) : (short)0,
                        InvestmentTypeID = leadRow["InvestmentTypeID"] != DBNull.Value ? Convert.ToInt16(leadRow["InvestmentTypeID"]) : (short)0,
                        IsChurn = leadRow["IsChurn"] != DBNull.Value && Convert.ToBoolean(leadRow["IsChurn"]),
                        EmployeeID = leadRow["EmployeeID"] == DBNull.Value ? string.Empty : leadRow["EmployeeID"].ToString(),
                        SA = leadRow["SA"] == DBNull.Value ? default : Convert.ToDecimal(leadRow["SA"]),
                        CityID = leadRow["CityID"] == DBNull.Value ? default : Convert.ToInt16(leadRow["CityID"]),
                        Utm_source = leadRow["Utm_source"] == DBNull.Value ? string.Empty : leadRow["Utm_source"].ToString(),
                        UTM_Medium = leadRow["Utm_medium"] == DBNull.Value ? string.Empty : leadRow["Utm_medium"].ToString(),
                        LeadSource = leadRow["LeadSource"] == DBNull.Value ? string.Empty : leadRow["LeadSource"].ToString(),
                        Country = leadRow["Country"] == DBNull.Value ? string.Empty : leadRow["Country"].ToString(),
                        Utm_term = leadRow["Utm_term"] == DBNull.Value ? string.Empty : leadRow["Utm_term"].ToString(),
                        source = leadRow["Source"] == DBNull.Value ? string.Empty : leadRow["Source"].ToString(),
                        TypeOfPolicy = leadRow["TypeofPolicy"] == DBNull.Value ? string.Empty : leadRow["TypeofPolicy"].ToString(),
                        UserID = leadRow["UserID"] == DBNull.Value ? default : Convert.ToInt64(leadRow["UserID"]),
                        JobID = leadRow["JobID"] == DBNull.Value ? default : Convert.ToInt16(leadRow["JobID"]),
                        Utm_campaign = leadRow["Utm_campaign"] == DBNull.Value ? string.Empty : leadRow["Utm_campaign"].ToString(),
                        ReferralId = leadRow["referralid"] == DBNull.Value ? default : Convert.ToInt64(leadRow["referralid"]),
                        IsFos = leadRow["IsFos"] != DBNull.Value && Convert.ToBoolean(leadRow["IsFos"]),
                        CPMRTO = leadRow["CPMRTO"] != DBNull.Value && Convert.ToBoolean(leadRow["CPMRTO"]),
                        ContinuePQ = leadRow["ContinuePQ"] == DBNull.Value ? null : Convert.ToDateTime(leadRow["ContinuePQ"]),
                        RolloverCheck = leadRow["RolloverCheck"] == DBNull.Value ? null : Convert.ToInt32(leadRow["RolloverCheck"]),
                        TotalNoOfLives = leadRow["TotalNoOfLives"] == DBNull.Value ? null : Convert.ToInt32(leadRow["TotalNoOfLives"]),
                        CustomerType = leadRow["CustomerType"] == DBNull.Value ? default : Convert.ToString(leadRow["CustomerType"]),
                        ProductID = leadRow["ProductId"] == DBNull.Value ? default : Convert.ToInt16(leadRow["ProductId"]),
                        IsCMB = leadRow["IsCMB"] != DBNull.Value && Convert.ToBoolean(leadRow["IsCMB"]),
                        IsCPED = leadRow["IsCPED"] != DBNull.Value && Convert.ToBoolean(leadRow["IsCPED"]),
                        PolicyType = leadRow["PolicyType"] != DBNull.Value ? Convert.ToInt16(leadRow["PolicyType"]) : default,
                        EmployeeRange = leadRow["EmployeeRange"] != DBNull.Value ? Convert.ToString(leadRow["EmployeeRange"]) : default,
                        OccupancyId = leadRow["OccupancyId"] != DBNull.Value ? Convert.ToInt32(leadRow["OccupancyId"]) : default,
                        TotalNoOFEmployees = leadRow["TotalNoOFEmployees"] != DBNull.Value ? Convert.ToInt32(leadRow["TotalNoOFEmployees"]) : default,
                        TransitType = leadRow["TransitType"] != DBNull.Value ? Convert.ToString(leadRow["TransitType"]) : default,
                        LeadScore = leadRow["LeadScore"] != DBNull.Value ? Convert.ToDecimal(leadRow["LeadScore"]) : default,
                        AssignedToAgent = leadRow["AssignedAgent"] != DBNull.Value ? Convert.ToInt64(leadRow["AssignedAgent"]) : default,
                        ParentId = leadRow["ParentId"] != DBNull.Value ? Convert.ToInt64(leadRow["ParentId"]) : default
                    };

                    // NRI lead flag: IsNRI
                    if (!AllocationBLL.IndiaCodes.Contains(leadData.Country))
                    {
                        leadData.IsNRI = true;
                    }

                    leadList.Add(leadData);
                }
            }
        }

        internal static void PopulateLeadAdditionalDetails(LeadDetails lead)
        {
            DataSet data = AllocationDLL.GetAdditionalSMEData(lead);
            if (data != null && data.Tables != null && data.Tables[0].Rows.Count > 0)
            {
                var row = data.Tables[0].Rows[0];
                lead.InsurerID = row["InsurerID"] == DBNull.Value ? lead.InsurerID : Convert.ToInt32(row["InsurerID"]);
                lead.CustBooking = row["CustBooking"] != DBNull.Value && Convert.ToBoolean(row["CustBooking"]);
                lead.IsMarineBooking = row["IsMarinBooking"] != DBNull.Value && Convert.ToBoolean(row["IsMarinBooking"]);
                lead.IsWorkManBooking = row["IsWorkManBooking"] != DBNull.Value && Convert.ToBoolean(row["IsWorkManBooking"]);
                lead.IsAllocable = row["IsAllocable"] != DBNull.Value ? Convert.ToBoolean(row["IsAllocable"]) : lead.IsAllocable;
                lead.UserID = row["UserID"] == DBNull.Value ? lead.UserID : Convert.ToInt64(row["UserID"]);
            }
        }

        internal static void PopulateDataAndAllocateLeadAsync(List<LeadDetails> leadList, Dictionary<long, string> leadErrors, List<GroupLeads> subProductGroupLeads, int totalAssignedLeads)
        {
            foreach (LeadDetails lead in leadList)
            {
                try
                {
                    PopulateLeadAdditionalDetails(lead);
                    if (!lead.IsFos && !lead.IsChurn)
                    {
                        //lead.LeadRank = GetLeadRankFromEngine(lead, leadErrors);
                        lead.GroupID = GetGroupIdFromEngine(lead, leadErrors);
                        lead.LeadRank = AllocationDLL.GetSmeLeadRank(lead);
                    }

                    if (lead.InvestmentTypeID == 19 && subProductGroupLeads != null && subProductGroupLeads.Count > 0)
                    {
                        var sortedGroupList = subProductGroupLeads.OrderBy(p => p.LeadsAllocated).ToList();
                        lead.GroupID = GetLeadGroup(lead, sortedGroupList, totalAssignedLeads);
                    }

                    GetAgentAllocation(lead);

                    if (lead.IsAllocable)
                    {
                        AssignLead(lead);

                        lead.AssigntoUserID = lead.UserID;
                        lead.GroupCode = lead.GroupID > 0 ? lead.GroupID.ToString() : string.Empty;

                        AllocationDLL.DumpLeadDetails(lead, "SMEAllocation");

                        if (lead.InvestmentTypeID == 19 && subProductGroupLeads != null && subProductGroupLeads.Count > 0 &&
                            (lead.LeadSource == "PB" || lead.LeadSource == "PBMobile")
                            )
                        {
                            totalAssignedLeads = totalAssignedLeads + 1;
                            foreach (var groupLead in subProductGroupLeads)
                            {
                                if (groupLead.GroupId == lead.GroupID)
                                {
                                    groupLead.LeadsAllocated = groupLead.LeadsAllocated + 1;
                                    break;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    var err = ex.ToString();
                    leadErrors.TryAdd(lead.LeadId, err);
                }
            }
        }

        internal static List<GroupLeads> GetGroupLeadsCount()
        {
            List<GroupLeads> groupLeadsCount = null;
            try
            {
                DataSet data = AllocationDLL.GetGroupLeadsCount();
                if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                {
                    groupLeadsCount = (from dr in data.Tables[0].AsEnumerable()
                                       select new GroupLeads
                                       {
                                           GroupId = dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt32(dr["GroupId"]) : default,
                                           LeadsAllocated = dr["LeadsAllocated"] != null && dr["LeadsAllocated"] != DBNull.Value ? Convert.ToInt32(dr["LeadsAllocated"]) : default,
                                           GroupAllocationPercent = dr["GroupAllocationPercent"] != null && dr["GroupAllocationPercent"] != DBNull.Value ? Convert.ToInt32(dr["GroupAllocationPercent"]) : default
                                       }).ToList();

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetGroupLeadsCount", "Allocation", "SMEAllocation", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return groupLeadsCount;
        }

        private static short GetLeadGroup(LeadDetails lead, List<GroupLeads> subProductGroupLeads, int totalAssignedLeads)
        {
            var groupId = lead.GroupID;
            if (lead.InvestmentTypeID == 19 && (lead.LeadSource == "PB" || lead.LeadSource == "PBMobile"))
            {
                if (subProductGroupLeads.Count == 1)
                {
                    groupId = (short)subProductGroupLeads[0].GroupId;
                }
                else
                {
                    foreach (var group in subProductGroupLeads)
                    {
                        double result = (Convert.ToDouble(group.LeadsAllocated) / Convert.ToDouble(totalAssignedLeads)) * 100;
                        if (result > group.GroupAllocationPercent)
                        {
                            continue;
                        }
                        else
                        {
                            groupId = (short)group.GroupId;
                        }
                        break;
                    }
                }
            }
            return groupId;
        }

        private static short GetLeadRankFromEngine(LeadDetails lead, Dictionary<long, string> leadErrors)
        {
            short leadRank = lead.LeadRank;
            try
            {
                // Fetch custom utm to use further for lead ranking
                lead.Custom_UTM = HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.ADHOC_CustomUTM, lead);

                if (HealthLeadRankRuleEngine.Execute(SmeEngines.WC, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.WC_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PIForDoc, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.PIForDocLeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.Marine, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.Marine_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GTL, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.GTL_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GHI, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.GHI_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PI_For_Company, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.PIForCompanyLeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PI_Medical_Establishment, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.PIMedicalLeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GeneralLiability, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.GL_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.DirectorsOfficers, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.DirectorsOfficersLeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CyberRisk, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.CyberRisk_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.EAR, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.EAR_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CAR, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.CAR_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.ShopOwner, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.ShopOwner_LeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.OfficePackage, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.OfficePackageLeadRank, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.Fire, lead) == "true")
                {
                    leadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeLeadRankEngines.Fire_LeadRank, lead));
                }

                if (leadRank == -1)
                {
                    leadRank = 0;
                }
            }
            catch (Exception ex)
            {
                var err = " GetLeadRankFromEngine" + " - " + ex.Message;
                leadErrors.TryAdd(lead.LeadId, err);
            }
            return leadRank;
        }

        internal static short GetGroupIdFromEngine(LeadDetails lead, Dictionary<long, string> leadErrors)
        {
            short groupId = 0;
            try
            {
                if (HealthLeadRankRuleEngine.Execute(SmeEngines.WC, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.WC_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PIForDoc, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.PIForDoc_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.Marine, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.Marine_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.OPD, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.OPD_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.KeyMan, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.KeyMan_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GroupTravel, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GroupTravel_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GroupTotalProtect, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GroupTotalProtect_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GroupGratuity, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GroupGratuity_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GroupCareCovid19, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GroupCareCovid19_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GTL, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GTL_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GPA, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GPA_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GHI, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GHI_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PublicLiability, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.PublicLiability_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PI_For_Company, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.PI_For_Company_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.PI_Medical_Establishment, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.PI_Medical_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.GeneralLiability, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.GeneralLiability_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.DroneInsurance, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.DroneInsurance_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.DirectorsOfficers, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.DirectorsOfficers_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CyberRisk, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.CyberRisk_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CommercialCrime, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.CommercialCrime_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.EAR, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.EAR_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CPM, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.CPM_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CAR, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.CAR_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.ShopOwner, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.ShopOwner_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.OfficePackage, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.OfficePackage_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.Fire, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.Fire_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.Burglary, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.Burglary_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.CyberRiskIndividual, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.CyberRiskIndividual_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.SuretyBond, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.SuretyBond_Group, lead));
                }
                else if (HealthLeadRankRuleEngine.Execute(SmeEngines.TradeCredit, lead) == "true")
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.TradeCredit_Group, lead));
                }

                if (groupId <= 0)
                {
                    groupId = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(SmeEngines.ADHOC, lead));
                }

                if (groupId == -1)
                {
                    groupId = 0;
                }
            }
            catch (Exception ex)
            {
                var err = " GetGroupIdFromEngine" + " - " + ex.Message;
                leadErrors.TryAdd(lead.LeadId, err);
            }
            return groupId;
        }

        internal static void AssignLead(LeadDetails lead)
        {
            AllocationDLL.AssignSMELead(lead);
        }

        internal static void GetAgentAllocation(LeadDetails lead)
        {
            DataSet data = AllocationDLL.GetAgentAllocation(lead);
            if (data != null && data.Tables != null && data.Tables[0] != null && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
            {
                var row = data.Tables[0].Rows[0];
                lead.JobID = row["JobID"] != DBNull.Value ? Convert.ToInt16(row["JobID"]) : lead.JobID;
                lead.UserID = row["UserID"] != DBNull.Value ? Convert.ToInt64(row["UserID"]) : lead.UserID;
                lead.GroupID = row["GroupId"] != DBNull.Value ? Convert.ToInt16(row["GroupId"]) : lead.GroupID;
                lead.AgentGrade = row["AgentGrade"] != DBNull.Value ? Convert.ToInt16(row["AgentGrade"]) : lead.AgentGrade;
                lead.LeadRank = row["LeadRank"] != DBNull.Value ? Convert.ToInt16(row["LeadRank"]) : lead.LeadRank;
                lead.IsAgentGrading = row["IsAgentGrading"] != DBNull.Value ? Convert.ToBoolean(row["IsAgentGrading"]) : lead.IsAgentGrading;
            }
        }

        internal static void UpdateAgentsDataForAllocation()
        {
            AllocationDLL.UpdateAgentsDataForAllocation();
        }

        internal static void UpdateAgentGrade()
        {
            AllocationDLL.UpdateAgentGrade();
        }

    }
}