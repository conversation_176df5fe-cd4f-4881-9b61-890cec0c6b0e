﻿using DataAccessLayer;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace EmailCommunicationBLL
{
    public class SMEBLL : ISMEBLL
    {
        public AssignedLeadData AssignLeadToAgent(long leadId)
        {
            var result = new AssignedLeadData();
            try
            {
                if (leadId > 0)
                {
                    var smeLeads = new List<LeadDetails>();
                    var leadErrors = new Dictionary<long, string>();

                    GetSMELeadsForAllocation(smeLeads, leadId);

                    if (smeLeads.Count > 0)
                    {
                        result.AssignedAgent = smeLeads[0].AssignedToAgent;
                        result.ParentId = smeLeads[0].ParentId;
                        if (result.AssignedAgent == 0 && new[] { 1, 2, 3, 4, 11 }.Contains(smeLeads[0].StatusId))
                        {
                            PopulateDataAndAllocateLead(smeLeads, leadErrors);
                            result.AssignedAgent = AllocationDLL.GetLeadAssignedAgent(leadId);
                        }
                        result.LeadId = leadId;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(leadId), leadId, ex.ToString(), "AssignLeadToAgent", "Allocation", "SMEAllocation", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        internal void PopulateDataAndAllocateLead(List<LeadDetails> leadList, Dictionary<long, string> leadErrors)
        {
            foreach (LeadDetails lead in leadList)
            {
                try
                {
                    SMEAllocation.PopulateLeadAdditionalDetails(lead);
                    if (!lead.IsFos && !lead.IsChurn)
                    {
                        //lead.LeadRank = GetLeadRankFromEngine(lead, leadErrors);
                        lead.GroupID = SMEAllocation.GetGroupIdFromEngine(lead, leadErrors);
                        lead.LeadRank = AllocationDLL.GetSmeLeadRank(lead);
                    }
                    SMEAllocation.GetAgentAllocation(lead);

                    if (lead.IsAllocable)
                    {
                        SMEAllocation.AssignLead(lead);

                        lead.AssigntoUserID = lead.UserID;
                        lead.GroupCode = lead.GroupID > 0 ? lead.GroupID.ToString() : string.Empty;

                        AllocationDLL.DumpLeadDetails(lead, "SMEAllocationAPI");
                    }
                }
                catch (Exception ex)
                {
                    var err = ex.ToString();
                    leadErrors.TryAdd(lead.LeadId, err);
                }
            }
        }

        internal void GetSMELeadsForAllocation(List<LeadDetails> leadList, long LeadId)
        {
            DataSet leadsDataSet = AllocationDLL.GetSMELeadsForAllocation(LeadId);

            if (leadsDataSet != null && leadsDataSet.Tables != null && leadsDataSet.Tables[0].Rows != null && leadsDataSet.Tables[0].Rows.Count > 0)
            {
                leadList.Clear();
                foreach (DataRow leadRow in leadsDataSet.Tables[0].Rows)
                {
                    LeadDetails leadData = new()
                    {
                        LeadId = (long)leadRow["LeadID"],
                        MobileNo = leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : default,
                        LeadRank = leadRow["LeadRank"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadRank"]) : default,
                        CreatedOn = leadRow["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(leadRow["CreatedOn"]) : DateTime.MinValue,
                        CustomerId = leadRow["CustId"] == DBNull.Value ? default : (long)leadRow["CustId"],
                        StatusId = leadRow["StatusID"] != DBNull.Value ? (byte)leadRow["StatusID"] : (byte)0,
                        SubStatusId = leadRow["SubStatusID"] != DBNull.Value ? Convert.ToInt16(leadRow["SubStatusID"]) : (short)0,
                        IsAllocable = leadRow["IsAllocable"] != DBNull.Value && Convert.ToBoolean(leadRow["IsAllocable"]),
                        PolicyExpiryDate = leadRow["policyexpirydate"] != DBNull.Value ? Convert.ToDateTime(leadRow["policyexpirydate"]) : null,
                        GroupID = leadRow["GroupID"] != DBNull.Value ? Convert.ToInt16(leadRow["GroupID"]) : (short)0,
                        LeadGrade = leadRow["LeadGrade"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadGrade"]) : (short)0,
                        InvestmentTypeID = leadRow["InvestmentTypeID"] != DBNull.Value ? Convert.ToInt16(leadRow["InvestmentTypeID"]) : (short)0,
                        IsChurn = leadRow["IsChurn"] != DBNull.Value && Convert.ToBoolean(leadRow["IsChurn"]),
                        EmployeeID = leadRow["EmployeeID"] == DBNull.Value ? string.Empty : leadRow["EmployeeID"].ToString(),
                        SA = leadRow["SA"] == DBNull.Value ? default : Convert.ToDecimal(leadRow["SA"]),
                        CityID = leadRow["CityID"] == DBNull.Value ? default : Convert.ToInt16(leadRow["CityID"]),
                        Utm_source = leadRow["Utm_source"] == DBNull.Value ? string.Empty : leadRow["Utm_source"].ToString(),
                        UTM_Medium = leadRow["Utm_medium"] == DBNull.Value ? string.Empty : leadRow["Utm_medium"].ToString(),
                        LeadSource = leadRow["LeadSource"] == DBNull.Value ? string.Empty : leadRow["LeadSource"].ToString(),
                        Country = leadRow["Country"] == DBNull.Value ? string.Empty : leadRow["Country"].ToString(),
                        Utm_term = leadRow["Utm_term"] == DBNull.Value ? string.Empty : leadRow["Utm_term"].ToString(),
                        source = leadRow["Source"] == DBNull.Value ? string.Empty : leadRow["Source"].ToString(),
                        TypeOfPolicy = leadRow["TypeofPolicy"] == DBNull.Value ? string.Empty : leadRow["TypeofPolicy"].ToString(),
                        UserID = leadRow["UserID"] == DBNull.Value ? default : Convert.ToInt64(leadRow["UserID"]),
                        JobID = leadRow["JobID"] == DBNull.Value ? default : Convert.ToInt16(leadRow["JobID"]),
                        Utm_campaign = leadRow["Utm_campaign"] == DBNull.Value ? string.Empty : leadRow["Utm_campaign"].ToString(),
                        ReferralId = leadRow["referralid"] == DBNull.Value ? default : Convert.ToInt64(leadRow["referralid"]),
                        IsFos = leadRow["IsFos"] != DBNull.Value && Convert.ToBoolean(leadRow["IsFos"]),
                        CPMRTO = leadRow["CPMRTO"] != DBNull.Value && Convert.ToBoolean(leadRow["CPMRTO"]),
                        ContinuePQ = leadRow["ContinuePQ"] == DBNull.Value ? null : Convert.ToDateTime(leadRow["ContinuePQ"]),
                        RolloverCheck = leadRow["RolloverCheck"] == DBNull.Value ? null : Convert.ToInt32(leadRow["RolloverCheck"]),
                        TotalNoOfLives = leadRow["TotalNoOfLives"] == DBNull.Value ? null : Convert.ToInt32(leadRow["TotalNoOfLives"]),
                        CustomerType = leadRow["CustomerType"] == DBNull.Value ? default : Convert.ToString(leadRow["CustomerType"]),
                        ProductID = leadRow["ProductId"] == DBNull.Value ? default : Convert.ToInt16(leadRow["ProductId"]),
                        IsCMB = leadRow["IsCMB"] != DBNull.Value && Convert.ToBoolean(leadRow["IsCMB"]),
                        IsCPED = leadRow["IsCPED"] != DBNull.Value && Convert.ToBoolean(leadRow["IsCPED"]),
                        PolicyType = leadRow["PolicyType"] != DBNull.Value ? Convert.ToInt16(leadRow["PolicyType"]) : default,
                        EmployeeRange = leadRow["EmployeeRange"] != DBNull.Value ? Convert.ToString(leadRow["EmployeeRange"]) : default,
                        OccupancyId = leadRow["OccupancyId"] != DBNull.Value ? Convert.ToInt32(leadRow["OccupancyId"]) : default,
                        TotalNoOFEmployees = leadRow["TotalNoOFEmployees"] != DBNull.Value ? Convert.ToInt32(leadRow["TotalNoOFEmployees"]) : default,
                        TransitType = leadRow["TransitType"] != DBNull.Value ? Convert.ToString(leadRow["TransitType"]) : default,
                        LeadScore = leadRow["LeadScore"] != DBNull.Value ? Convert.ToDecimal(leadRow["LeadScore"]) : default,
                        AssignedToAgent = leadRow["AssignedAgent"] != DBNull.Value ? Convert.ToInt64(leadRow["AssignedAgent"]) : default,
                        ParentId = leadRow["ParentId"] != DBNull.Value ? Convert.ToInt64(leadRow["ParentId"]) : default
                    };

                    // NRI lead flag: IsNRI
                    if (!AllocationBLL.IndiaCodes.Contains(leadData.Country))
                    {
                        leadData.IsNRI = true;
                    }

                    leadList.Add(leadData);
                }
            }
        }
    }
}
