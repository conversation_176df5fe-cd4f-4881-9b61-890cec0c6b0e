﻿using Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace EmailCommunicationBLL
{
   

    public class MigrateUnsubscriptionData
    {
        private readonly System.Timers.Timer _timer;
        private readonly int interval;
        private readonly short intervalInMinutes;

        public MigrateUnsubscriptionData()
        {
            intervalInMinutes = 10;
            interval = intervalInMinutes * 60 * 1000; // Interval in milliseconds (2 Minute)
            _timer = new(interval);
            _timer.Elapsed += Timer_Elapsed;
            _timer.Start();
        }


        private void Timer_Elapsed(object? sender, ElapsedEventArgs e)
        {
            if (CoreCommonMethods.IsDevMode())
            {
                //donot run scheduler on local
                return;
            }
            //Stop and start timer to prevent the database call overlap
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();

            try
            {
                _timer.Stop();
                CustomerdetailBLL.FetchCustomerForUnsubscriptionDayWise();
              
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "MigrateUnsubscriptionData", "Allocation", "Scheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
                _timer.Start();
            }
        }
    }
}
