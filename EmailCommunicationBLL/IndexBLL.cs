﻿using Newtonsoft.Json;
using System.Runtime.Caching;

namespace EmailCommunicationBLL
{
    public class IndexBLL : IIndexBLL
    {
        public static string ClearCache(string key)
        {
            if (key != "")
            {
                ObjectCache memcache = MemoryCache.Default;
                return JsonConvert.SerializeObject(memcache.Remove(key));
            }
            return "Some Error Occured";

        }
    }

}

