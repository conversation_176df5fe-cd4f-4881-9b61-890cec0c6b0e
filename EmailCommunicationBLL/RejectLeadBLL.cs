﻿using DataAccessLayer;
using System;
using System.Collections.Generic;
using System.Data;



namespace EmailCommunicationBLL
{
    public class RejectLeadBLL: IRejectLeadBLL
    {
        public static void RejectPredictiveLeads(List<string> AllRjectedLeadSP)
        {
            DateTime dt = DateTime.Now;
            string exception = string.Empty;
            try
            {
                for (int day = 90; day >= 1; day--)
                {
                    try
                    {
                        foreach (string SPName in AllRjectedLeadSP)
                        {
                            try
                            {
                                DataSet ds = AllocationDLL.GetPredectiveLeadsRejection(day, SPName);
                                if (ds != null && ds.Tables[0].Rows.Count != 0)
                                {
                                    foreach (DataRow leadRow in ds.Tables[0].Rows)
                                    {
                                        Int64 LeadID = leadRow["LeadID"] == null || leadRow["LeadID"] == DBNull.Value ? (Int64)0 : (Int64)leadRow["LeadID"];
                                        try
                                        {
                                            Int16 StatusId = leadRow["StatusId"] == null || leadRow["StatusId"] == DBNull.Value ? (Int16)0 : (Int16)leadRow["StatusId"];
                                            Int16 ProductId = leadRow["ProductID"] == null || leadRow["ProductID"] == DBNull.Value ? (Int16)0 : (Int16)leadRow["ProductID"];
                                            string Reason = leadRow["Reason"] == null || leadRow["Reason"] == DBNull.Value ? "" : (string)leadRow["Reason"];
                                            AllocationDLL.RejectLeads(LeadID, StatusId, ProductId, Reason);
                                        }
                                        catch (Exception ex)
                                        {
                                            LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "RejectPredictiveLeads_LeadId", "Allocation", "", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                                        }
                                    }
                                }
                            }
                           catch(Exception ex)
                            {
                                LoggingHelper.LoggingHelper.AddloginQueue("",day, ex.ToString(), "RejectPredictiveLeads_SPName", "Allocation", "", SPName, string.Empty, DateTime.Now, DateTime.Now);
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue("", day, ex.ToString(), "RejectPredictiveLeads_day", "Allocation", "", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                    }
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, exception, "RejectPredictiveLeads", "Allocation", "", string.Empty, string.Empty, dt, DateTime.Now);
            }


        }
    }
}
