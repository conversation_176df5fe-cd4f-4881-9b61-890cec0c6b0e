﻿using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class TwoWheelerRejectionHelper
    {
        public static bool RejectUnAssignedTwoWheeler(ParentDetails ParentLead)
        {
            
            
            
            List<Int16> StatusIDList = new List<Int16> { 1, 2 };
            

            if (RejectionHelper.IsDateWithinRange(5, 10, ParentLead.CreatedOn)
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                )
            {
                return true;
            }
            return false;
        }
    }
}
