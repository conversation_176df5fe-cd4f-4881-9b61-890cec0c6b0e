﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class SMERejectionHelper
    {

        

        public static bool Reject250DaysOlderSME(ParentDetails ParentLead)
        {
            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate" };

            if (RejectionHelper.IsDateWithinRange(250, 255, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings)))
            {
                return true;
            }

            return false;
        }

        public static bool RejectSMELeadsLogic2(ParentDetails ParentLead)
        {

            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF", "RENEWAL" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate" };
            List<Int16> StatusIDList = new List<Int16> {1,2,3};
            List<Int16> InvestmentIDList1 = new List<Int16> { 5, 7, 8, 13, 16, 17, 18, 19, 21 };
            List<Int16> InvestmentIDList2 = new List<Int16> { 1, 2, 12, 14, 15, 20, 24, 25 };

            if (((RejectionHelper.IsDateWithinRange(60, 250, ParentLead.CreatedOn) && RejectionHelper.CompareInts(ParentLead.InvestmentTypeID,InvestmentIDList1)) 
                || (RejectionHelper.IsDateWithinRange(90, 250, ParentLead.CreatedOn) && RejectionHelper.CompareInts(ParentLead.InvestmentTypeID, InvestmentIDList2))) 
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings)) 
                && RejectionHelper.CompareInts(ParentLead.StatusID,StatusIDList))
            {
                return true;
            }
            return false;
        }

        public static bool RejectSMELeadsLogic3(ParentDetails ParentLead)
        {

            List<string> LeadSourceStrings = new List<string> { "RENEWAL" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2, 3 };
            List<Int16> InvestmentIDList1 = new List<Int16> { 5, 7, 8, 13, 16, 17, 18, 19, 21 };
            List<Int16> InvestmentIDList2 = new List<Int16> { 1, 2, 12, 14, 15, 20, 24, 25 };

            if (((RejectionHelper.IsDateWithinRange(90, 250, ParentLead.CreatedOn) && RejectionHelper.CompareInts(ParentLead.InvestmentTypeID, InvestmentIDList1))
                || (RejectionHelper.IsDateWithinRange(120, 250, ParentLead.CreatedOn) && RejectionHelper.CompareInts(ParentLead.InvestmentTypeID, InvestmentIDList2)))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings))
                && (RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
            {
                return true;
            }
            return false;
        }
    }
}
