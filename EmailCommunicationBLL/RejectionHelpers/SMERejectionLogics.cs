﻿using PropertyLayers;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class SMERejectionLogics
    {
        public bool Evaluate(ParentDetails ParentLead)
        {
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                return false;
            }

            

            if (SMERejectionHelper.Reject250DaysOlderSME(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan250days";
                return true;
            }


            if (SMERejectionHelper.RejectSMELeadsLogic2(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan60days";
                return true;
            }

            if (SMERejectionHelper.RejectSMELeadsLogic3(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan60days";
                return true;
            }
            


            return false;
        }
    }
}
