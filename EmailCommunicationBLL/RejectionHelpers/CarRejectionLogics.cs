﻿using PropertyLayers;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class CarRejectionLogics
    {
        public bool Evaluate(ParentDetails ParentLead)
        {
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                return false;
            }

            
            if (CarRejectionHelper.RejectNewAndValidCarLeads(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan40days";
                return true;
            }

            if (CarRejectionHelper.Reject60DaysOlderNewCarleads(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan40days";
                return true;
            }

            if(CarRejectionHelper.RejectHomChurnLeads(ParentLead))
            {

                ParentLead.Reason = "RejectLeadsHomChurnLead";
                return true;


            }
            


            if (CarRejectionHelper.RejectUnAssignedCarLeads(ParentLead))
            {

                ParentLead.Reason = "Un assigned leads";
                return true;


            }


            return false;
        

            
        }
    }
}
