﻿using PropertyLayers;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class InvestmentRejectionLogics
    {
        public bool Evaluate(ParentDetails ParentLead)
        {
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate)) 
            {
                return false;
            } 

            if (InvestmentRejectionHelper.Reject90DaysOlderLead(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan90days";
                return true;
            }

            if (InvestmentRejectionHelper.RejectUnAssignedInvestmentLeads(ParentLead))
            {
                ParentLead.Reason = "Un assigned leads";
                return true;
            }

            return false;
        }
    }
}

















