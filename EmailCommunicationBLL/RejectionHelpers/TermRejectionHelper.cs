﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class TermRejectionHelper
    {
        public static bool RejectTermNRILeads(ParentDetails ParentLead)
        {

            //Check for ParentID is NULL
            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2, 3, 4, 11 };

            if (RejectionHelper.IsDateWithinRange(0, 1, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings))
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                && ParentLead.LeadRank == 247)
            {
                return true;
            }
            return false;
        }


        public static bool RejectUnAssignedTermLogic1(ParentDetails ParentLead)
        {

            //No check for LD.ParentID is NULL
            List<string> LeadSourceStrings = new List<string> {"PBMOBILE", "PB", "PBMOBILEAPP", "WHATSAPP" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2};

            if (RejectionHelper.IsDateWithinRange(0, 1, ParentLead.CreatedOn)
                && (RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings) || ParentLead.LeadSource.Contains("health", StringComparison.OrdinalIgnoreCase))
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                && ParentLead.LeadRank == 170)
            {
                return true;
            }
            return false;
        }

        public static bool RejectUnAssignedTermLogic2(ParentDetails ParentLead)
        {

            //Check for LD.ParentID is NULL
            List<string> LeadSourceStrings = new List<string> { "PBMOBILE", "PB", "PBMOBILEAPP", "WHATSAPP" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2 };
            List<Int16> LeadRankList = new List<Int16> { 1, 2, 3, 4, 11, 12, 13, 14, 20, 21, 22, 23, 24, 41, 42, 43, 44, 51, 52, 53, 54, 81, 82, 83, 91, 92, 101, 102, 103, 104, 31, 32, 33, 34, 35, 36, 121, 122, 123, 124, 125, 88 };

            if (RejectionHelper.IsDateWithinRange(3, 15, ParentLead.CreatedOn)
                && (RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings) || ParentLead.LeadSource.Contains("health", StringComparison.OrdinalIgnoreCase))
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                && !(RejectionHelper.CompareInts(ParentLead.LeadRank, LeadRankList)))
            {
                return true;
            }
            return false;
        }

        public static bool RejectUnAssignedTermLogic3(ParentDetails ParentLead)
        {

            //No check for LD.ParentID is NULL
            List<string> LeadSourceStrings = new List<string> { "RENEWAL" };
            List<Int16> StatusIDList = new List<Int16> { 1, 2 };
            

            if (RejectionHelper.IsDateWithinRange(5, 10, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && (ParentLead.LeadAssignData.Count == 0)
                && (RejectionHelper.CompareInts(ParentLead.StatusID, StatusIDList))
                )
            {
                return true;
            }
            return false;
        }




    }
}
