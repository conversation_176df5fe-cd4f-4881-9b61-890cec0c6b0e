﻿using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class TwoWheelerRejectionLogics
    {
        public bool Evaluate(ParentDetails ParentLead)
        {
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                return false;
            }

            if (TwoWheelerRejectionHelper.RejectUnAssignedTwoWheeler(ParentLead))
            {
                ParentLead.Reason = "Un assigned leads";
                return true;
            }
            return false;
        }
    }
}
