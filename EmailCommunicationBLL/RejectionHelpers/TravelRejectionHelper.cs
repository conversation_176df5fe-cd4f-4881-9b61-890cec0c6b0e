﻿using PropertyLayers;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class TravelRejectionHelper
    {

        public static bool Reject120DaysOlderTravel(ParentDetails ParentLead)
        {
            List<string> LeadSourceStrings = new List<string> { "ACAFFAPP", "ACAFF" };
            List<string> UTM_SourceStrings = new List<string> { "OfflineAffiliate" };

            if (RejectionHelper.IsDateWithinRange(120, 150, ParentLead.CreatedOn)
                && !(RejectionHelper.CompareStrings(ParentLead.LeadSource, LeadSourceStrings))
                && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, UTM_SourceStrings)))
            {
                return true;
            }

            return false;
        }
    }
}
