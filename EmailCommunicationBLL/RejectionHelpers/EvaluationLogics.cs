﻿using Helper;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class EvaluationLogics : ILeadRejectionLogics
    {
        public bool GenericEvaluate(ParentDetails ParentLead, LeadRejectionLogicsDocument doc)
        {
            bool IsReject = true;

            if (doc.SkipCallbackChk != true &&  ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                IsReject = false;
                return IsReject;
            }

            if (doc.SkipCallbackChk != true && RejectionHelper.IfFutureAppointmentExist(ParentLead.AppointmentDateTime))
            {
                IsReject = false;
                return IsReject;
            }

            if (!doc.IsActive || doc.ProductId != ParentLead.ProductId) return false;

            if (doc.DontRejectChildWithLeadSource != null)
                ParentLead.DontRejectChildWithLeadSource = new List<string>(doc.DontRejectChildWithLeadSource);
            if (doc.DontRejectChildWithUtmSource != null)
                ParentLead.DontRejectChildWithUtmSource = new List<string>(doc.DontRejectChildWithUtmSource);




            foreach (var condition in doc.Conditions)
            {
                var _IsReject = false;
                switch (condition.Property)
                {
                    case "LeadCreatedOn":

                        if (RejectionHelper.IsDateWithinRange(condition.MinDays, condition.MaxDays, ParentLead.CreatedOn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "LastCallDate":

                        if (ParentLead.CallHistory == null || ParentLead.CallHistory.Count == 0 || !(RejectionHelper.IsDateWithinRange(condition.MinDays, condition.MaxDays, ParentLead.LastCallDate)))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "LeadSource":
                        if (condition.StrNotIn!=null &&  !(RejectionHelper.CompareStrings(ParentLead.LeadSource, condition.StrNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.StrIn != null && RejectionHelper.CompareStrings(ParentLead.LeadSource, condition.StrIn))
                        {
                            _IsReject = true;
                        }

                        if ((condition.StrLike != null && condition.StrLike != "") && CoreCommonMethods.Like(ParentLead.LeadSource, condition.StrLike))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "UTM_Source":
                        if (condition.StrNotIn != null && !(RejectionHelper.CompareStrings(ParentLead.UTM_Source, condition.StrNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.StrIn != null && RejectionHelper.CompareStrings(ParentLead.UTM_Source, condition.StrIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "StatusID":
                        if (condition.IntNotIn != null && !(RejectionHelper.CompareInts(ParentLead.StatusID, condition.IntNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.IntIn != null && RejectionHelper.CompareInts(ParentLead.StatusID, condition.IntIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "IsLeadUnassigned": 
                        if (ParentLead.LeadAssignData[0].AssignedDate == DateTime.MinValue) _IsReject = true;
                        break;

                    case "LeadAssignedDate":
                        if (ParentLead.LeadAssignData.Count != 0 && RejectionHelper.IsDateWithinRange(condition.MinDays, condition.MaxDays, ParentLead.LeadAssignData[0].AssignedDate)) _IsReject = true;
                        break;

                    case "AssignedToGroupID":
                        if (ParentLead.LeadAssignData.Count != 0 && condition.IntNotIn != null && condition.NotInQueryInt != 0
                            && !(RejectionHelper.CompareInts(ParentLead.LeadAssignData[0].AssignToGroupId, LeadRejectionBLL.GetAssignToGroupIDs(condition.NotInQueryInt)))
                            && !(RejectionHelper.CompareInts(ParentLead.LeadAssignData[0].AssignToGroupId, condition.IntNotIn)))
                        {
                            _IsReject = true;
                        }

                        if(ParentLead.LeadAssignData.Count != 0 && condition.IntIn != null && RejectionHelper.CompareInts(ParentLead.LeadAssignData[0].AssignToGroupId, condition.IntIn)) 
                            _IsReject = true;

                        if (ParentLead.LeadAssignData.Count != 0 && condition.IntNotIn != null && !(RejectionHelper.CompareInts(ParentLead.LeadAssignData[0].AssignToGroupId, condition.IntNotIn)))
                            _IsReject = true;
                        break;

                    case "InvestmentTypeID":
                        if (condition.IntIn != null && RejectionHelper.CompareInts(ParentLead.InvestmentTypeID, condition.IntIn)) 
                            _IsReject = true;
                        if (condition.IntNotIn != null && !(RejectionHelper.CompareInts(ParentLead.InvestmentTypeID, condition.IntNotIn)))
                            _IsReject = true;
                        break;

                    case "LeadRank":
                        if (condition.IntIn != null && RejectionHelper.CompareInts(ParentLead.LeadRank, condition.IntIn)) 
                            _IsReject = true;
                        if (condition.IntNotIn != null && !(RejectionHelper.CompareInts(ParentLead.LeadRank, condition.IntNotIn)))
                            _IsReject = true;
                        break;

                    case "TotalTalkTime":
                        if (condition.IntIn != null && ParentLead.TotalTalkTime <= condition.IntIn[0])
                        {
                            _IsReject = true;
                        }
                        break;

                    case "Country":
                        if (condition.StrNotIn != null && !(RejectionHelper.CompareStrings(ParentLead.Country, condition.StrNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.StrIn != null && RejectionHelper.CompareStrings(ParentLead.Country, condition.StrIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "PaymentStatus":
                        if (condition.IntNotIn != null && !(RejectionHelper.CompareInts(Convert.ToInt16(ParentLead.PaymentStatus), condition.IntNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.IntIn != null && RejectionHelper.CompareInts(Convert.ToInt16(ParentLead.PaymentStatus), condition.IntIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "OfferCreatedOn":
                        if (ParentLead.OfferCreatedOn != DateTime.MinValue)
                            _IsReject = true;

                        break;

                    case "TravelStartDateExpired":
                        if (ParentLead.TravelStartDate != DateTime.MinValue && ParentLead.TravelStartDate < DateTime.Now.Date)
                            _IsReject = true;
                        break;

                    case "CallCount":
                        if (condition.IntIn != null && ParentLead.CallHistory.Count < condition.IntIn[0])
                            _IsReject = true;
                        break;

                    case "TotalTalkTimeInLastNDays":
                        if (condition.IntIn != null && ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0
                            && RejectionHelper.TalkTimeInLastNDays(ParentLead.CallHistory,condition.MaxDays) <= condition.IntIn[0])
                            _IsReject = true;
                        break;

                    case "CallCountInLastNDays":
                        if (condition.IntIn != null && ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0
                            && RejectionHelper.CallCountInLastNDays(ParentLead.CallHistory, condition.MaxDays) >= condition.IntIn[0])
                            _IsReject = true;
                        break;

                    case "TotalCallCount":
                        if (condition.IntIn != null && ParentLead.TotalCallCount >= condition.IntIn[0])
                            _IsReject = true;
                        break;

                    case "UTM_Term":
                        if (condition.StrNotIn != null && !(RejectionHelper.CompareStrings(ParentLead.UTM_Term, condition.StrNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.StrIn != null && RejectionHelper.CompareStrings(ParentLead.UTM_Term, condition.StrIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "Source":
                        if (condition.StrNotIn != null && !(RejectionHelper.CompareStrings(ParentLead.Source, condition.StrNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.StrIn != null && RejectionHelper.CompareStrings(ParentLead.Source, condition.StrIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "PolicyExpiryDate":
                        if (ParentLead.PolicyExpiryDate != DateTime.MinValue && RejectionHelper.IsDateWithinRange(condition.MinDays, condition.MaxDays, ParentLead.PolicyExpiryDate))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "CountValidNotAnsweredCalls":
                        if (condition.IntIn != null && ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0
                            && RejectionHelper.CountValidNotAnsweredCalls(ParentLead.CallHistory) > condition.IntIn[0])
                        {
                            _IsReject = true;
                        }
                        break;

                    case "CountValidNotAnsweredCalls_RENEWAL":
                        if (ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0 &&
                            RejectionHelper.CountValidNotAnsweredCalls_RENEWAL(ParentLead.CallHistory, ParentLead) > GetMinCallCount(doc.Cases, ParentLead))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "SME_GetValidCallNACount":
                        if (ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0 &&
                            RejectionHelper.CountValidNotAnsweredCalls(ParentLead.CallHistory) > GetMinCallCount_SME(doc.Cases, ParentLead))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "GetValidCallNACount":
                        if (ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0 
                            && RejectionHelper.CountValidNotAnsweredCalls(ParentLead.CallHistory) > GetMinCallCount(doc.Cases, ParentLead))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "CheckIfLastNValidCallsNotAnswered":
                        if (condition.IntIn != null && ParentLead.CallHistory != null && ParentLead.CallHistory.Count > 0
                            && RejectionHelper.CheckIfLastNValidCallsNotAnswered(ParentLead.CallHistory, condition.IntIn[0]))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "LeadIdDivisibleBy":
                        if (condition.IntIn != null && condition.IntIn.Count > 0 && ParentLead.ParentID % condition.IntIn[0] == 0)
                            _IsReject = true;
                        break;

                    case "GroupCode":
                        if ((condition.StrNotLike != null && condition.StrNotLike != "") && !(CoreCommonMethods.Like(ParentLead.LeadSource, condition.StrLike)))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "CustUnsubscribeCategoryId":
                        if (condition.IntNotIn != null && !RejectionHelper.CompareInts(Convert.ToInt16(ParentLead.CategoryId), condition.IntNotIn))
                        {
                            _IsReject = true;
                        }
                        if (condition.IntIn != null && RejectionHelper.CompareInts(Convert.ToInt16(ParentLead.CategoryId), condition.IntIn))
                        {
                            _IsReject = true;
                        }
                        break;
                    case "CustUnsubscribeChannelId":
                        if (condition.IntNotIn != null && !RejectionHelper.CompareInts(Convert.ToInt16(ParentLead.ChannelId), condition.IntNotIn))
                        {
                            _IsReject = true;
                        }
                        if (condition.IntIn != null && RejectionHelper.CompareInts(Convert.ToInt16(ParentLead.ChannelId), condition.IntIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    case "CustUnsubscribeCreatedOn":

                        if (RejectionHelper.IsDateWithinRange(condition.MinDays, condition.MaxDays, ParentLead.CustUnsubscribeCreatedOn))
                        {
                            _IsReject = true;
                        }
                        break;
                    case "IsPreviousPolicyDateAssumed0":

                        if (ParentLead.IsPreviousPolicyDateAssumed == false)
                        {
                            _IsReject = true;
                        }
                        break;

                    case "PolicyType":

                        if (condition.StrNotIn != null && !(RejectionHelper.CompareStrings(ParentLead.PolicyType, condition.StrNotIn)))
                        {
                            _IsReject = true;
                        }
                        if (condition.StrIn != null && RejectionHelper.CompareStrings(ParentLead.PolicyType, condition.StrIn))
                        {
                            _IsReject = true;
                        }
                        break;

                    default:
                        break;
        
                }

                if (!_IsReject)
                {
                    IsReject = false;
                    break;

                }
                


            }
            


            return IsReject;
        }

        public int GetMinCallCount_SME(List<SmeConditionsCase> Cases, ParentDetails ParentLead)
        {
            foreach(var SME_Case in Cases)
            {
                if (SME_Case.InvestmentTypeID == null && SME_Case.StatusID == null)
                    return SME_Case.Sum;

                if(RejectionHelper.CompareInts(ParentLead.InvestmentTypeID, SME_Case.InvestmentTypeID)
                    && RejectionHelper.CompareInts(ParentLead.StatusID, SME_Case.StatusID))
                {
                    return SME_Case.Sum;
                }

            }

            return 20;
        }

        public int GetMinCallCount(List<SmeConditionsCase> Cases, ParentDetails ParentLead)
        {
            foreach (var _Case in Cases)
            {
                if (_Case.StatusID == null)
                    return _Case.Sum;

                if (RejectionHelper.CompareInts(ParentLead.StatusID, _Case.StatusID))
                {
                    return _Case.Sum;
                }

            }

            return 20;
        }


    }
}


