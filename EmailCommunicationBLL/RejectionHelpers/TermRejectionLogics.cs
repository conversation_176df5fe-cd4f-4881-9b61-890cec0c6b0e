﻿using PropertyLayers;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class TermRejectionLogics
    {
        public bool Evaluate(ParentDetails ParentLead)
        {
            
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                return false;
            }

            
             if (TermRejectionHelper.RejectTermNRILeads(ParentLead))
             {
                 ParentLead.Reason = "RejectNRIbelow10thpassed";
                 return true;
             }

             if (TermRejectionHelper.RejectUnAssignedTermLogic2(ParentLead))
             {
                 ParentLead.Reason = "Un assigned leads";
                 return true;
             }


            

            if (TermRejectionHelper.RejectUnAssignedTermLogic1(ParentLead))
            {
                ParentLead.Reason = "Un assigned leads";               
                return true;
            }

            if (TermRejectionHelper.RejectUnAssignedTermLogic3(ParentLead))
            {
                ParentLead.Reason = "Un assigned leads";
                return true;
            }


            return false;
        }
    }
}
