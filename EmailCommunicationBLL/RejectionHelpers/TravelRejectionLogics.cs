﻿using PropertyLayers;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class TravelRejectionLogics
    {

        public bool Evaluate(ParentDetails ParentLead)
        {
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                return false;
            }


            if (TravelRejectionHelper.Reject120DaysOlderTravel(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan90days";
                return true;

            }

            return false;
        }
    }
}
