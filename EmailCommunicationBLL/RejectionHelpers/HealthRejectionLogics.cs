﻿using PropertyLayers;

namespace EmailCommunicationBLL.RejectionHelpers
{
    public class HealthRejectionLogics
    {
        public bool Evaluate(ParentDetails ParentLead)
        {
            if (ParentLead.CallBackData.Count > 0 && RejectionHelper.IfFutureCustomerCBExist(ParentLead.CallBackData[0].CallBackType, ParentLead.CallBackData[0].EventDate))
            {
                return false;
            }

            
            if (HealthRejectionHelper.RejectCorpLeadsOlderThan120days(ParentLead))
            {
                ParentLead.Reason = "RejectLeadsOlderThan120days-Corp";
                return true;
            }
            

            if (HealthRejectionHelper.RejectUnAssignedHealthLeads(ParentLead))
            {
                ParentLead.Reason = "Un assigned leads";
                return true;
            }



            return false;
        }
    }
}
