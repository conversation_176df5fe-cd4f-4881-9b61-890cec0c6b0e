
DECLARE @CustomerID BIGINT = 19223289 ;
WITH CustomerCallStats AS (
  SELECT 
    CustomerID,
    DATEPART(dw, CallDate) AS day_of_week,
    DATEPART(HOUR, CallDate) AS hour_of_day,
    CAST(SUM(CASE WHEN talktime > 1 THEN 1 ELSE 0 END) AS FLOAT) / 
    NULLIF(COUNT(1), 0) AS answer_rate,
    SUM(CASE WHEN talktime > 1 THEN 1 ELSE 0 END) AS answeredCount,
    SUM(CASE WHEN talktime > 1 THEN 0 ELSE 1 END) AS unansweredCount,
    COUNT(1) AS call_count
  FROM MTX.calldatahistory cdh
  INNER JOIN Matrix.crm.leaddetails ld on ld.LeadID= cdh.leadid
  WHERE ld.CustomerID=@CustomerID
  GROUP BY CustomerID, DATEPART(dw, CallDate), DATEPART(HOUR, CallDate)
)


SELECT 
TOP 100
  CustomerID,
  day_of_week,
  hour_of_day,
  answer_rate,
  call_count,
  unansweredCount,
  answeredCount
FROM CustomerCallStats
-- WHERE CustomerID = @CustomerID
ORDER BY answeredCount DESC, answer_rate DESC, call_count DESC;


-- DATA
CustomerID	day_of_week	hour_of_day	answer_rate	call_count	unansweredCount	answeredCount
19223289	7	11	0	24	10	14
19223289	6	15	0	15	4	11
19223289	3	10	0	14	3	11
19223289	5	11	0	14	4	10
19223289	5	18	0	24	15	9
19223289	2	12	0	17	8	9
19223289	4	15	0	13	4	9
19223289	7	15	0	24	16	8
19223289	7	17	0	20	12	8
19223289	1	11	0	18	10	8
19223289	6	13	0	18	10	8
19223289	5	15	0	17	9	8
19223289	1	12	0	16	8	8
19223289	7	12	0	17	10	7
19223289	5	17	0	15	8	7
19223289	1	15	0	14	7	7
19223289	6	11	0	13	6	7
19223289	4	13	0	12	5	7
19223289	6	10	0	13	7	6
19223289	2	14	0	13	7	6
19223289	4	16	0	13	7	6
19223289	2	18	0	12	6	6
19223289	5	16	0	12	6	6
19223289	3	12	0	11	5	6
19223289	5	10	0	11	5	6
19223289	6	14	0	11	5	6
19223289	6	12	0	9	3	6
19223289	4	14	0	8	2	6
19223289	4	19	0	23	18	5
19223289	7	18	0	20	15	5
19223289	3	16	0	18	13	5
19223289	4	12	0	15	10	5
19223289	5	14	0	14	9	5
19223289	4	17	0	12	7	5
19223289	2	10	0	11	6	5
19223289	2	16	0	10	5	5
19223289	5	13	0	10	5	5
19223289	3	17	0	9	4	5
19223289	2	11	0	17	13	4
19223289	6	18	0	13	9	4
19223289	6	16	0	13	9	4
19223289	7	19	0	11	7	4
19223289	3	18	0	14	11	3
19223289	7	14	0	12	9	3
19223289	6	17	0	11	8	3
19223289	1	16	0	10	7	3
19223289	7	16	0	10	7	3
19223289	7	10	0	8	5	3
19223289	1	13	0	8	5	3
19223289	3	13	0	7	4	3
19223289	1	18	0	6	3	3
19223289	5	19	0	6	3	3
19223289	4	10	0	6	3	3
19223289	1	10	0	4	1	3
19223289	3	11	0	20	18	2
19223289	2	17	0	16	14	2
19223289	2	15	0	9	7	2
19223289	4	18	0	9	7	2
19223289	3	15	0	8	6	2
19223289	1	17	0	7	5	2
19223289	7	9	0	5	3	2
19223289	6	20	0	5	3	2
19223289	2	19	0	4	2	2
19223289	4	9	1	1	0	1
19223289	7	20	1	1	0	1
19223289	3	9	1	1	0	1
19223289	1	19	1	1	0	1
19223289	6	21	1	1	0	1
19223289	3	19	0	15	14	1
19223289	5	12	0	10	9	1
19223289	2	13	0	9	8	1
19223289	7	13	0	8	7	1
19223289	2	20	0	6	5	1
19223289	4	11	0	6	5	1
19223289	3	14	0	4	3	1
19223289	3	8	0	9	9	0
19223289	4	20	0	3	3	0
19223289	6	19	0	2	2	0
19223289	1	14	0	2	2	0
19223289	3	20	0	2	2	0
19223289	6	9	0	2	2	0
19223289	5	20	0	1	1	0
19223289	2	21	0	1	1	0
19223289	1	9	0	1	1	0
19223289	7	21	0	1	1	0
19223289	3	7	0	1	1	0
