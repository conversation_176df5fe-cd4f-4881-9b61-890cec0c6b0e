﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using DataAccessLayer;
using MongoDB.Driver;
using PropertyLayers;
using MongoConfigProject;
using DataAccessLibrary;
using Newtonsoft.Json.Linq;
using System.Text;
using MongoDB.Driver.Linq;
using Helper;
using Newtonsoft.Json;
using System.Collections;

namespace EmailCommunicationBLL
{

    public class FOSAllocationBLL : IFOSAllocationBLL
    {

        public static List<CityZone> existingZones;
        public static decimal DistanceAPICallAsync(Decimal agentLong, Decimal agentLat, Decimal leadLong, Decimal leadLat, string Region)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                string URL = string.Empty;
                if (Region == "WEST")
                    URL = "westdistancecalcurl".AppSettings() + agentLong + "," + agentLat + ";" + leadLong + "," + leadLat;
                else if (Region == "SOUTH")
                    URL = "southdistancecalcurl".AppSettings() + agentLong + "," + agentLat + ";" + leadLong + "," + leadLat;
                else if (Region == "EAST")
                    URL = "eastdistancecalcurl".AppSettings() + agentLong + "," + agentLat + ";" + leadLong + "," + leadLat;
                else
                    URL = "distancecalcurl".AppSettings() + agentLong + "," + agentLat + ";" + leadLong + "," + leadLat;

                object response = CommonAPICall.CallAPI(URL, "", "GET", 3000, "application/json", null);
                var JsonResponse = JObject.Parse((string)response);
                var distance = JsonResponse["routes"][0]["distance"];
                if ((decimal)distance > 0)
                    return (decimal)distance / 1000;
                else
                    return 0;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "DistanceAPICallAsync", "Allocation", "FOSLeadAllocationScheduler", string.Empty, string.Empty, RequestDatetime, DateTime.Now);
                return 1000;
            }

        }
        public void FOSLeadAllocation(bool sameDayAllocation = false)
        {
            string strexception = string.Empty;
            DateTime RequestDatetime = DateTime.Now;
            StringBuilder sb = new();
            try
            {
                sb.Append("start-");
                List<Int16> Slotlist = new List<Int16>() { 1, 2, 3, 4, 5, 6, 7 };
                Dictionary<int, (TimeSpan Start, TimeSpan End)> slotTimes = new()
                {
                    { 1, (new TimeSpan(8, 0, 0), new TimeSpan(10, 0, 0)) },   // 8:00 AM - 10:00 AM
                    { 2, (new TimeSpan(10, 0, 0), new TimeSpan(12, 0, 0)) },  // 10:00 AM - 12:00 PM
                    { 3, (new TimeSpan(12, 0, 0), new TimeSpan(14, 0, 0)) },  // 12:00 PM - 2:00 PM
                    { 4, (new TimeSpan(14, 0, 0), new TimeSpan(16, 0, 0)) },  // 2:00 PM - 4:00 PM
                    { 5, (new TimeSpan(16, 0, 0), new TimeSpan(18, 0, 0)) },  // 4:00 PM - 6:00 PM
                    { 6, (new TimeSpan(18, 0, 0), new TimeSpan(20, 0, 0)) },  // 6:00 PM - 8:00 PM
                    { 7, (new TimeSpan(20, 0, 0), new TimeSpan(22, 0, 0)) }   // 8:00 PM - 10:00 PM
                };

                //List<string> FOSRegionList = "FOSRegionList".AppSettings().Split(',').ToList();
                List<FOSRegionModel> FOSRegionList = new List<FOSRegionModel>();
                //string FOSRegionDelhi = "FOSRegionDelhi".AppSettings();
                List<string> FOSRegionListforRank = "FOSRegionListforRank".AppSettings().Split(',').ToList();
                List<string> FOSRegionListforSlotLimit = "FOSRegionListforSlotLimit".AppSettings().Split(',').ToList();
                Dictionary<long,bool>AgentSpokeCitiesList=new Dictionary<long,bool>();

                DataSet ds = FOSAllocationDLL.CityGroupMappingAllocation();
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    FOSRegionList = (from dr in ds.Tables[0].AsEnumerable()
                                     select new FOSRegionModel
                                     {
                                         FOSRegionName = dr["FOSRegionName"]!=null && dr["FOSRegionName"]!= DBNull.Value ? Convert.ToString(dr["FOSRegionName"]):"",
                                         MaxDistance = dr["MaxDistance"]!=null && dr["MaxDistance"] != DBNull.Value ? Convert.ToDecimal(dr["MaxDistance"]):Convert.ToDecimal(0)
                                     }).ToList();

                    if (sameDayAllocation) {
                        var sameDayAllocationRegions = new List<string> { "Term-Delhi", "HEALTH-DELHI" };
                        FOSRegionList = FOSRegionList.Where(region => sameDayAllocationRegions.Contains(region.FOSRegionName)).ToList();

                        var currentTime = DateTime.Now.TimeOfDay;
                        var nextHourTime = DateTime.Now.AddHours(1).TimeOfDay;    
                        // Remove passed slots based on current time
                        // Slotlist.RemoveAll(slotId => slotTimes[slotId].End <= currentTime);

                        // Allocate only for slot next hour
                        Slotlist = Slotlist.Where(slotId => 
                            slotTimes[slotId].Start >= currentTime && 
                            slotTimes[slotId].Start <= nextHourTime
                        ).ToList();
                    }


                    foreach (FOSRegionModel _FOSRegionModel in FOSRegionList)
                    {

                        Dictionary<long, string> ZoneAgentMapping = new();
                        foreach (Int16 SlotID in Slotlist)
                        {
                            DataSet Leadds = FOSAllocationDLL.GetDataForDistanceCalculation(SlotID, _FOSRegionModel.FOSRegionName, sameDayAllocation);

                            Dictionary<long, bool> AgentAllocated = new();
                            Dictionary<long, bool> LeadAllocated = new();
                            List<LeadAgentData> LeadAgentList = new();

                            List<long> AgentsWithNoZones = new();
                            List<long> LeadsWithNoZones = new();

                            if (UseZoneAllocation(_FOSRegionModel)  && ZoneAgentMapping.Count == 0)
                            {
                                foreach (DataRow AgentRow in Leadds.Tables[1].Rows)
                                {
                                    var UserID = (AgentRow["UserID"] != null && AgentRow["UserID"] != DBNull.Value) ? Convert.ToInt64(AgentRow["UserID"]) : Convert.ToInt64(0);
                                    var AgentLAT = (AgentRow["agentlat"] != null && AgentRow["agentlat"] != DBNull.Value) ? Convert.ToDecimal(AgentRow["agentlat"]) : Convert.ToDecimal(0.00);
                                    var AgentLong = (AgentRow["agentlong"] != null && AgentRow["agentlong"] != DBNull.Value) ? Convert.ToDecimal(AgentRow["agentlong"]) : Convert.ToDecimal(0.00);
                                    var AgentZone = GetZonebyLatLong(Convert.ToDouble(AgentLAT), Convert.ToDouble(AgentLong), _FOSRegionModel.FOSRegionName);
                                    ZoneAgentMapping.Add(UserID, AgentZone);
                                    if (AgentZone == "")
                                        AgentsWithNoZones.Add(UserID);
                                }
                            }

                            foreach (DataRow LeadRow in Leadds.Tables[0].Rows)
                            {
                                long LeadID = (LeadRow["LeadID"] != null && LeadRow["LeadID"] != DBNull.Value) ? Convert.ToInt64(LeadRow["LeadID"]) : Convert.ToInt64(0);
                                try
                                {
                                    var LAT = (LeadRow["lat"] != null && LeadRow["lat"] != DBNull.Value) ? Convert.ToDecimal(LeadRow["lat"]) : Convert.ToDecimal(0.00);
                                    var Long = (LeadRow["long"] != null && LeadRow["long"] != DBNull.Value) ? Convert.ToDecimal(LeadRow["long"]) : Convert.ToDecimal(0.00);
                                    string GroupType = (LeadRow["GroupType"] != null && LeadRow["GroupType"] != DBNull.Value) ? Convert.ToString(LeadRow["GroupType"]) : string.Empty;
                                    string Region = (LeadRow["Region"] != null && LeadRow["Region"] != DBNull.Value) ? Convert.ToString(LeadRow["Region"]) : string.Empty;
                                    var SlotId = (LeadRow["SlotId"] != null && LeadRow["SlotId"] != DBNull.Value) ? Convert.ToInt16(LeadRow["SlotId"]) : Convert.ToInt16(0);
                                    var AppointmentDateTime = LeadRow["AppointmentDateTime"] != null && LeadRow["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(LeadRow["AppointmentDateTime"]) : DateTime.MinValue;
                                    var Priority = (LeadRow["Priority"] != null && LeadRow["Priority"] != DBNull.Value) ? Convert.ToInt16(LeadRow["Priority"]) : Convert.ToInt16(1);
                                    var LeadRank = (LeadRow["LeadRank"] != null && LeadRow["LeadRank"] != DBNull.Value) ? Convert.ToInt16(LeadRow["LeadRank"]) : Convert.ToInt16(1);
                                    string LeadZone = "";
                                    if (UseZoneAllocation(_FOSRegionModel))
                                    {
                                        LeadZone = GetZonebyLatLong(Convert.ToDouble(LAT), Convert.ToDouble(Long), _FOSRegionModel.FOSRegionName);
                                        if (LeadZone == "")
                                            LeadsWithNoZones.Add(LeadID);
                                    }

                                    foreach (DataRow AgentRow in Leadds.Tables[1].Rows)
                                    {
                                        var UserID = (AgentRow["UserID"] != null && AgentRow["UserID"] != DBNull.Value) ? Convert.ToInt64(AgentRow["UserID"]) : Convert.ToInt64(0);

                                        if (!UseZoneAllocation(_FOSRegionModel) || LeadZone == "" || (ZoneAgentMapping.ContainsKey(UserID) && CheckZoneEligibility(LeadZone, ZoneAgentMapping[UserID])))
                                        {
                                            var AgentLAT = (AgentRow["agentlat"] != null && AgentRow["agentlat"] != DBNull.Value) ? Convert.ToDecimal(AgentRow["agentlat"]) : Convert.ToDecimal(0.00);
                                            var AgentLong = (AgentRow["agentlong"] != null && AgentRow["agentlong"] != DBNull.Value) ? Convert.ToDecimal(AgentRow["agentlong"]) : Convert.ToDecimal(0.00);
                                            var AgentCurentLAT = (AgentRow["agentcurrentlat"] != null && AgentRow["agentcurrentlat"] != DBNull.Value) ? Convert.ToDecimal(AgentRow["agentcurrentlat"]) : Convert.ToDecimal(0.00);
                                            var AgentCurrentLong = (AgentRow["agentcurrentlong"] != null && AgentRow["agentcurrentlong"] != DBNull.Value) ? Convert.ToDecimal(AgentRow["agentcurrentlong"]) : Convert.ToDecimal(0.00);
                                            var SlotLeadCount = (AgentRow["SlotLeadCount"] != null && AgentRow["SlotLeadCount"] != DBNull.Value) ? Convert.ToInt16(AgentRow["SlotLeadCount"]) : Convert.ToInt16(0);
                                            var FreshAssignSlotLeadCount = (AgentRow["FreshAssignSlotLeadCount"] != null && AgentRow["FreshAssignSlotLeadCount"] != DBNull.Value) ? Convert.ToInt16(AgentRow["FreshAssignSlotLeadCount"]) : Convert.ToInt16(0);
                                            var Gender = (AgentRow["Gender"] != null && AgentRow["Gender"] != DBNull.Value) ? Convert.ToInt16(AgentRow["Gender"]) : Convert.ToInt16(0);
                                            var GroupId = (AgentRow["GroupId"] != null && AgentRow["GroupId"] != DBNull.Value) ? Convert.ToInt16(AgentRow["GroupId"]) : Convert.ToInt16(0);
                                            var Grade = (AgentRow["Grade"] != null && AgentRow["Grade"] != DBNull.Value) ? Convert.ToInt16(AgentRow["Grade"]) : Convert.ToInt16(0);

                                            DistanceInfo odistanceInfo = new DistanceInfo();
                                            odistanceInfo.Distance = DistanceAPICallAsync(AgentLong, AgentLAT, Long, LAT, Region);

                                            if (AgentCurentLAT > 0 && AgentCurrentLong > 0)
                                                odistanceInfo.CurrentLocationDistance = DistanceAPICallAsync(AgentCurrentLong, AgentCurentLAT, Long, LAT, Region);
                                            odistanceInfo.UserID = UserID;
                                            odistanceInfo.LeadID = LeadID;
                                            odistanceInfo.SlotId = SlotId;
                                            odistanceInfo.LeadZone = LeadZone;
                                            FOSAllocationDLL.InsertDistancesToSql(odistanceInfo);

                                            decimal distanceconsidered = odistanceInfo.CurrentLocationDistance > 0 ? odistanceInfo.CurrentLocationDistance : odistanceInfo.Distance;

                                            if (CheckEligibility(SlotLeadCount, distanceconsidered, odistanceInfo.Distance, Gender, SlotId, _FOSRegionModel.FOSRegionName, _FOSRegionModel.MaxDistance, sameDayAllocation, FOSRegionListforSlotLimit) && distanceconsidered != 1000 && odistanceInfo.Distance != 1000)
                                            {
                                                LeadAgentData leadAgentData = new LeadAgentData();
                                                leadAgentData.LeadID = LeadID;
                                                leadAgentData.UserID = UserID;
                                                leadAgentData.GroupId = GroupId;
                                                leadAgentData.Distance = distanceconsidered;
                                                leadAgentData.Priority = Priority;
                                                leadAgentData.LeadRank = LeadRank;
                                                leadAgentData.Grade = Grade;
                                                LeadAgentList.Add(leadAgentData);
                                            }
                                        }

                                    }
                                }
                                catch (Exception ex)
                                {
                                    LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "FOSLeadAllocation_LeadError", "Allocation", "FOSLeadAllocationScheduler", string.Empty, string.Empty, RequestDatetime, DateTime.Now);
                                }
                            }

                            var SortedLeadAgentList = LeadAgentList.OrderBy(x => x.Priority).ThenBy(x => x.Grade).ThenBy(x => x.Distance).ToList();

                            foreach (var LeadAgentData in SortedLeadAgentList)
                            {
                                if (!AgentAllocated.ContainsKey(LeadAgentData.UserID) && !LeadAllocated.ContainsKey(LeadAgentData.LeadID) 
                                    && ((!_FOSRegionModel.FOSRegionName.ToLower().Contains("spoke") && !AgentSpokeCitiesList.ContainsKey(LeadAgentData.UserID)) || _FOSRegionModel.FOSRegionName.ToLower().Contains("spoke")))
                                 
                                {
                                    LeadDetails _LeadDetails = new LeadDetails();
                                    _LeadDetails.AssigntoUserID = LeadAgentData.UserID;
                                    _LeadDetails.AssignbyUserID = 124;
                                    _LeadDetails.ProductID = 2;
                                    _LeadDetails.LeadId = LeadAgentData.LeadID;
                                    _LeadDetails.GroupID = LeadAgentData.GroupId;
                                    _LeadDetails.AllocationTrackingEntryFlag = 1;
                                    _LeadDetails.JobID = 58;
                                    _LeadDetails.InsurerID = 0;
                                    _LeadDetails.SelectionCount = 0;
                                    _LeadDetails.LeadRank = 0;

                                    if(_FOSRegionModel.FOSRegionName.ToLower().Contains("spoke") && !AgentSpokeCitiesList.ContainsKey(LeadAgentData.UserID))
                                    {
                                        AgentSpokeCitiesList.Add(LeadAgentData.UserID, true);
                                    }
                                    AllocationDLL.ReAssignLead(_LeadDetails);
                                    AllocationDLL.UpdateAgentAllocationCounter(_LeadDetails.LeadId, _LeadDetails.AssigntoUserID, 2, 0, null);
                                    AgentAllocated.Add(LeadAgentData.UserID, true);
                                    LeadAllocated.Add(LeadAgentData.LeadID, true);
                                }
                            }
                            if (UseZoneAllocation(_FOSRegionModel))
                            {
                                if(AgentsWithNoZones.Count>0) LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "FOSLeadAllocation_AgentWithNoZone", "Allocation", "FOSLeadAllocationScheduler", _FOSRegionModel.FOSRegionName, JsonConvert.SerializeObject(AgentsWithNoZones), RequestDatetime, DateTime.Now);
                                if (LeadsWithNoZones.Count > 0)  LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "FOSLeadAllocation_LeadWithNoZone", "Allocation", "FOSLeadAllocationScheduler", _FOSRegionModel.FOSRegionName, JsonConvert.SerializeObject(LeadsWithNoZones), RequestDatetime, DateTime.Now);

                            }
                            LogAgentData(Leadds.Tables[1].Rows, SlotID, AgentAllocated);

                        }

                    }
                    sb.Append("end-");
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, strexception, "FOSLeadAllocation", "Allocation", "FOSLeadAllocationScheduler", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
            }
        }
        public static bool CheckEligibility(Int16 SlotLeadCount, decimal distanceconsidered, decimal baseDistance, Int16 Gender, Int16 SlotId, string FOSRegionName,decimal MaxDistance, bool sameDayAllocation,List<string> FOSRegionListforSlotLimit)
        {
            short MaxLeadPerSlot = 2;
            if(FOSRegionListforSlotLimit!=null && FOSRegionListforSlotLimit.Any() && FOSRegionListforSlotLimit.Contains(FOSRegionName))
            {
                MaxLeadPerSlot = 3;
            }
            if (sameDayAllocation) {
                // Force allocation
                MaxLeadPerSlot = 4;
                MaxDistance = MaxDistance < 200 ? 200 : MaxDistance;
            }
            return SlotLeadCount < MaxLeadPerSlot && CheckDistanceEligibility(distanceconsidered, baseDistance, FOSRegionName, MaxDistance) && ((EnumGender)Gender != EnumGender.Female || ((EnumGender)Gender == EnumGender.Female && SlotId != 7));
        }
        public static bool CheckDistanceEligibility(decimal distanceconsidered, decimal baseDistance, string FOSRegionName, decimal MaxDistance)
        {
            ////return distanceconsidered <= 15 && baseDistance <= 15;
            //if (FOSRegionName == "HEALTH-MUMBAI")
            //    return baseDistance <= 7;
            //else if (FOSRegionName.StartsWith("SAVING"))
            //    return baseDistance <= 100;
            //else if (FOSRegionName.StartsWith("HEALTH-RENEWAL"))
            //    return baseDistance <= 50;
            //else
            //    return baseDistance <= 15;

            return baseDistance <= MaxDistance;
        }
        public static string GetZonebyLatLong(double Lat, double Long, string fOSRegionName)
        {
            Coordinate pointCoordinate = new(Lat, Long);
            string res = "";
            var AllZones = AllocationDLL.GetAllZonesFromMongo();

            if (existingZones == null || existingZones.Count == 0 || existingZones[0].FOSRegionName != fOSRegionName)
            {
                existingZones = AllZones.Where(zone => zone.FOSRegionName == fOSRegionName).ToList();
            }

            existingZones.ForEach((CityZone zone) =>
            {
                var result = zone.Coordinates.ContainsPoint(pointCoordinate);
                if (result)
                {
                    res = zone.ZoneId;
                }
            });
            return res;
        }

        public static bool CheckZoneEligibility(string LeadZone, string AgentZone)
        {
            return (LeadZone == "" || (LeadZone == AgentZone));
        }

        public static void LogAgentData(DataRowCollection AgentRows, short SlotID, Dictionary<long, bool> agentAllocated)
        {
            foreach (DataRow AgentRow in AgentRows)
            {
                try
                {
                    var UserID = (AgentRow["UserID"] != null && AgentRow["UserID"] != DBNull.Value) ? Convert.ToInt64(AgentRow["UserID"]) : Convert.ToInt64(0);
                    var SlotLeadCount = (AgentRow["SlotLeadCount"] != null && AgentRow["SlotLeadCount"] != DBNull.Value) ? Convert.ToInt16(AgentRow["SlotLeadCount"]) : Convert.ToInt16(0);
                    var FreshAssignSlotLeadCount = (AgentRow["FreshAssignSlotLeadCount"] != null && AgentRow["FreshAssignSlotLeadCount"] != DBNull.Value) ? Convert.ToInt16(AgentRow["FreshAssignSlotLeadCount"]) : Convert.ToInt16(0);
                    if (agentAllocated.ContainsKey(UserID))
                    {
                        SlotLeadCount = (short)(SlotLeadCount + 1);
                        FreshAssignSlotLeadCount = 1;
                    }
                    FOSAllocationDLL.LogAgentData(UserID, SlotLeadCount, FreshAssignSlotLeadCount, SlotID);
                }
                catch (Exception ex)
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "LogAgentData", "Allocation", "FOSLeadAllocationScheduler", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                }
            }
        }

        private bool UseZoneAllocation(FOSRegionModel _FOSRegionModel)
        {
            string ZoneAllocationRegionsStr = "ZoneAllocationRegions".AppSettings();

            if (String.IsNullOrWhiteSpace(ZoneAllocationRegionsStr)) return false;

            List<string> ZoneAllocationRegions = ZoneAllocationRegionsStr.Split(',').ToList();

            if (ZoneAllocationRegions.Contains(_FOSRegionModel.FOSRegionName))
            {
                return true;
            }
            else return false;
        }

    }
}

